/* Circuit Simulator Styles */

body {
    margin: 0;
    padding: 0;
    background: #f5f5f5;
    overflow: hidden;
}

/* Simulator Navigation */
.simulator-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: #2c3e50;
    color: white;
    z-index: 1000;
    padding: 10px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.simulator-nav .nav-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.simulator-nav h1 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.simulator-nav .back-btn {
    color: #4ecdc4;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.simulator-nav .back-btn:hover {
    background: rgba(78, 205, 196, 0.1);
}

.simulator-controls {
    display: flex;
    gap: 10px;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* Main Simulator Layout */
.simulator-main {
    display: grid;
    grid-template-columns: 250px 1fr 300px;
    height: 100vh;
    margin-top: 60px;
}

/* Component Palette */
.component-palette {
    background: white;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    padding: 20px;
}

.component-palette h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
}

.component-category {
    margin-bottom: 25px;
}

.component-category h4 {
    margin: 0 0 10px 0;
    color: #667eea;
    font-size: 1rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.component-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: grab;
    transition: all 0.3s ease;
    text-align: center;
}

.component-item:hover {
    background: #e9ecef;
    border-color: #667eea;
    transform: translateY(-2px);
}

.component-item.dragging {
    opacity: 0.5;
    cursor: grabbing;
}

.component-item span {
    font-size: 0.8rem;
    color: #666;
    margin-top: 5px;
}

/* Component Icons */
.component-icon {
    width: 30px;
    height: 30px;
    position: relative;
    margin-bottom: 5px;
}

.battery-icon {
    background: linear-gradient(90deg, #ff6b6b 50%, #333 50%);
    border-radius: 3px;
}

.resistor-icon {
    background: #f4e4bc;
    border-radius: 3px;
    position: relative;
}

.resistor-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: repeating-linear-gradient(90deg, #8B4513 0px, #8B4513 3px, transparent 3px, transparent 6px);
}

.capacitor-icon {
    background: #ddd;
    border-radius: 3px;
    position: relative;
}

.capacitor-icon::before,
.capacitor-icon::after {
    content: '';
    position: absolute;
    top: 20%;
    bottom: 20%;
    width: 3px;
    background: #333;
}

.capacitor-icon::before {
    left: 30%;
}

.capacitor-icon::after {
    right: 30%;
}

.led-icon {
    background: #ff6b6b;
    border-radius: 50%;
    position: relative;
}

.led-icon::after {
    content: '→';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}

.diode-icon {
    background: #ddd;
    border-radius: 3px;
    position: relative;
}

.diode-icon::before {
    content: '▶|';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-size: 12px;
}

.transistor-icon {
    background: #4ecdc4;
    border-radius: 3px;
    position: relative;
}

.transistor-icon::before {
    content: 'T';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.voltmeter-icon,
.ammeter-icon,
.oscilloscope-icon {
    background: #333;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.voltmeter-icon::before {
    content: 'V';
}

.ammeter-icon::before {
    content: 'A';
}

.oscilloscope-icon::before {
    content: '~';
}

/* Circuit Workspace */
.circuit-workspace {
    display: flex;
    flex-direction: column;
    background: white;
}

.workspace-toolbar {
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 10px 20px;
    display: flex;
    gap: 20px;
    align-items: center;
}

.tool-group {
    display: flex;
    gap: 5px;
    padding: 0 10px;
    border-right: 1px solid #ddd;
}

.tool-group:last-child {
    border-right: none;
}

.tool-btn {
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #666;
}

.tool-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
    color: #667eea;
}

.tool-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.tool-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.simulation-btn {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
}

.simulation-btn:hover:not(:disabled) {
    background: #45b7aa;
}

.circuit-canvas {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: white;
    cursor: default;
}

.circuit-canvas.tool-wire {
    cursor: crosshair;
}

.circuit-canvas.tool-delete {
    cursor: not-allowed;
}

.circuit-svg {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
}

.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image: 
        linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Welcome Message */
.welcome-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
    z-index: 1;
}

.welcome-content {
    background: rgba(255, 255, 255, 0.9);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.welcome-content i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.welcome-content h3 {
    margin-bottom: 15px;
    color: #333;
}

.welcome-content p {
    margin-bottom: 20px;
    line-height: 1.6;
}

.quick-start {
    text-align: left;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.quick-start h4 {
    margin-bottom: 10px;
    color: #667eea;
}

.quick-start ol {
    margin: 0;
    padding-left: 20px;
}

.quick-start li {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

/* Circuit Components */
.circuit-component {
    position: absolute;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.circuit-component:hover {
    transform: scale(1.1);
}

.circuit-component.selected {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.circuit-component.animated {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Component Symbols */
.battery-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.battery-symbol .positive,
.battery-symbol .negative {
    width: 8px;
    height: 30px;
    border-radius: 2px;
}

.battery-symbol .positive {
    background: #ff6b6b;
    height: 40px;
}

.battery-symbol .negative {
    background: #333;
}

.resistor-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.resistor-symbol .zigzag {
    width: 40px;
    height: 15px;
    background: #f4e4bc;
    border-radius: 3px;
    position: relative;
}

.resistor-symbol .zigzag::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 2px;
    background: repeating-linear-gradient(90deg, #8B4513 0px, #8B4513 3px, transparent 3px, transparent 6px);
}

.capacitor-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.capacitor-symbol .plate1,
.capacitor-symbol .plate2 {
    width: 3px;
    height: 30px;
    background: #333;
    border-radius: 1px;
}

.led-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.led-symbol .led-body {
    width: 25px;
    height: 25px;
    background: #ff6b6b;
    border-radius: 50%;
}

.led-symbol .led-arrow {
    position: absolute;
    top: 5px;
    right: 5px;
    color: #feca57;
    font-size: 12px;
}

.led-symbol .led-arrow::before {
    content: '→';
}

.diode-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.diode-symbol .triangle {
    width: 0;
    height: 0;
    border-left: 15px solid #333;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.diode-symbol .line {
    width: 3px;
    height: 20px;
    background: #333;
    margin-left: 2px;
}

.transistor-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.transistor-symbol .base {
    width: 3px;
    height: 30px;
    background: #333;
    position: absolute;
    left: 15px;
}

.transistor-symbol .collector,
.transistor-symbol .emitter {
    width: 20px;
    height: 2px;
    background: #333;
    position: absolute;
    right: 10px;
}

.transistor-symbol .collector {
    top: 15px;
}

.transistor-symbol .emitter {
    bottom: 15px;
}

.meter-symbol {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.meter-symbol .meter-face {
    width: 35px;
    height: 35px;
    background: #333;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

/* Properties Panel */
.properties-panel {
    background: white;
    border-left: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
}

.properties-panel h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.2rem;
}

.property-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.property-section:last-child {
    border-bottom: none;
}

.property-section h4 {
    margin: 0 0 15px 0;
    color: #667eea;
    font-size: 1rem;
}

.no-selection {
    text-align: center;
    color: #999;
    padding: 20px;
}

.no-selection i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.property-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.property-field {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.property-field label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.property-field input,
.property-field select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.property-field input:focus,
.property-field select:focus {
    outline: none;
    border-color: #667eea;
}

.status-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-label {
    font-weight: 600;
    color: #666;
    font-size: 0.9rem;
}

.status-value {
    color: #333;
    font-size: 0.9rem;
}

.measurements-display,
.analysis-display {
    color: #666;
    font-size: 0.9rem;
}

.no-measurements,
.no-analysis {
    text-align: center;
    color: #999;
    font-style: italic;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Tutorial Overlay */
.tutorial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.tutorial-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    max-width: 400px;
}

.tutorial-content h3 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.tutorial-content p {
    margin-bottom: 30px;
    color: #666;
    line-height: 1.6;
}

.tutorial-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .simulator-main {
        grid-template-columns: 200px 1fr 250px;
    }
    
    .component-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .simulator-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .component-palette,
    .properties-panel {
        height: 200px;
        overflow-y: auto;
    }
    
    .simulator-nav .nav-container {
        flex-direction: column;
        gap: 10px;
    }
    
    .workspace-toolbar {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .tool-group {
        border-right: none;
        padding: 0;
    }
}
