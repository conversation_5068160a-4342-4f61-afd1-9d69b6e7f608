// Bilingual Support System
// نظام دعم اللغتين

class BilingualManager {
    constructor() {
        this.currentLanguage = 'en';
        this.isRTL = false;
        this.translations = {};
        this.init();
    }

    init() {
        // Load saved language preference
        this.loadLanguagePreference();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Apply initial language
        this.applyLanguage(this.currentLanguage);
        
        // Load translations
        this.loadTranslations();
    }

    loadLanguagePreference() {
        const savedLang = localStorage.getItem('preferredLanguage');
        if (savedLang && ['en', 'ar'].includes(savedLang)) {
            this.currentLanguage = savedLang;
        } else {
            // Auto-detect browser language
            const browserLang = navigator.language || navigator.userLanguage;
            if (browserLang.startsWith('ar')) {
                this.currentLanguage = 'ar';
            }
        }
    }

    setupEventListeners() {
        // Language toggle buttons
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const lang = e.target.dataset.lang;
                this.switchLanguage(lang);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                this.toggleLanguage();
            }
        });
    }

    switchLanguage(language) {
        if (language === this.currentLanguage) return;
        
        // Add transition effect
        document.body.classList.add('language-loading');
        
        setTimeout(() => {
            this.currentLanguage = language;
            this.applyLanguage(language);
            this.saveLanguagePreference();
            
            // Remove loading effect
            document.body.classList.remove('language-loading');
            
            // Trigger custom event
            this.dispatchLanguageChangeEvent();
        }, 150);
    }

    toggleLanguage() {
        const newLang = this.currentLanguage === 'en' ? 'ar' : 'en';
        this.switchLanguage(newLang);
    }

    applyLanguage(language) {
        this.isRTL = language === 'ar';
        
        // Update HTML attributes
        document.documentElement.lang = language;
        document.documentElement.dir = this.isRTL ? 'rtl' : 'ltr';
        
        // Update language toggle buttons
        this.updateLanguageButtons();
        
        // Update content
        this.updateContent();
        
        // Update page title
        this.updatePageTitle();
        
        // Update meta tags
        this.updateMetaTags();
        
        // Apply RTL/LTR specific styles
        this.applyDirectionStyles();
    }

    updateLanguageButtons() {
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === this.currentLanguage);
        });
    }

    updateContent() {
        // Update elements with data attributes
        const elements = document.querySelectorAll('[data-en][data-ar]');
        elements.forEach(element => {
            const text = element.dataset[this.currentLanguage];
            if (text) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.placeholder = text;
                } else {
                    element.textContent = text;
                }
            }
        });

        // Update elements with title attributes
        const titleElements = document.querySelectorAll('[data-title-en][data-title-ar]');
        titleElements.forEach(element => {
            const title = element.dataset[`title${this.currentLanguage === 'en' ? 'En' : 'Ar'}`];
            if (title) {
                element.title = title;
            }
        });
    }

    updatePageTitle() {
        const titles = {
            en: 'Virtual Electronics Lab - Interactive Learning Platform',
            ar: 'معمل الإلكترونيات الافتراضي - منصة التعلم التفاعلي'
        };
        document.title = titles[this.currentLanguage];
    }

    updateMetaTags() {
        const descriptions = {
            en: 'Comprehensive virtual electronics laboratory for biomedical engineering students with interactive simulations and bilingual support',
            ar: 'مختبر إلكترونيات افتراضي شامل لطلاب الهندسة الطبية الحيوية مع محاكيات تفاعلية ودعم ثنائي اللغة'
        };

        const keywords = {
            en: 'electronics, biomedical, engineering, virtual lab, simulation, education, interactive learning',
            ar: 'إلكترونيات، طبية حيوية، هندسة، مختبر افتراضي، محاكاة، تعليم، تعلم تفاعلي'
        };

        // Update description
        let metaDesc = document.querySelector('meta[name="description"]');
        if (metaDesc) {
            metaDesc.content = descriptions[this.currentLanguage];
        }

        // Update keywords
        let metaKeywords = document.querySelector('meta[name="keywords"]');
        if (metaKeywords) {
            metaKeywords.content = keywords[this.currentLanguage];
        }
    }

    applyDirectionStyles() {
        // Add/remove RTL specific classes
        document.body.classList.toggle('rtl-layout', this.isRTL);
        document.body.classList.toggle('ltr-layout', !this.isRTL);

        // Update navigation icons for RTL
        if (this.isRTL) {
            this.updateIconsForRTL();
        } else {
            this.resetIconsForLTR();
        }
    }

    updateIconsForRTL() {
        // Flip chevron icons
        const chevronRightIcons = document.querySelectorAll('.fa-chevron-right');
        chevronRightIcons.forEach(icon => {
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-left');
        });

        // Flip arrow icons
        const arrowRightIcons = document.querySelectorAll('.fa-arrow-right');
        arrowRightIcons.forEach(icon => {
            icon.classList.remove('fa-arrow-right');
            icon.classList.add('fa-arrow-left');
        });
    }

    resetIconsForLTR() {
        // Reset chevron icons
        const chevronLeftIcons = document.querySelectorAll('.fa-chevron-left');
        chevronLeftIcons.forEach(icon => {
            icon.classList.remove('fa-chevron-left');
            icon.classList.add('fa-chevron-right');
        });

        // Reset arrow icons
        const arrowLeftIcons = document.querySelectorAll('.fa-arrow-left');
        arrowLeftIcons.forEach(icon => {
            icon.classList.remove('fa-arrow-left');
            icon.classList.add('fa-arrow-right');
        });
    }

    saveLanguagePreference() {
        localStorage.setItem('preferredLanguage', this.currentLanguage);
    }

    dispatchLanguageChangeEvent() {
        const event = new CustomEvent('languageChanged', {
            detail: {
                language: this.currentLanguage,
                isRTL: this.isRTL
            }
        });
        document.dispatchEvent(event);
    }

    loadTranslations() {
        // Extended translations for dynamic content
        this.translations = {
            en: {
                loading: 'Loading...',
                error: 'Error occurred',
                success: 'Success',
                confirm: 'Confirm',
                cancel: 'Cancel',
                close: 'Close',
                save: 'Save',
                edit: 'Edit',
                delete: 'Delete',
                search: 'Search...',
                noResults: 'No results found',
                tryAgain: 'Try again',
                welcome: 'Welcome',
                getStarted: 'Get Started',
                learnMore: 'Learn More',
                viewAll: 'View All',
                readMore: 'Read More',
                showLess: 'Show Less',
                previous: 'Previous',
                next: 'Next',
                finish: 'Finish',
                skip: 'Skip',
                retry: 'Retry'
            },
            ar: {
                loading: 'جاري التحميل...',
                error: 'حدث خطأ',
                success: 'نجح',
                confirm: 'تأكيد',
                cancel: 'إلغاء',
                close: 'إغلاق',
                save: 'حفظ',
                edit: 'تعديل',
                delete: 'حذف',
                search: 'بحث...',
                noResults: 'لم يتم العثور على نتائج',
                tryAgain: 'حاول مرة أخرى',
                welcome: 'مرحباً',
                getStarted: 'ابدأ الآن',
                learnMore: 'اعرف المزيد',
                viewAll: 'عرض الكل',
                readMore: 'اقرأ المزيد',
                showLess: 'عرض أقل',
                previous: 'السابق',
                next: 'التالي',
                finish: 'إنهاء',
                skip: 'تخطي',
                retry: 'إعادة المحاولة'
            }
        };
    }

    // Get translation for a key
    t(key) {
        return this.translations[this.currentLanguage][key] || key;
    }

    // Format numbers based on language
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Convert to Arabic-Indic numerals
            const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return number.toString().replace(/\d/g, (digit) => arabicNumerals[digit]);
        }
        return number.toString();
    }

    // Format dates based on language
    formatDate(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        if (this.currentLanguage === 'ar') {
            return new Intl.DateTimeFormat('ar-SA', options).format(date);
        }
        return new Intl.DateTimeFormat('en-US', options).format(date);
    }

    // Get current language info
    getLanguageInfo() {
        return {
            code: this.currentLanguage,
            isRTL: this.isRTL,
            name: this.currentLanguage === 'en' ? 'English' : 'العربية'
        };
    }

    // Add dynamic content with bilingual support
    addBilingualContent(element, enText, arText) {
        element.dataset.en = enText;
        element.dataset.ar = arText;
        element.textContent = this.currentLanguage === 'en' ? enText : arText;
    }

    // Update specific element content
    updateElementContent(selector, enText, arText) {
        const element = document.querySelector(selector);
        if (element) {
            this.addBilingualContent(element, enText, arText);
        }
    }

    // Batch update multiple elements
    batchUpdateContent(updates) {
        updates.forEach(update => {
            this.updateElementContent(update.selector, update.en, update.ar);
        });
    }
}

// Initialize bilingual manager
const bilingualManager = new BilingualManager();

// Export for global access
window.BilingualManager = BilingualManager;
window.bilingual = bilingualManager;

// Listen for language changes
document.addEventListener('languageChanged', (e) => {
    console.log(`Language changed to: ${e.detail.language} (RTL: ${e.detail.isRTL})`);
    
    // Trigger any additional language-specific functionality
    if (typeof window.onLanguageChange === 'function') {
        window.onLanguageChange(e.detail);
    }
});

// Utility functions for easy access
window.t = (key) => bilingualManager.t(key);
window.getCurrentLanguage = () => bilingualManager.currentLanguage;
window.isRTL = () => bilingualManager.isRTL;
window.switchLanguage = (lang) => bilingualManager.switchLanguage(lang);

// Auto-update content when DOM changes
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Update any new bilingual elements
                    const bilingualElements = node.querySelectorAll('[data-en][data-ar]');
                    if (bilingualElements.length > 0) {
                        bilingualManager.updateContent();
                    }
                }
            });
        }
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true
});

console.log('🌐 Bilingual system initialized successfully!');
