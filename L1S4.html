<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دائرة الإنذار ذاتي الإغلاق</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            color: #334155;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden; /* Prevent scrollbars from showing during transitions */
        }
        .slide-container {
            position: relative;
            width: 100%;
            max-width: 960px; /* Standard presentation width */
            height: 600px; /* Fixed height for slides */
            background-color: #ffffff;
            border-radius: 1.5rem; /* Rounded corners */
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden; /* Hide overflowing content during transitions */
            display: flex;
            flex-direction: column;
        }
        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            padding: 2.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: translateX(100%);
            transition: opacity 0.7s ease-out, transform 0.7s ease-out;
            background-color: #ffffff;
            box-sizing: border-box;
        }
        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }
        .slide.previous {
            transform: translateX(-100%);
        }
        .slide-content {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e3a8a; /* Dark blue */
            margin-bottom: 1.5rem;
        }
        .slide-subtitle {
            font-size: 1.5rem;
            font-weight: 600;
            color: #3b82f6; /* Medium blue */
            margin-bottom: 1rem;
        }
        .slide-text {
            font-size: 1.125rem;
            line-height: 1.75;
            text-align: justify;
            max-width: 80%;
            margin-bottom: 1rem;
        }
        .list-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 0.75rem;
            text-align: right;
            width: 100%;
            max-width: 80%;
        }
        .list-item svg {
            flex-shrink: 0;
            margin-left: 0.75rem;
            margin-top: 0.25rem;
            color: #10b981; /* Green */
        }
        .navigation-buttons {
            position: absolute;
            bottom: 1.5rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 10;
        }
        .nav-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 9999px; /* Fully rounded */
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .nav-button:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
        }
        .nav-button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
            box-shadow: none;
        }

        /* Specific styles for diagrams and icons */
        .diagram-container {
            width: 100%;
            max-width: 90%;
            height: auto;
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .diagram-container svg {
            width: 100%;
            height: auto;
        }
        .circuit-diagram {
            width: 100%;
            height: auto;
            max-width: 90%;
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .circuit-diagram svg {
            width: 100%;
            height: auto;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1rem;
            background-color: #f8fafc;
        }

        /* Animation for icons */
        @keyframes bounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-5px);
            }
        }
        .animated-icon {
            animation: bounce 1.5s infinite ease-in-out;
        }

        /* Specific styles for text alignment in lists */
        .text-right-list {
            text-align: right;
            width: 100%;
        }
        .text-right-list li {
            margin-bottom: 0.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .slide-container {
                height: auto;
                min-height: 100vh;
                border-radius: 0;
                box-shadow: none;
            }
            .slide {
                padding: 1.5rem;
                position: relative; /* Allow content to flow */
                height: auto;
                min-height: 100vh;
            }
            .slide-title {
                font-size: 2rem;
            }
            .slide-subtitle {
                font-size: 1.25rem;
            }
            .slide-text, .list-item {
                font-size: 1rem;
                max-width: 100%;
            }
            .list-item {
                flex-direction: row-reverse; /* For RTL, icon on right */
                text-align: right;
            }
            .list-item svg {
                margin-right: 0.75rem;
                margin-left: 0;
            }
            .navigation-buttons {
                bottom: 1rem;
                gap: 0.5rem;
            }
            .nav-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="slide active" id="slide-1">
            <div class="slide-content">
                <h1 class="slide-title">أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</h1>
                <p class="slide-subtitle">د. محمد يعقوب إسماعيل</p>
                <p class="slide-text">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
                <p class="slide-text mt-4 text-gray-600">المستوى الأول الحلقة 4: دائرة "إغلاق ذاتي للإنذار" (Self-Latching Alarm Circuit)</p>
            </div>
        </div>

        <div class="slide" id="slide-2">
            <div class="slide-content">
                <h2 class="slide-title">مقدمة ومفاهيم أساسية</h2>
                <div class="flex items-center justify-center gap-4 mb-4">
                    <svg class="w-12 h-12 text-blue-500 animated-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.001 12.001 0 002.944 12c.047 1.402.164 2.80.364 4.132m.765 3.611A11.917 11.917 0 0012 21.055a11.917 11.917 0 008.871-3.208m-.765-3.611c.068-.49.12-1.0.158-1.5H21a1 1 0 001-1v-2a1 1 0 00-1-1h-.422c-.038-.5-.09-1.01-.158-1.5M12 12v2m-2 4h4"></path></svg>
                    <p class="slide-text">دائرة حيوية للعديد من الأجهزة الطبية، خاصة تلك المتعلقة بالسلامة والمراقبة.</p>
                </div>
                <p class="slide-text">الفكرة الأساسية: بمجرد تفعيل الإنذار بسبب حدوث حالة معينة، يظل الإنذار فعالاً حتى لو زالت الحالة المسببة له. ولا يتم إيقاف الإنذار إلا بتدخل يدوي (زر إعادة تعيين).</p>
                <p class="slide-subtitle mt-4">المفاهيم الأساسية التي سنغطيها:</p>
                <ul class="text-right-list pr-8">
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>مبدأ الإغلاق الذاتي (Latching).</span>
                    </li>
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>استخدام الثايرستور (SCR) كعنصر إغلاق ذاتي.</span>
                    </li>
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>تصميم دائرة إعادة تعيين (Reset) للإنذار.</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-3">
            <div class="slide-content">
                <h2 class="slide-title">أهمية الدائرة في الأجهزة الطبية</h2>
                <div class="flex flex-col gap-4 w-full max-w-md">
                    <div class="flex items-center gap-4 bg-blue-50 p-4 rounded-xl shadow-md">
                        <svg class="w-10 h-10 text-blue-600 animated-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6.912 2.912A15.003 15.003 0 0112 21c-3.111 0-5.83-.997-8.125-2.665M2.28 9c-.38-.92-.566-1.92-.566-2.954C1.714 3.79 4.419 1 7.705 1c1.685 0 3.326.602 4.695 1.76l.66.565m-.706 7.006c-.38-.92-.566-1.92-.566-2.954C11.714 3.79 14.419 1 17.705 1c1.685 0 3.326.602 4.695 1.76l.66.565M12 12a2 2 0 100-4 2 2 0 000 4z"></path></svg>
                        <p class="text-lg text-gray-700 text-right">ضمان الانتباه للحالات الحرجة، حتى لو كانت المشكلة مؤقتة.</p>
                    </div>
                    <div class="flex items-center gap-4 bg-green-50 p-4 rounded-xl shadow-md">
                        <svg class="w-10 h-10 text-green-600 animated-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>
                        <p class="text-lg text-gray-700 text-right">منع التجاهل التلقائي للإنذارات التي قد "تصلح نفسها".</p>
                    </div>
                    <div class="flex items-center gap-4 bg-red-50 p-4 rounded-xl shadow-md">
                        <svg class="w-10 h-10 text-red-600 animated-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <p class="text-lg text-gray-700 text-right">السلامة أولاً: لا يمكن التهاون مع أي إنذار يتعلق بسلامة المريض.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-4">
            <div class="slide-content">
                <h2 class="slide-title">النظرية الرئيسية: استخدام الثايرستور (SCR)</h2>
                <div class="flex flex-col md:flex-row items-center gap-8 w-full">
                    <div class="flex-1 text-right">
                        <p class="slide-text">الـ SCR هو مكون شبه موصل رباعي الطبقات (PNPN) له ثلاثة أطراف: المصعد (Anode - A)، المهبط (Cathode - K)، والبوابة (Gate - G).</p>
                        <p class="slide-subtitle mt-4">آلية العمل:</p>
                        <ul class="text-right-list pr-8">
                            <li class="list-item">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.001 12.001 0 002.944 12c.047 1.402.164 2.80.364 4.132m.765 3.611A11.917 11.917 0 0012 21.055a11.917 11.917 0 008.871-3.208m-.765-3.611c.068-.49.12-1.0.158-1.5H21a1 1 0 001-1v-2a1 1 0 00-1-1h-.422c-.038-.5-.09-1.01-.158-1.5M12 12v2m-2 4h4"></path></svg>
                                <span><b>القطع (OFF):</b> بدون نبضة على البوابة، لا يمر تيار.</span>
                            </li>
                            <li class="list-item">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.001 12.001 0 002.944 12c.047 1.402.164 2.80.364 4.132m.765 3.611A11.917 11.917 0 0012 21.055a11.917 11.917 0 008.871-3.208m-.765-3.611c.068-.49.12-1.0.158-1.5H21a1 1 0 001-1v-2a1 1 0 00-1-1h-.422c-.038-.5-.09-1.01-.158-1.5M12 12v2m-2 4h4"></path></svg>
                                <span><b>التوصيل (ON):</b> نبضة صغيرة على البوابة تجعله يوصل.</span>
                            </li>
                            <li class="list-item">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                                <span><b>الإغلاق الذاتي:</b> يظل موصلاً حتى لو أزيلت نبضة البوابة، طالما التيار أعلى من <b>تيار الإمساك (Ih)</b>.</span>
                            </li>
                            <li class="list-item">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                                <span><b>إعادة التعيين:</b> لقطع التوصيل، يجب أن ينخفض التيار عبره إلى ما دون Ih (عادة بقطع التغذية مؤقتاً).</span>
                            </li>
                        </ul>
                    </div>
                    <div class="flex-1 flex justify-center">
                        <svg class="w-48 h-48" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <title>رمز الثايرستور (SCR)</title>
                            <line x1="50" y1="10" x2="50" y2="30" stroke="#3b82f6" stroke-width="3"/>
                            <text x="55" y="20" fill="#3b82f6" font-size="10" text-anchor="start">A</text>

                            <polygon points="50,30 25,70 75,70" fill="#bfdbfe" stroke="#3b82f6" stroke-width="3"/>

                            <line x1="75" y1="50" x2="90" y2="50" stroke="#10b981" stroke-width="3"/>
                            <text x="95" y="55" fill="#10b981" font-size="10" text-anchor="start">G</text>

                            <line x1="50" y1="70" x2="50" y2="90" stroke="#ef4444" stroke-width="3"/>
                            <line x1="40" y1="70" x2="60" y2="70" stroke="#ef4444" stroke-width="3"/>
                            <text x="55" y="80" fill="#ef4444" font-size="10" text-anchor="start">K</text>
                            <animateTransform
                                attributeName="transform"
                                attributeType="XML"
                                type="translate"
                                from="0 0"
                                to="0 -5"
                                dur="1.5s"
                                repeatCount="indefinite"
                                additive="sum"
                            />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-5">
            <div class="slide-content">
                <h2 class="slide-title">المخطط الصندوقي لدائرة الإنذار ذاتي الإغلاق</h2>
                <div class="diagram-container">
                    <svg viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                        <style>
                            .box { fill: #e0f2fe; stroke: #3b82f6; stroke-width: 2; border-radius: 8px; }
                            .arrow { stroke: #1e3a8a; stroke-width: 2; marker-end: url(#arrowhead); }
                            .text { font-family: 'Inter', sans-serif; font-size: 16px; fill: #334155; text-anchor: middle; }
                            .label { font-family: 'Inter', sans-serif; font-size: 14px; fill: #475569; text-anchor: middle; }
                            .flow-animation {
                                animation: flow 2s linear infinite;
                            }
                            @keyframes flow {
                                0% { stroke-dashoffset: 0; }
                                100% { stroke-dashoffset: -20; }
                            }
                        </style>
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#1e3a8a" />
                            </marker>
                        </defs>

                        <rect x="50" y="150" width="150" height="70" rx="8" ry="8" class="box" />
                        <text x="125" y="195" class="text">مدخل من الحساس</text>
                        <text x="125" y="215" class="label">(تجاوز حد معين)</text>

                        <rect x="325" y="150" width="150" height="70" rx="8" ry="8" class="box" />
                        <text x="400" y="185" class="text">دائرة الإغلاق الذاتي</text>
                        <text x="400" y="205" class="label">(Latch)</text>

                        <rect x="600" y="150" width="150" height="70" rx="8" ry="8" class="box" />
                        <text x="675" y="185" class="text">خرج الإنذار</text>
                        <text x="675" y="205" class="label">(LED, Buzzer)</text>

                        <rect x="325" y="270" width="150" height="70" rx="8" ry="8" class="box" />
                        <text x="400" y="305" class="text">مفتاح إعادة التعيين</text>
                        <text x="400" y="325" class="label">(Reset)</text>

                        <rect x="50" y="30" width="150" height="50" rx="8" ry="8" class="box" />
                        <text x="125" y="60" class="text">مصدر تغذية</text>

                        <line x1="200" y1="185" x2="325" y2="185" class="arrow" stroke-dasharray="5,5">
                            <animate attributeName="stroke-dashoffset" from="0" to="-20" dur="1s" repeatCount="indefinite" />
                        </line>
                        <text x="262.5" y="175" class="label">نبضة تحفيز</text>

                        <line x1="475" y1="185" x2="600" y2="185" class="arrow" stroke-dasharray="5,5">
                            <animate attributeName="stroke-dashoffset" from="0" to="-20" dur="1s" repeatCount="indefinite" />
                        </line>
                        <text x="537.5" y="175" class="label">حالة الإنذار (مستمرة)</text>

                        <line x1="400" y1="270" x2="400" y2="220" class="arrow" stroke-dasharray="5,5">
                            <animate attributeName="stroke-dashoffset" from="0" to="-20" dur="1s" repeatCount="indefinite" />
                        </line>
                        <text x="410" y="245" class="label">إشارة إعادة تعيين</text>

                        <line x1="125" y1="80" x2="125" y2="150" class="arrow" stroke-dasharray="5,5"/>
                        <line x1="125" y1="80" x2="400" y2="80" class="arrow" stroke-dasharray="5,5"/>
                        <line x1="400" y1="80" x2="400" y2="150" class="arrow" stroke-dasharray="5,5"/>
                        <line x1="400" y1="80" x2="675" y2="80" class="arrow" stroke-dasharray="5,5"/>
                        <line x1="675" y1="80" x2="675" y2="150" class="arrow" stroke-dasharray="5,5"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-6">
            <div class="slide-content">
                <h2 class="slide-title">المخطط الكهربائي: دائرة إنذار ذاتي الإغلاق (باستخدام SCR)</h2>
                <div class="circuit-diagram">
                    <svg viewBox="0 0 800 500" xmlns="http://www.w3.org/2000/svg">
                        <style>
                            .component { stroke: #334155; fill: none; stroke-width: 2; }
                            .wire { stroke: #475569; stroke-width: 2; }
                            .label { font-family: 'Inter', sans-serif; font-size: 12px; fill: #334155; text-anchor: middle; }
                            .title-text { font-family: 'Inter', sans-serif; font-size: 18px; font-weight: bold; fill: #1e3a8a; text-anchor: middle; }
                            .sub-label { font-family: 'Inter', sans-serif; font-size: 10px; fill: #64748b; text-anchor: middle; }
                            .highlight-path { stroke: #10b981; stroke-width: 3; stroke-dasharray: 5,5; }
                            @keyframes flow-circuit {
                                0% { stroke-dashoffset: 0; }
                                100% { stroke-dashoffset: -20; }
                            }
                        </style>
                        <text x="400" y="30" class="title-text">دائرة إنذار ذاتي الإغلاق (باستخدام SCR)</text>

                        <line x1="100" y1="100" x2="100" y2="150" class="wire" />
                        <line x1="100" y1="100" x2="700" y2="100" class="wire" />
                        <text x="100" y="90" class="label">+Vs</text>
                        <line x1="100" y1="400" x2="100" y2="350" class="wire" />
                        <line x1="100" y1="400" x2="700" y2="400" class="wire" />
                        <text x="100" y="410" class="label">GND</text>
                        <polygon points="100,400 95,410 105,410" fill="#475569" />

                        <rect x="150" y="120" width="20" height="20" rx="5" ry="5" class="component" />
                        <line x1="160" y1="120" x2="160" y2="100" class="wire" />
                        <line x1="160" y1="140" x2="160" y2="160" class="wire" />
                        <text x="160" y="110" class="label">SW_Trigger</text>
                        <text x="160" y="125" class="sub-label">(NO)</text>

                        <rect x="150" y="180" width="20" height="40" rx="5" ry="5" class="component" />
                        <line x1="160" y1="160" x2="160" y2="180" class="wire" />
                        <line x1="160" y1="220" x2="160" y2="240" class="wire" />
                        <text x="160" y="170" class="label">R_gate</text>
                        <text x="160" y="200" class="sub-label">1kΩ</text>

                        <polygon points="160,240 130,290 190,290" class="component" />
                        <line x1="160" y1="290" x2="160" y2="350" class="wire" />
                        <line x1="190" y1="270" x2="220" y2="270" class="wire" />
                        <text x="160" y="260" class="label">SCR</text>
                        <text x="230" y="275" class="label">G</text>
                        <text x="160" y="230" class="label">A</text>
                        <text x="160" y="360" class="label">K</text>

                        <rect x="300" y="120" width="20" height="20" rx="5" ry="5" class="component" />
                        <line x1="310" y1="120" x2="310" y2="100" class="wire" />
                        <line x1="310" y1="140" x2="310" y2="160" class="wire" />
                        <text x="310" y="110" class="label">SW_Reset</text>
                        <text x="310" y="125" class="sub-label">(NC)</text>
                        <line x1="310" y1="160" x2="310" y2="240" class="wire" />
                        <line x1="310" y1="240" x2="160" y2="240" class="wire" /> <circle cx="160" cy="370" r="10" fill="#ef4444" stroke="#ef4444" stroke-width="2" />
                        <line x1="160" y1="350" x2="160" y2="360" class="wire" />
                        <line x1="160" y1="380" x2="160" y2="400" class="wire" />
                        <text x="160" y="395" class="label">LED_Alarm</text>
                        <text x="160" y="370" class="sub-label">Alarm</text>

                        <rect x="200" y="360" width="20" height="40" rx="5" ry="5" class="component" />
                        <line x1="160" y1="380" x2="200" y2="380" class="wire" />
                        <line x1="210" y1="400" x2="210" y2="400" class="wire" />
                        <text x="210" y="350" class="label">R_LED_Alarm</text>
                        <text x="210" y="380" class="sub-label">470Ω</text>
                        <line x1="210" y1="400" x2="700" y2="400" class="wire" /> <path d="M310 100 L310 160 L160 240 L160 290 L160 350 L160 360 L160 380 L200 380 L210 380 L210 400" class="highlight-path">
                            <animate attributeName="stroke-dashoffset" from="0" to="-100" dur="3s" repeatCount="indefinite" />
                        </path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-7">
            <div class="slide-content">
                <h2 class="slide-title">مواصفات المكونات الرئيسية</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full text-right">
                    <div class="bg-purple-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-purple-700 mb-2">الثايرستور (SCR)</h3>
                        <ul class="list-disc pr-6">
                            <li>أقصى جهد عكسي متكرر (Vdrm, Vrrm)</li>
                            <li>متوسط تيار الحالة الموصلة (It(avg))</li>
                            <li>تيار البوابة اللازم للقدح (Igt)</li>
                            <li>تيار الإمساك (Holding Current - Ih)</li>
                        </ul>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-yellow-700 mb-2">مفتاح إعادة التعيين (Reset Switch)</h3>
                        <ul class="list-disc pr-6">
                            <li>النوع: ضاغط لحظي، مغلق عادة (NC)</li>
                            <li>التحمل الكهربائي (Current/Voltage Rating)</li>
                            <li>المتانة وسهولة الوصول</li>
                        </ul>
                    </div>
                    <div class="bg-teal-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-teal-700 mb-2">مفتاح/إشارة التحفيز (Trigger)</h3>
                        <ul class="list-disc pr-6">
                            <li>إذا كان مفتاحاً: ضاغط لحظي، مفتوح عادة (NO)</li>
                            <li>إذا كانت إشارة: توفير الجهد والتيار اللازمين لقدح SCR</li>
                        </ul>
                    </div>
                    <div class="bg-orange-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-orange-700 mb-2">حمل الإنذار (LED, Buzzer)</h3>
                        <ul class="list-disc pr-6">
                            <li>LED ومقاومته</li>
                            <li>الجرس: نوع DC، جهد التشغيل، استهلاك التيار</li>
                            <li>وضوح الإنذار وعدم الإزعاج</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-8">
            <div class="slide-content">
                <h2 class="slide-title">الخلاصة والمهارات المكتسبة</h2>
                <p class="slide-text">دائرة الإنذار ذاتي الإغلاق هي أداة قوية لضمان عدم تجاهل الأحداث الهامة في الأجهزة الطبية. باستخدام مكون بسيط مثل الـ SCR، يمكننا إنشاء آلية "إمساك" فعالة تتطلب تدخلاً يدوياً لإعادة التعيين.</p>
                <p class="slide-subtitle mt-4">المهارات المتوقع اكتسابها:</p>
                <ul class="text-right-list pr-8">
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>فهم مبدأ عمل الثايرستور (SCR).</span>
                    </li>
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>تصميم دائرة إنذار بسيطة تستخدم SCR.</span>
                    </li>
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>فهم آلية التحفيز (Triggering) وإعادة التعيين (Resetting).</span>
                    </li>
                    <li class="list-item">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                        <span>اختيار SCR مناسب بناءً على مواصفات الدائرة والحمل.</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-9">
            <div class="slide-content">
                <h2 class="slide-title">خبرات عملية سابقة ومشاكل مشابهة</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full text-right">
                    <div class="bg-red-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-red-700 mb-2">القدح الخاطئ للـ SCR (Spurious Triggering)</h3>
                        <p class="text-gray-700 text-sm">المشكلة: قدح عشوائي بسبب الضوضاء الكهربائية (EMI/RFI).</p>
                        <p class="text-gray-700 text-sm">الحلول: إضافة مكثف صغير بين البوابة والمهبط، استخدام مقاومة بوابة أقل، تحسين توجيه الأسلاك.</p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-blue-700 mb-2">الـ SCR لا ينطفئ</h3>
                        <p class="text-gray-700 text-sm">المشكلة: الحمل يسحب تياراً أقل من تيار الإمساك (Ih).</p>
                        <p class="text-gray-700 text-sm">الحلول: استخدام SCR ذي تيار إمساك أقل، أو إضافة "مقاومة نزف" بالتوازي مع الحمل.</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-green-700 mb-2">اختيار مفتاح إعادة التعيين الخاطئ</h3>
                        <p class="text-gray-700 text-sm">المشكلة: استخدام مفتاح Normally Open (NO) بدلاً من Normally Closed (NC).</p>
                        <p class="text-gray-700 text-sm">الدرس المستفاد: الانتباه الشديد لنوع المفاتيح المستخدمة (NO/NC).</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-xl shadow-md">
                        <h3 class="text-xl font-semibold text-yellow-700 mb-2">بديل الـ SCR: دائرة ترانزستورين</h3>
                        <p class="text-gray-700 text-sm">في تطبيقات الجهود المنخفضة جداً أو الحساسية العالية، يمكن استخدام زوج من الترانزستورات BJT.</p>
                        <p class="text-gray-700 text-sm">الدرس المستفاد: لكل مكون نقاط قوة وضعف؛ يمكن تحقيق نفس الوظيفة بطرق مختلفة.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-10">
            <div class="slide-content">
                <h2 class="slide-title">نصيحة من خبير</h2>
                <div class="flex items-center justify-center gap-4 mb-4">
                    <svg class="w-16 h-16 text-indigo-600 animated-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 21v-7m-4 0h8m4 0h1a2 2 0 002-2V5a2 2 0 00-2-2H3a2 2 0 00-2 2v10a2 2 0 002 2h1l1 1h4zm-6.6-7.24a1 1 0 11-1.4 1.4L10 9.4l-2.8-2.8a1 1 0 011.4-1.4L10 8.6l2.8-2.8a1 1 0 011.4 1.4L10 9.4z"></path></svg>
                    <p class="slide-text text-xl font-medium text-gray-800">عند استخدام الـ SCRs، تأكد دائماً من أن لديك آلية موثوقة لإعادة التعيين.</p>
                </div>
                <p class="slide-text text-lg text-gray-700">في التطبيقات الحرجة، قد ترغب في إضافة مؤشر منفصل يوضح أن "الإنذار قد تم تحفيزه" حتى لو تم إسكاته مؤقتاً.</p>
                <p class="slide-text text-lg font-bold text-blue-700 mt-4">تذكر أن الهدف من الإنذار الممسك هو عدم السماح بتجاهل المشكلة!</p>
            </div>
        </div>

        <div class="navigation-buttons">
            <button id="prevBtn" class="nav-button" disabled>السابق</button>
            <button id="nextBtn" class="nav-button">التالي</button>
        </div>
    </div>

    <script>
        // JavaScript for slide navigation
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // Function to show a specific slide
        function showSlide(index) {
            // Remove active and previous classes from all slides
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'previous');
                if (i < index) {
                    slide.classList.add('previous');
                }
            });

            // Add active class to the current slide
            slides[index].classList.add('active');
            currentSlide = index;

            // Update button states
            prevBtn.disabled = currentSlide === 0;
            nextBtn.disabled = currentSlide === slides.length - 1;
        }

        // Event listeners for navigation buttons
        nextBtn.addEventListener('click', () => {
            if (currentSlide < slides.length - 1) {
                showSlide(currentSlide + 1);
            }
        });

        prevBtn.addEventListener('click', () => {
            if (currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        });

        // Initialize the first slide on window load
        window.onload = function() {
            showSlide(0);
        };
    </script>
</body>
</html>
