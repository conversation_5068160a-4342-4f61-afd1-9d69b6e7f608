import React, { useState, useEffect } from 'react';
import {
  ChevronLeft, ChevronRight, Lightbulb, CircuitBoard, Component, Zap,
  ToggleLeft, ToggleRight, Sparkles, Speaker, Power, LayoutDashboard,
  Binary, Waves,
} from 'lucide-react';

// Main App Component
const App = () => {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);

  // Array of slide data
  const slides = [
    {
      title: 'الترانزستور ثنائي القطبية (BJT)',
      component: IntroductionSlide,
      icon: <Sparkles className="w-16 h-16 text-blue-500" />,
    },
    {
      title: 'ما هو الترانزستور ثنائي القطبية؟',
      component: WhatIsBJTSlide,
      icon: <Lightbulb className="w-16 h-16 text-green-500" />,
    },
    {
      title: 'هيكل الترانزستور (NPN و PNP)',
      component: StructureSlide,
      icon: <LayoutDashboard className="w-16 h-16 text-purple-500" />,
    },
    {
      title: 'أطراف الترانزستور',
      component: TerminalsSlide,
      icon: <Component className="w-16 h-16 text-red-500" />,
    },
    {
      title: 'مبدأ عمل الترانزستور ثنائي القطبية',
      component: WorkingPrincipleSlide,
      icon: <Zap className="w-16 h-16 text-yellow-500" />,
    },
    {
      title: 'أنواع الترانزستورات ثنائية القطبية',
      component: TypesSlide,
      icon: <Binary className="w-16 h-16 text-teal-500" />,
    },
    {
      title: 'الاستخدامات: الترانزستور كمضخم',
      component: AmplifierUseSlide,
      icon: <Speaker className="w-16 h-16 text-indigo-500" />,
    },
    {
      title: 'الاستخدامات: الترانزستور كمفتاح إلكتروني',
      component: SwitchUseSlide,
      icon: <ToggleLeft className="w-16 h-16 text-orange-500" />,
    },
    {
      title: 'تطبيقات أخرى للترانزستور',
      component: OtherApplicationsSlide,
      icon: <Waves className="w-16 h-16 text-pink-500" />,
    },
    {
      title: 'ملخص وأهمية الترانزستور',
      component: SummarySlide,
      icon: <CircuitBoard className="w-16 h-16 text-gray-500" />,
    },
  ];

  const goToNextSlide = () => {
    setCurrentSlideIndex((prevIndex) => (prevIndex + 1) % slides.length);
  };

  const goToPreviousSlide = () => {
    setCurrentSlideIndex((prevIndex) => (prevIndex - 1 + slides.length) % slides.length);
  };

  const CurrentSlideComponent = slides[currentSlideIndex].component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 flex flex-col items-center justify-center p-4 font-inter text-right">
      <style>
        {`
          @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
          body { font-family: 'Inter', sans-serif; }
          .slide-container {
            direction: rtl; /* Right-to-left for Arabic content */
            text-align: right;
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 2.5rem;
            max-width: 900px;
            width: 100%;
            min-height: 550px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            animation: fadeIn 0.5s ease-out;
          }
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
          }
          .nav-button {
            background-color: #4F46E5; /* Indigo 600 */
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 15px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
          }
          .nav-button:hover {
            background-color: #4338CA; /* Indigo 700 */
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
          }
          .nav-button:disabled {
            background-color: #A5B4FC; /* Indigo 300 */
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
          }
          .interactive-button {
            background-color: #10B981; /* Emerald 500 */
            color: white;
            padding: 0.6rem 1.2rem;
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(16, 185, 129, 0.2);
          }
          .interactive-button:hover {
            background-color: #059669; /* Emerald 600 */
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
          }
          .diagram-box {
            background-color: #F3F4F6; /* Gray 100 */
            border: 1px solid #D1D5DB; /* Gray 300 */
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
          }
          .circuit-line {
            stroke: #374151; /* Gray 700 */
            stroke-width: 2;
            fill: none;
          }
          .component-fill {
            fill: #4B5563; /* Gray 700 */
          }
          .text-label {
            font-size: 0.9rem;
            fill: #374151; /* Gray 700 */
          }
          .animated-current {
            stroke-dasharray: 10, 5;
            animation: dash 1s linear infinite;
          }
          @keyframes dash {
            to {
              stroke-dashoffset: -15;
            }
          }
        `}
      </style>
      <div className="slide-container">
        <div className="flex-grow">
          <h1 className="text-4xl font-extrabold text-gray-800 mb-6 flex items-center justify-end">
            {slides[currentSlideIndex].title}
            <span className="mr-4">{slides[currentSlideIndex].icon}</span>
          </h1>
          <CurrentSlideComponent />
        </div>

        <div className="flex justify-between items-center mt-8 pt-4 border-t border-gray-200">
          <button
            onClick={goToPreviousSlide}
            disabled={currentSlideIndex === 0}
            className="nav-button flex items-center"
          >
            <ChevronRight className="w-5 h-5 ml-2" />
            السابق
          </button>
          <span className="text-lg font-medium text-gray-600">
            {currentSlideIndex + 1} / {slides.length}
          </span>
          <button
            onClick={goToNextSlide}
            disabled={currentSlideIndex === slides.length - 1}
            className="nav-button flex items-center"
          >
            التالي
            <ChevronLeft className="w-5 h-5 mr-2" />
          </button>
        </div>
      </div>
    </div>
  );
};

// --- Slide Components ---

const IntroductionSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      مرحباً بكم في هذا العرض التقديمي التفاعلي حول الترانزستور ثنائي القطبية (BJT).
      سنستكشف معًا هذا المكون الإلكتروني الأساسي الذي أحدث ثورة في عالم الإلكترونيات.
    </p>
    <p>
      الترانزستور هو حجر الزاوية في جميع الأجهزة الإلكترونية الحديثة تقريباً، من الهواتف الذكية
      وأجهزة الكمبيوتر إلى أنظمة التحكم الصناعية.
    </p>
    <div className="diagram-box mt-6">
      <p className="text-xl font-semibold text-gray-800">دعنا نبدأ رحلتنا!</p>
    </div>
  </div>
);

const WhatIsBJTSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      الترانزستور ثنائي القطبية (BJT) هو جهاز شبه موصل ذو ثلاثة أطراف يُستخدم بشكل أساسي في:
    </p>
    <ul className="list-disc list-inside mb-4 space-y-2">
      <li>
        <span className="font-semibold text-blue-600">التضخيم:</span> لزيادة قوة الإشارات الإلكترونية الضعيفة.
      </li>
      <li>
        <span className="font-semibold text-green-600">التبديل (المفاتيح الإلكترونية):</span> للتحكم في تدفق التيار، حيث يعمل كمفتاح تشغيل/إيقاف سريع.
      </li>
    </ul>
    <p className="mb-4">
      يتم التحكم في تيار كبير بين طرفي المجمع والباعث بواسطة تيار صغير يمر عبر طرف القاعدة.
    </p>
    <div className="diagram-box">
      <svg width="300" height="180" viewBox="0 0 300 180">
        {/* Input Signal */}
        <line x1="50" y1="90" x2="80" y2="90" className="circuit-line" />
        <text x="50" y="80" className="text-label" textAnchor="middle">إشارة الدخل</text>
        <rect x="80" y="60" width="140" height="60" rx="8" ry="8" className="component-fill opacity-90" />
        <text x="150" y="95" className="text-label" textAnchor="middle" fill="white" fontSize="1.1rem">
          الترانزستور
        </text>
        {/* Output Signal */}
        <line x1="220" y1="90" x2="250" y2="90" className="circuit-line" />
        <text x="250" y="80" className="text-label" textAnchor="middle">إشارة الخرج</text>

        {/* Control Line */}
        <line x1="150" y1="120" x2="150" y2="150" className="circuit-line" />
        <text x="150" y="165" className="text-label" textAnchor="middle">إشارة التحكم</text>
        <circle cx="150" cy="150" r="5" fill="#EF4444" /> {/* Control point indicator */}
      </svg>
    </div>
  </div>
);

const StructureSlide = () => {
  const [isNPN, setIsNPN] = useState(true);

  return (
    <div className="text-lg text-gray-700 leading-relaxed">
      <p className="mb-4">
        يتكون الترانزستور ثنائي القطبية من ثلاث طبقات من المواد شبه الموصلة،
        تشكل وصلتين PN. هناك نوعان رئيسيان:
      </p>
      <ul className="list-disc list-inside mb-4 space-y-2">
        <li>
          <span className="font-semibold text-blue-600">NPN:</span> طبقة P (القاعدة) بين طبقتين N (المجمع والباعث).
        </li>
        <li>
          <span className="font-semibold text-green-600">PNP:</span> طبقة N (القاعدة) بين طبقتين P (المجمع والباعث).
        </li>
      </ul>
      <div className="flex justify-center mb-6">
        <button
          onClick={() => setIsNPN(!isNPN)}
          className="interactive-button flex items-center"
        >
          {isNPN ? <ToggleRight className="w-5 h-5 ml-2" /> : <ToggleLeft className="w-5 h-5 ml-2" />}
          تبديل النوع ({isNPN ? 'NPN' : 'PNP'})
        </button>
      </div>
      <div className="diagram-box flex-col">
        <p className="font-semibold text-xl mb-4">{isNPN ? 'هيكل ترانزستور NPN' : 'هيكل ترانزستور PNP'}</p>
        <svg width="350" height="150" viewBox="0 0 350 150">
          {/* NPN Structure */}
          {isNPN ? (
            <>
              <rect x="50" y="20" width="80" height="110" fill="#60A5FA" rx="8" ry="8" /> {/* Collector N */}
              <text x="90" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">N</text>
              <text x="90" y="120" textAnchor="middle" fill="white" fontSize="0.9rem">المجمع</text>

              <rect x="130" y="40" width="90" height="70" fill="#F87171" rx="8" ry="8" /> {/* Base P */}
              <text x="175" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">P</text>
              <text x="175" y="105" textAnchor="middle" fill="white" fontSize="0.9rem">القاعدة</text>

              <rect x="220" y="20" width="80" height="110" fill="#60A5FA" rx="8" ry="8" /> {/* Emitter N */}
              <text x="260" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">N</text>
              <text x="260" y="120" textAnchor="middle" fill="white" fontSize="0.9rem">الباعث</text>
            </>
          ) : (
            <>
              {/* PNP Structure */}
              <rect x="50" y="20" width="80" height="110" fill="#F87171" rx="8" ry="8" /> {/* Collector P */}
              <text x="90" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">P</text>
              <text x="90" y="120" textAnchor="middle" fill="white" fontSize="0.9rem">المجمع</text>

              <rect x="130" y="40" width="90" height="70" fill="#60A5FA" rx="8" ry="8" /> {/* Base N */}
              <text x="175" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">N</text>
              <text x="175" y="105" textAnchor="middle" fill="white" fontSize="0.9rem">القاعدة</text>

              <rect x="220" y="20" width="80" height="110" fill="#F87171" rx="8" ry="8" /> {/* Emitter P */}
              <text x="260" y="75" textAnchor="middle" fill="white" fontSize="1.8rem" fontWeight="bold">P</text>
              <text x="260" y="120" textAnchor="middle" fill="white" fontSize="0.9rem">الباعث</text>
            </>
          )}
        </svg>
      </div>
    </div>
  );
};

const TerminalsSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      لكل ترانزستور ثنائي القطبية ثلاثة أطراف، لكل منها دور محدد في تشغيل الجهاز:
    </p>
    <ul className="list-disc list-inside mb-4 space-y-2">
      <li>
        <span className="font-semibold text-blue-600">القاعدة (Base - B):</span> الطرف الذي يتحكم في تدفق التيار عبر الترانزستور.
      </li>
      <li>
        <span className="font-semibold text-green-600">المجمع (Collector - C):</span> الطرف الذي يجمع التيار الرئيسي الذي يتدفق عبر الترانزستور.
      </li>
      <li>
        <span className="font-semibold text-red-600">الباعث (Emitter - E):</span> الطرف الذي يبعث التيار الرئيسي إلى الترانزستور.
      </li>
    </ul>
    <div className="diagram-box flex-col">
      <p className="font-semibold text-xl mb-4">رموز الترانزستورات في الدوائر</p>
      <div className="flex justify-around w-full">
        {/* NPN Symbol */}
        <div className="flex flex-col items-center">
          <p className="mb-2 font-medium">ترانزستور NPN</p>
          <svg width="150" height="150" viewBox="0 0 150 150">
            {/* Base line */}
            <line x1="75" y1="20" x2="75" y2="70" className="circuit-line" />
            <line x1="75" y1="70" x2="25" y2="70" className="circuit-line" /> {/* Base line to left */}
            <text x="10" y="75" className="text-label">B</text>

            {/* Collector line */}
            <line x1="75" y1="70" x2="75" y2="120" className="circuit-line" />
            <line x1="75" y1="120" x2="100" y2="120" className="circuit-line" /> {/* Collector line to right */}
            <text x="115" y="125" className="text-label">C</text>

            {/* Emitter line with arrow (NPN) */}
            <line x1="75" y1="70" x2="75" y2="120" className="circuit-line" />
            <line x1="75" y1="120" x2="50" y2="120" className="circuit-line" /> {/* Emitter line to left */}
            <polygon points="50,120 58,112 42,112" fill="#374151" transform="rotate(90 50 120)" /> {/* Arrow pointing out */}
            <text x="35" y="125" className="text-label">E</text>

            {/* Circle */}
            <circle cx="75" cy="70" r="60" className="circuit-line" />
          </svg>
        </div>

        {/* PNP Symbol */}
        <div className="flex flex-col items-center">
          <p className="mb-2 font-medium">ترانزستور PNP</p>
          <svg width="150" height="150" viewBox="0 0 150 150">
            {/* Base line */}
            <line x1="75" y1="20" x2="75" y2="70" className="circuit-line" />
            <line x1="75" y1="70" x2="25" y2="70" className="circuit-line" /> {/* Base line to left */}
            <text x="10" y="75" className="text-label">B</text>

            {/* Collector line */}
            <line x1="75" y1="70" x2="75" y2="120" className="circuit-line" />
            <line x1="75" y1="120" x2="100" y2="120" className="circuit-line" /> {/* Collector line to right */}
            <text x="115" y="125" className="text-label">C</text>

            {/* Emitter line with arrow (PNP) */}
            <line x1="75" y1="70" x2="75" y2="120" className="circuit-line" />
            <line x1="75" y1="120" x2="50" y2="120" className="circuit-line" /> {/* Emitter line to left */}
            <polygon points="50,120 42,128 58,128" fill="#374151" transform="rotate(90 50 120)" /> {/* Arrow pointing in */}
            <text x="35" y="125" className="text-label">E</text>

            {/* Circle */}
            <circle cx="75" cy="70" r="60" className="circuit-line" />
          </svg>
        </div>
      </div>
    </div>
  </div>
);

const WorkingPrincipleSlide = () => {
  const [isBiased, setIsBiased] = useState(false);

  return (
    <div className="text-lg text-gray-700 leading-relaxed">
      <p className="mb-4">
        يعمل الترانزستور عن طريق التحكم في تدفق التيار بين المجمع والباعث بواسطة تيار صغير يطبق على القاعدة.
        يتطلب ذلك "تحيزًا" مناسبًا للوصلات الداخلية:
      </p>
      <ul className="list-disc list-inside mb-4 space-y-2">
        <li>
          <span className="font-semibold text-blue-600">وصلة القاعدة-الباعث (Base-Emitter):</span> يجب أن تكون متحيزة أماميًا (Forward Biased) للسماح بتدفق تيار القاعدة.
        </li>
        <li>
          <span className="font-semibold text-green-600">وصلة القاعدة-المجمع (Base-Collector):</span> يجب أن تكون متحيزة عكسيًا (Reverse Biased) لجمع التيار.
        </li>
      </ul>
      <div className="flex justify-center mb-6">
        <button
          onClick={() => setIsBiased(!isBiased)}
          className="interactive-button flex items-center"
        >
          {isBiased ? <Power className="w-5 h-5 ml-2" /> : <Power className="w-5 h-5 ml-2" />}
          {isBiased ? 'إيقاف التحيز' : 'تطبيق التحيز'}
        </button>
      </div>
      <div className="diagram-box flex-col">
        <p className="font-semibold text-xl mb-4">مبدأ عمل ترانزستور NPN</p>
        <svg width="400" height="200" viewBox="0 0 400 200">
          {/* NPN Transistor */}
          <rect x="150" y="50" width="100" height="100" fill="#60A5FA" rx="10" ry="10" />
          <text x="200" y="80" textAnchor="middle" fill="white" fontSize="1.5rem" fontWeight="bold">N</text>
          <text x="200" y="110" textAnchor="middle" fill="white" fontSize="1.5rem" fontWeight="bold">P</text>
          <text x="200" y="140" textAnchor="middle" fill="white" fontSize="1.5rem" fontWeight="bold">N</text>

          {/* Base connection */}
          <line x1="100" y1="100" x2="150" y2="100" className="circuit-line" />
          <text x="90" y="95" className="text-label">قاعدة (B)</text>

          {/* Collector connection */}
          <line x1="250" y1="70" x2="300" y2="70" className="circuit-line" />
          <text x="310" y="65" className="text-label">مجمع (C)</text>

          {/* Emitter connection */}
          <line x1="250" y1="130" x2="300" y2="130" className="circuit-line" />
          <text x="310" y="135" className="text-label">باعث (E)</text>

          {/* Base-Emitter Voltage Source */}
          <line x1="100" y1="100" x2="100" y2="170" className="circuit-line" />
          <line x1="100" y1="170" x2="120" y2="170" className="circuit-line" />
          <line x1="120" y1="170" x2="120" y2="180" className="circuit-line" />
          <line x1="120" y1="180" x2="80" y2="180" className="circuit-line" />
          <line x1="80" y1="180" x2="80" y2="170" className="circuit-line" />
          <line x1="80" y1="170" x2="100" y2="170" className="circuit-line" />
          <text x="100" y="195" textAnchor="middle" className="text-label">V_BE</text>
          <circle cx="100" cy="170" r="3" fill="#EF4444" />

          {/* Collector-Emitter Voltage Source */}
          <line x1="300" y1="70" x2="300" y2="20" className="circuit-line" />
          <line x1="300" y1="20" x2="320" y2="20" className="circuit-line" />
          <line x1="320" y1="20" x2="320" y2="30" className="circuit-line" />
          <line x1="320" y1="30" x2="280" y2="30" className="circuit-line" />
          <line x1="280" y1="30" x2="280" y2="20" className="circuit-line" />
          <line x1="280" y1="20" x2="300" y2="20" className="circuit-line" />
          <text x="300" y="5" textAnchor="middle" className="text-label">V_CC</text>
          <circle cx="300" cy="20" r="3" fill="#EF4444" />

          {/* Ground */}
          <line x1="100" y1="180" x2="100" y2="190" className="circuit-line" />
          <line x1="90" y1="190" x2="110" y2="190" className="circuit-line" />
          <line x1="95" y1="195" x2="105" y2="195" className="circuit-line" /> {/* Fixed: y1 -> y2 */}
          <line x1="98" y1="200" x2="102" y2="200" className="circuit-line" />

          <line x1="300" y1="130" x2="300" y2="190" className="circuit-line" />
          <line x1="290" y1="190" x2="310" y2="190" className="circuit-line" />
          <line x1="295" y1="195" x2="305" y2="195" className="circuit-line" /> {/* Fixed: y1 -> y2 */}
          <line x1="298" y1="200" x2="302" y2="200" className="circuit-line" />

          {/* Animated current flow */}
          {isBiased && (
            <>
              <path d="M100,100 L150,100" className="circuit-line animated-current stroke-red-500" /> {/* Base current */}
              <path d="M250,70 L300,70" className="circuit-line animated-current stroke-blue-500" /> {/* Collector current */}
              <path d="M250,130 L300,130" className="circuit-line animated-current stroke-green-500" /> {/* Emitter current */}
              <path d="M150,100 L200,100 C200,120 200,130 250,130" className="circuit-line animated-current stroke-purple-500" /> {/* Internal flow */}
            </>
          )}
        </svg>
      </div>
    </div>
  );
};

const TypesSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      كما ذكرنا سابقًا، هناك نوعان رئيسيان من الترانزستورات ثنائية القطبية، NPN و PNP،
      والتي تختلف في ترتيب طبقات أشباه الموصلة واتجاه تدفق التيار.
    </p>
    <div className="diagram-box flex-col">
      <p className="font-semibold text-xl mb-4">مقارنة سريعة: NPN مقابل PNP</p>
      <table className="w-full text-sm text-gray-700 border-collapse">
        <thead>
          <tr className="bg-gray-200">
            <th className="py-2 px-4 border border-gray-300 text-center">الميزة</th>
            <th className="py-2 px-4 border border-gray-300 text-center">NPN</th>
            <th className="py-2 px-4 border border-gray-300 text-center">PNP</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="py-2 px-4 border border-gray-300 font-semibold">الطبقات</td>
            <td className="py-2 px-4 border border-gray-300 text-center">N-P-N</td>
            <td className="py-2 px-4 border border-gray-300 text-center">P-N-P</td>
          </tr>
          <tr className="bg-gray-50">
            <td className="py-2 px-4 border border-gray-300 font-semibold">تيار القاعدة (I_B)</td>
            <td className="py-2 px-4 border border-gray-300 text-center">يدخل القاعدة</td>
            <td className="py-2 px-4 border border-gray-300 text-center">يخرج من القاعدة</td>
          </tr>
          <tr>
            <td className="py-2 px-4 border border-gray-300 font-semibold">تيار المجمع (I_C)</td>
            <td className="py-2 px-4 border border-gray-300 text-center">يدخل المجمع</td>
            <td className="py-2 px-4 border border-gray-300 text-center">يخرج من المجمع</td>
          </tr>
          <tr className="bg-gray-50">
            <td className="py-2 px-4 border border-gray-300 font-semibold">التحيز الأمامي للقاعدة-الباعث</td>
            <td className="py-2 px-4 border border-gray-300 text-center">V_BE موجب</td>
            <td className="py-2 px-4 border border-gray-300 text-center">V_BE سالب</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
);

const AmplifierUseSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      أحد أهم استخدامات الترانزستور هو كمضخم للإشارات.
      يسمح تيار القاعدة الصغير بالتحكم في تيار أكبر بكثير بين المجمع والباعث،
      مما يؤدي إلى تضخيم الإشارة.
    </p>
    <p className="mb-4">
      <span className="font-semibold text-blue-600">مثال:</span> دائرة المضخم ذو الباعث المشترك (Common Emitter Amplifier) هي الأكثر شيوعًا.
    </p>
    <div className="diagram-box flex-col">
      <p className="font-semibold text-xl mb-4">دائرة مضخم الباعث المشترك (NPN)</p>
      <svg width="450" height="250" viewBox="0 0 450 250">
        {/* Input Signal */}
        <line x1="50" y1="120" x2="80" y2="120" className="circuit-line" />
        <circle cx="80" cy="120" r="5" fill="#EF4444" />
        <text x="50" y="110" className="text-label" textAnchor="middle">إشارة الدخل</text>

        {/* Base Resistor */}
        <line x1="80" y1="120" x2="110" y2="120" className="circuit-line" />
        <rect x="110" y="110" width="30" height="20" className="component-fill" />
        <text x="125" y="105" className="text-label" textAnchor="middle">R_B</text>
        <line x1="140" y1="120" x2="170" y2="120" className="circuit-line" />

        {/* NPN Transistor */}
        <line x1="170" y1="120" x2="170" y2="170" className="circuit-line" /> {/* Base line */}
        <line x1="170" y1="170" x2="120" y2="170" className="circuit-line" /> {/* Base to transistor body */}
        <polygon points="170,170 170,120 220,120 220,170" fill="none" stroke="#374151" strokeWidth="2" /> {/* Transistor body simplified */}

        {/* Emitter line with arrow */}
        <line x1="170" y1="170" x2="170" y2="210" className="circuit-line" />
        <polygon points="170,210 178,202 162,202" fill="#374151" transform="rotate(90 170 210)" /> {/* Arrow pointing out */}
        <text x="155" y="220" className="text-label">E</text>

        {/* Emitter Resistor to Ground */}
        <rect x="155" y="210" width="30" height="20" className="component-fill" />
        <text x="170" y="205" className="text-label" textAnchor="middle">R_E</text>
        <line x1="170" y1="230" x2="170" y2="240" className="circuit-line" />
        <line x1="160" y1="240" x2="180" y2="240" className="circuit-line" />
        <line x1="165" y1="245" x2="175" y1="245" className="circuit-line" />

        {/* Collector line */}
        <line x1="220" y1="120" x2="220" y2="70" className="circuit-line" />
        <text x="235" y="65" className="text-label">C</text>

        {/* Collector Resistor */}
        <rect x="205" y="40" width="30" height="20" className="component-fill" />
        <text x="220" y="35" className="text-label" textAnchor="middle">R_C</text>
        <line x1="220" y1="70" x2="220" y2="60" className="circuit-line" />
        <line x1="220" y1="40" x2="220" y2="20" className="circuit-line" />

        {/* VCC Power Supply */}
        <line x1="220" y1="20" x2="220" y2="10" className="circuit-line" />
        <line x1="210" y1="10" x2="230" y2="10" className="circuit-line" />
        <line x1="215" y1="5" x2="225" y1="5" className="circuit-line" />
        <text x="240" y="15" className="text-label">V_CC</text>

        {/* Output Signal */}
        <line x1="220" y1="70" x2="250" y2="70" className="circuit-line" />
        <circle cx="250" cy="70" r="5" fill="#3B82F6" />
        <text x="280" y="65" className="text-label">إشارة الخرج</text>
      </svg>
    </div>
  </div>
);

const SwitchUseSlide = () => {
  const [isSwitchOn, setIsSwitchOn] = useState(false);

  return (
    <div className="text-lg text-gray-700 leading-relaxed">
      <p className="mb-4">
        يمكن استخدام الترانزستور كمفتاح إلكتروني للتحكم في تشغيل وإيقاف الأحمال (مثل المصابيح أو المحركات).
        عندما يتم تطبيق تيار كافٍ على القاعدة، يعمل الترانزستور كمفتاح مغلق (تشغيل).
        عندما لا يوجد تيار على القاعدة، يعمل كمفتاح مفتوح (إيقاف).
      </p>
      <div className="flex justify-center mb-6">
        <button
          onClick={() => setIsSwitchOn(!isSwitchOn)}
          className="interactive-button flex items-center"
        >
          {isSwitchOn ? <ToggleRight className="w-5 h-5 ml-2" /> : <ToggleLeft className="w-5 h-5 ml-2" />}
          {isSwitchOn ? 'إيقاف المفتاح' : 'تشغيل المفتاح'}
        </button>
      </div>
      <div className="diagram-box flex-col">
        <p className="font-semibold text-xl mb-4">دائرة مفتاح ترانزستور (NPN)</p>
        <svg width="400" height="200" viewBox="0 0 400 200">
          {/* VCC Power Supply */}
          <line x1="200" y1="10" x2="200" y2="20" className="circuit-line" />
          <line x1="190" y1="20" x2="210" y1="20" className="circuit-line" />
          <line x1="195" y1="15" x2="205" y1="15" className="circuit-line" />
          <text x="220" y="25" className="text-label">V_CC</text>

          {/* Load (LED) */}
          <line x1="200" y1="20" x2="200" y2="60" className="circuit-line" />
          <circle cx="200" cy="75" r="15" fill={isSwitchOn ? "#FCD34D" : "#D1D5DB"} stroke="#FBBF24" strokeWidth="2" />
          <text x="200" y="78" textAnchor="middle" fill={isSwitchOn ? "#92400E" : "#4B5563"} fontSize="0.8rem">مصباح</text>
          <line x1="200" y1="90" x2="200" y2="110" className="circuit-line" />

          {/* NPN Transistor */}
          <line x1="200" y1="110" x2="200" y2="160" className="circuit-line" /> {/* Collector line */}
          <line x1="150" y1="135" x2="200" y2="135" className="circuit-line" /> {/* Base line */}
          <polygon points="200,160 208,152 192,152" fill="#374151" transform="rotate(90 200 160)" /> {/* Emitter arrow */}
          <text x="135" y="140" className="text-label">B</text>
          <text x="215" y="105" className="text-label">C</text>
          <text x="215" y="165" className="text-label">E</text>

          {/* Ground */}
          <line x1="200" y1="160" x2="200" y2="170" className="circuit-line" />
          <line x1="190" y1="170" x2="210" y1="170" className="circuit-line" />
          <line x1="195" y1="175" x2="205" y1="175" className="circuit-line" />
          <line x1="198" y1="180" x2="202" y1="180" className="circuit-line" />

          {/* Control Switch/Button */}
          <rect x="50" y="125" width="30" height="20" className="component-fill" />
          <text x="65" y="120" className="text-label" textAnchor="middle">R_B</text>
          <line x1="50" y1="135" x2="20" y2="135" className="circuit-line" />
          <line x1="20" y1="135" x2="20" y2="100" className="circuit-line" />
          <circle cx="20" cy="100" r="5" fill={isSwitchOn ? "#10B981" : "#EF4444"} />
          <text x="20" y="90" className="text-label" textAnchor="middle">{isSwitchOn ? 'تشغيل' : 'إيقاف'}</text>

          {/* Animated current flow */}
          {isSwitchOn && (
            <>
              <path d="M200,20 L200,60" className="circuit-line animated-current stroke-blue-500" /> {/* Current to LED */}
              <path d="M200,90 L200,110" className="circuit-line animated-current stroke-blue-500" /> {/* Current from LED */}
              <path d="M200,110 L200,160" className="circuit-line animated-current stroke-blue-500" /> {/* Collector-Emitter current */}
              <path d="M20,100 L20,135 L150,135" className="circuit-line animated-current stroke-red-500" /> {/* Base current */}
            </>
          )}
        </svg>
      </div>
    </div>
  );
};

const OtherApplicationsSlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      بالإضافة إلى التضخيم والتبديل، يستخدم الترانزستور ثنائي القطبية في مجموعة واسعة من التطبيقات الإلكترونية الأخرى، بما في ذلك:
    </p>
    <ul className="list-disc list-inside mb-4 space-y-2">
      <li>
        <span className="font-semibold text-blue-600">المذبذبات (Oscillators):</span> لتوليد إشارات كهربائية متذبذبة بترددات محددة.
      </li>
      <li>
        <span className="font-semibold text-green-600">المعدلات (Modulators):</span> لتغيير خصائص إشارة (مثل سعتها أو ترددها) بناءً على إشارة أخرى.
      </li>
      <li>
        <span className="font-semibold text-red-600">منظمات الجهد (Voltage Regulators):</span> للحفاظ على جهد خرج ثابت بغض النظر عن تغيرات جهد الدخل أو الحمل.
      </li>
      <li>
        <span className="font-semibold text-purple-600">الدوائر المتكاملة (Integrated Circuits):</span> المكون الأساسي في بناء الرقائق الإلكترونية المعقدة.
      </li>
    </ul>
    <div className="diagram-box flex justify-around items-center">
      <div className="flex flex-col items-center p-4">
        <Waves className="w-16 h-16 text-blue-500 mb-2" />
        <p className="text-sm font-medium">المذبذبات</p>
      </div>
      <div className="flex flex-col items-center p-4">
        <Sparkles className="w-16 h-16 text-green-500 mb-2" />
        <p className="text-sm font-medium">المعدلات</p>
      </div>
      <div className="flex flex-col items-center p-4">
        <Power className="w-16 h-16 text-red-500 mb-2" />
        <p className="text-sm font-medium">منظمات الجهد</p>
      </div>
      <div className="flex flex-col items-center p-4">
        <CircuitBoard className="w-16 h-16 text-purple-500 mb-2" />
        <p className="text-sm font-medium">الدوائر المتكاملة</p>
      </div>
    </div>
  </div>
);

const SummarySlide = () => (
  <div className="text-lg text-gray-700 leading-relaxed">
    <p className="mb-4">
      لقد استكشفنا معًا أساسيات الترانزستور ثنائي القطبية (BJT)، وأنواعه، ومبدأ عمله،
      وأهم استخداماته في عالم الإلكترونيات.
    </p>
    <ul className="list-disc list-inside mb-4 space-y-2">
      <li>
        الترانزستور هو جهاز تحكم في التيار، يسمح لتيار صغير بالتحكم في تيار أكبر.
      </li>
      <li>
        الأنواع الرئيسية هي NPN و PNP، وتختلف في ترتيب الطبقات واتجاه التيار.
      </li>
      <li>
        يُستخدم على نطاق واسع كمضخم للإشارات وكمفتاح إلكتروني.
      </li>
      <li>
        يلعب دورًا حاسمًا في تصميم الدوائر الإلكترونية الحديثة.
      </li>
    </ul>
    <p className="font-semibold text-xl text-center text-gray-800 mt-6">
      شكراً لكم على اهتمامكم!
    </p>
    <div className="diagram-box mt-6">
      <p className="text-xl font-semibold text-gray-800">الترانزستور: قلب الإلكترونيات الحديثة!</p>
    </div>
  </div>
);

export default App;
