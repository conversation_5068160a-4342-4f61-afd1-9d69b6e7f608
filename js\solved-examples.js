// Solved Examples Module JavaScript

// Global variables
let currentProblemType = 'ohms-law';
let currentProblem = null;
let moduleProgress = 0;

// Problem templates for different types
const problemTemplates = {
    'ohms-law': [
        {
            question: "A medical device requires a current-limiting resistor. If the LED operates at 2.1V and requires 20mA, and the supply voltage is 5V, what resistance value is needed?",
            given: { voltage_supply: 5, voltage_led: 2.1, current: 0.02 },
            solution: { resistance: 145, steps: ["Calculate voltage drop across resistor: V_R = 5V - 2.1V = 2.9V", "Apply Ohm's law: R = V/I = 2.9V / 0.02A = 145Ω"] },
            circuit: { type: 'led-resistor', elements: ['5V', 'R=?', 'LED(2.1V,20mA)'] }
        },
        {
            question: "An ECG amplifier has an input impedance of 10MΩ and draws 0.5μA of current. What is the input voltage?",
            given: { resistance: 10000000, current: 0.0000005 },
            solution: { voltage: 5, steps: ["Apply Ohm's law: V = I × R", "V = 0.5μA × 10MΩ = 5V"] },
            circuit: { type: 'amplifier-input', elements: ['V=?', '10MΩ', '0.5μA'] }
        },
        {
            question: "A pulse oximeter LED driver circuit uses a 3.3V supply. If the red LED has a forward voltage of 1.8V and needs 25mA, calculate the required series resistance.",
            given: { voltage_supply: 3.3, voltage_led: 1.8, current: 0.025 },
            solution: { resistance: 60, steps: ["Voltage across resistor: V_R = 3.3V - 1.8V = 1.5V", "R = V_R / I = 1.5V / 0.025A = 60Ω"] },
            circuit: { type: 'led-driver', elements: ['3.3V', 'R=?', 'Red LED(1.8V,25mA)'] }
        }
    ],
    'power': [
        {
            question: "A defibrillator capacitor stores 360J of energy at 2000V. What is the capacitance value?",
            given: { energy: 360, voltage: 2000 },
            solution: { capacitance: 0.00018, steps: ["Use energy formula: E = ½CV²", "Rearrange: C = 2E/V²", "C = 2×360J/(2000V)² = 180μF"] },
            circuit: { type: 'capacitor-energy', elements: ['C=?', '2000V', '360J'] }
        },
        {
            question: "An ECG electrode has a resistance of 2kΩ and carries 1mA of current. Calculate the power dissipated.",
            given: { resistance: 2000, current: 0.001 },
            solution: { power: 0.002, steps: ["Use power formula: P = I²R", "P = (0.001A)² × 2000Ω = 2mW"] },
            circuit: { type: 'electrode-power', elements: ['2kΩ', '1mA', 'P=?'] }
        }
    ],
    'divider': [
        {
            question: "Design a voltage divider to provide 3.3V output from a 5V supply for a microcontroller interface. If R2 = 2.2kΩ, find R1.",
            given: { voltage_in: 5, voltage_out: 3.3, r2: 2200 },
            solution: { r1: 1133, steps: ["Use voltage divider: V_out = V_in × R2/(R1+R2)", "3.3 = 5 × 2200/(R1+2200)", "Solve: R1 = 1.13kΩ"] },
            circuit: { type: 'voltage-divider', elements: ['5V', 'R1=?', 'R2=2.2kΩ', '3.3V'] }
        }
    ],
    'filter': [
        {
            question: "Design a low-pass filter for an ECG amplifier with cutoff frequency of 100Hz. If C = 1μF, calculate R.",
            given: { frequency: 100, capacitance: 0.000001 },
            solution: { resistance: 1592, steps: ["Use formula: fc = 1/(2πRC)", "Rearrange: R = 1/(2πfcC)", "R = 1/(2π×100×1μF) = 1.59kΩ"] },
            circuit: { type: 'low-pass-filter', elements: ['R=?', 'C=1μF', 'fc=100Hz'] }
        }
    ]
};

// Initialize the module
document.addEventListener('DOMContentLoaded', function() {
    initializeModule();
    setupEventListeners();
    generateNewProblem();
    updateProgress();
});

function initializeModule() {
    console.log('Solved Examples module initialized');
    
    // Animate hero elements
    animateHeroElements();
    
    // Setup intersection observer for animations
    setupScrollAnimations();
}

function setupEventListeners() {
    // Problem type buttons
    const problemBtns = document.querySelectorAll('.problem-btn');
    problemBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            problemBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            // Update current problem type
            currentProblemType = this.dataset.type;
            // Generate new problem
            generateNewProblem();
        });
    });

    // Category explore buttons
    const exploreBtns = document.querySelectorAll('.explore-btn');
    exploreBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const category = this.closest('.category-card').dataset.category;
            openCategory(category);
        });
    });

    // Filter calculator inputs
    const filterInputs = document.querySelectorAll('#filterR, #filterC');
    filterInputs.forEach(input => {
        input.addEventListener('input', calculateCutoffFrequency);
    });
}

function animateHeroElements() {
    // Animate hero text elements with staggered delays
    const heroElements = document.querySelectorAll('.hero-text h1, .hero-text p, .hero-objectives');
    heroElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 200);
    });

    // Animate showcase elements
    const showcaseElements = document.querySelectorAll('.category-item, .featured-example');
    showcaseElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, 600 + index * 150);
    });
}

function setupScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe category cards
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        observer.observe(card);
    });
}

function generateNewProblem() {
    const problems = problemTemplates[currentProblemType];
    if (!problems || problems.length === 0) return;
    
    // Select random problem
    const randomIndex = Math.floor(Math.random() * problems.length);
    currentProblem = problems[randomIndex];
    
    // Update problem display
    updateProblemDisplay();
    
    // Clear previous solution
    clearSolutionWorkspace();
}

function updateProblemDisplay() {
    if (!currentProblem) return;
    
    const problemDisplay = document.getElementById('problemDisplay');
    if (!problemDisplay) return;
    
    problemDisplay.innerHTML = `
        <p>${currentProblem.question}</p>
        <div class="problem-circuit">
            <div class="circuit-elements">
                ${currentProblem.circuit.elements.map(element => 
                    `<span class="circuit-element">${element}</span>`
                ).join('')}
            </div>
        </div>
    `;
    
    // Add animation
    problemDisplay.style.opacity = '0';
    problemDisplay.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        problemDisplay.style.transition = 'all 0.5s ease';
        problemDisplay.style.opacity = '1';
        problemDisplay.style.transform = 'translateY(0)';
    }, 100);
}

function clearSolutionWorkspace() {
    const textareas = document.querySelectorAll('.solution-steps textarea');
    const inputs = document.querySelectorAll('.solution-steps input');
    
    textareas.forEach(textarea => textarea.value = '');
    inputs.forEach(input => input.value = '');
    
    // Hide any previous feedback
    const feedback = document.querySelector('.solution-feedback');
    if (feedback) feedback.remove();
}

function checkSolution() {
    if (!currentProblem) return;
    
    const finalAnswer = document.querySelector('.solution-steps input[type="text"]');
    if (!finalAnswer || !finalAnswer.value.trim()) {
        showFeedback('Please enter your final answer.', 'warning');
        return;
    }
    
    const userAnswer = parseFloat(finalAnswer.value.replace(/[^\d.-]/g, ''));
    const correctAnswer = Object.values(currentProblem.solution)[0];
    
    const tolerance = 0.1; // 10% tolerance
    const isCorrect = Math.abs(userAnswer - correctAnswer) / correctAnswer <= tolerance;
    
    if (isCorrect) {
        showFeedback('Correct! Well done!', 'success');
        updateProgress(10);
    } else {
        showFeedback(`Not quite right. The correct answer is ${formatAnswer(correctAnswer)}.`, 'error');
    }
}

function showHint() {
    if (!currentProblem) return;
    
    const hints = {
        'ohms-law': 'Remember Ohm\'s Law: V = I × R. Identify what you know and what you need to find.',
        'power': 'Power formulas: P = V²/R, P = I²R, P = VI. For capacitors: E = ½CV².',
        'divider': 'Voltage divider formula: Vout = Vin × R2/(R1+R2)',
        'filter': 'Cutoff frequency formula: fc = 1/(2πRC)'
    };
    
    const hint = hints[currentProblemType] || 'Think about the fundamental relationships between voltage, current, and resistance.';
    showFeedback(hint, 'info');
}

function showSolution() {
    if (!currentProblem) return;
    
    const solutionSteps = currentProblem.solution.steps;
    const solutionHtml = `
        <div class="detailed-solution">
            <h4>Complete Solution:</h4>
            <ol>
                ${solutionSteps.map(step => `<li>${step}</li>`).join('')}
            </ol>
            <p class="final-answer">Final Answer: ${formatAnswer(Object.values(currentProblem.solution)[0])}</p>
        </div>
    `;
    
    showFeedback(solutionHtml, 'solution');
}

function showFeedback(message, type) {
    // Remove existing feedback
    const existingFeedback = document.querySelector('.solution-feedback');
    if (existingFeedback) existingFeedback.remove();
    
    // Create new feedback element
    const feedback = document.createElement('div');
    feedback.className = `solution-feedback ${type}`;
    feedback.innerHTML = message;
    
    // Insert after solution actions
    const solutionActions = document.querySelector('.solution-actions');
    solutionActions.parentNode.insertBefore(feedback, solutionActions.nextSibling);
    
    // Animate in
    feedback.style.opacity = '0';
    feedback.style.transform = 'translateY(-10px)';
    
    setTimeout(() => {
        feedback.style.transition = 'all 0.3s ease';
        feedback.style.opacity = '1';
        feedback.style.transform = 'translateY(0)';
    }, 100);
}

function formatAnswer(value) {
    if (value >= 1000000) {
        return `${(value / 1000000).toFixed(2)}M`;
    } else if (value >= 1000) {
        return `${(value / 1000).toFixed(2)}k`;
    } else if (value < 0.001) {
        return `${(value * 1000000).toFixed(2)}μ`;
    } else if (value < 1) {
        return `${(value * 1000).toFixed(2)}m`;
    } else {
        return value.toFixed(2);
    }
}

function openCategory(category) {
    // This would typically navigate to a detailed category page
    console.log(`Opening category: ${category}`);
    
    // For now, show a message
    showFeedback(`Opening ${category} examples category...`, 'info');
    
    // Update progress
    updateProgress(5);
}

function calculateCutoffFrequency() {
    const rInput = document.getElementById('filterR');
    const cInput = document.getElementById('filterC');
    const resultSpan = document.getElementById('cutoffResult');
    
    if (!rInput || !cInput || !resultSpan) return;
    
    const r = parseFloat(rInput.value);
    const c = parseFloat(cInput.value) * 1e-6; // Convert μF to F
    
    if (r > 0 && c > 0) {
        const fc = 1 / (2 * Math.PI * r * c);
        resultSpan.textContent = `${fc.toFixed(1)} Hz`;
    } else {
        resultSpan.textContent = '-- Hz';
    }
}

function updateProgress(increment = 0) {
    moduleProgress = Math.min(100, moduleProgress + increment);
    
    const progressFill = document.getElementById('moduleProgress');
    const progressText = document.querySelector('.progress-text');
    
    if (progressFill) {
        progressFill.style.width = `${moduleProgress}%`;
    }
    
    if (progressText) {
        progressText.textContent = `Progress: ${moduleProgress}%`;
    }
}

// Export functions for global access
window.generateNewProblem = generateNewProblem;
window.checkSolution = checkSolution;
window.showHint = showHint;
window.showSolution = showSolution;
window.openCategory = openCategory;
