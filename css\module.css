/* Module-specific styles */

/* Module Navigation */
.module-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.module-nav .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: #667eea;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 25px;
    background: rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: translateX(-2px);
}

.module-nav h1 {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
}

.progress-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-text {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

.progress-bar {
    width: 150px;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
    width: 0%;
}

/* Module Content */
.module-content {
    margin-top: 80px;
    padding: 40px 0;
    min-height: calc(100vh - 80px);
}

/* Introduction Section */
.intro-section {
    padding: 60px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.intro-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 15px;
}

.intro-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.learning-objectives {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.learning-objectives h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.learning-objectives ul {
    list-style: none;
    padding: 0;
}

.learning-objectives li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.learning-objectives i {
    color: #4ecdc4;
}

/* Atom Animation */
.intro-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.atom-model {
    width: 200px;
    height: 200px;
    position: relative;
}

.nucleus {
    width: 20px;
    height: 20px;
    background: #ff6b6b;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 20px #ff6b6b;
}

.electron-orbit {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.orbit-1 {
    width: 60px;
    height: 60px;
    animation: rotate 2s linear infinite;
}

.orbit-2 {
    width: 100px;
    height: 100px;
    animation: rotate 3s linear infinite reverse;
}

.orbit-3 {
    width: 140px;
    height: 140px;
    animation: rotate 4s linear infinite;
}

.electron {
    width: 8px;
    height: 8px;
    background: #4ecdc4;
    border-radius: 50%;
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 10px #4ecdc4;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Concepts Section */
.concepts-section {
    padding: 60px 20px;
    background: #f8f9fa;
}

.concepts-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.concept-card {
    background: white;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.concept-card:hover {
    transform: translateY(-5px);
}

.concept-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.concept-header h3 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.concept-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.concept-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.concept-content.expanded {
    max-height: 1000px;
}

.concept-content > div {
    padding: 20px;
}

.concept-explanation {
    border-bottom: 1px solid #eee;
}

.concept-explanation p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.formula-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.formula-box strong {
    display: block;
    font-size: 1.3rem;
    color: #667eea;
    margin-bottom: 5px;
}

.formula-box span {
    color: #666;
    font-size: 0.9rem;
}

/* Simulations */
.concept-simulation {
    background: #f8f9fa;
}

.current-demo, .voltage-demo, .resistance-demo, .power-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.wire {
    width: 300px;
    height: 10px;
    background: #ddd;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.electron-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.electron-flow .electron {
    width: 8px;
    height: 8px;
    background: #feca57;
    border-radius: 50%;
    position: absolute;
    top: 1px;
    animation: flow 2s linear infinite;
}

.electron-flow .electron:nth-child(1) { animation-delay: 0s; }
.electron-flow .electron:nth-child(2) { animation-delay: 0.5s; }
.electron-flow .electron:nth-child(3) { animation-delay: 1s; }
.electron-flow .electron:nth-child(4) { animation-delay: 1.5s; }

@keyframes flow {
    from { left: -10px; }
    to { left: 100%; }
}

.current-controls, .voltage-controls, .resistance-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.current-controls input, .voltage-controls input, .resistance-controls input {
    width: 200px;
}

/* Battery Simulation */
.battery {
    display: flex;
    align-items: center;
    gap: 5px;
}

.battery-terminal {
    width: 20px;
    height: 40px;
    background: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.battery-terminal.positive {
    background: #ff6b6b;
}

.battery-terminal.negative {
    background: #333;
}

.battery-body {
    width: 100px;
    height: 60px;
    background: linear-gradient(90deg, #ff6b6b 50%, #ddd 50%);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.voltage-indicator {
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Resistor Simulation */
.resistor {
    display: flex;
    align-items: center;
    gap: 10px;
}

.resistor-body {
    width: 80px;
    height: 30px;
    background: #f4e4bc;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;
}

.color-band {
    width: 8px;
    height: 100%;
    border-radius: 2px;
}

.resistor-leads {
    width: 20px;
    height: 2px;
    background: #666;
}

.resistor-leads::before,
.resistor-leads::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #666;
}

.resistor-leads::before {
    left: -30px;
}

.resistor-leads::after {
    right: -30px;
}

/* Light Bulb Simulation */
.light-bulb {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bulb-glass {
    width: 60px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    border: 2px solid #ddd;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filament {
    width: 30px;
    height: 30px;
    border: 2px solid #666;
    border-radius: 50%;
    position: relative;
}

.filament::before,
.filament::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #666;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.filament::after {
    transform: translate(-50%, -50%) rotate(90deg);
}

.glow {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 255, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bulb-base {
    width: 30px;
    height: 20px;
    background: #666;
    border-radius: 0 0 5px 5px;
}

.power-display {
    margin-top: 20px;
}

.power-meter {
    background: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
}

/* Quiz Section */
.quiz-section {
    padding: 60px 20px;
    background: white;
}

.quiz-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.quiz-container {
    max-width: 800px;
    margin: 0 auto;
}

.quiz-question {
    display: none;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.quiz-question.active {
    display: block;
}

.quiz-question h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.3rem;
}

.quiz-options {
    display: grid;
    gap: 10px;
    margin-bottom: 20px;
}

.quiz-option {
    padding: 15px;
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 1rem;
}

.quiz-option:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.quiz-option.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.quiz-option.correct {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.1);
}

.quiz-option.wrong {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.quiz-option:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.quiz-feedback {
    padding: 10px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quiz-feedback.correct {
    background: rgba(78, 205, 196, 0.1);
    color: #2d7d32;
}

.quiz-feedback.wrong {
    background: rgba(255, 107, 107, 0.1);
    color: #c62828;
}

.quiz-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.question-counter {
    font-weight: 600;
    color: #666;
}

.quiz-results {
    text-align: center;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
}

.quiz-results h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.8rem;
}

.score-display {
    margin-bottom: 20px;
}

.score {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

/* Next Steps */
.next-steps {
    padding: 60px 20px;
    background: #f8f9fa;
}

.next-steps h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.next-modules {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.next-module-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.next-module-card:hover {
    transform: translateY(-5px);
}

.next-module-card h3 {
    margin-bottom: 15px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.next-module-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .intro-section {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .intro-content h2 {
        font-size: 2rem;
    }
    
    .module-nav .nav-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .progress-indicator {
        order: -1;
    }
    
    .concept-content > div {
        padding: 15px;
    }
    
    .wire {
        width: 250px;
    }
    
    .next-modules {
        grid-template-columns: 1fr;
    }
}
