/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.main-header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.nav-logo i {
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #fff;
    color: #667eea;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-secondary:hover {
    background: #fff;
    color: #667eea;
}

/* Hero Animation */
.hero-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.circuit-board {
    width: 300px;
    height: 300px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.component {
    position: absolute;
    border-radius: 8px;
    animation: pulse 2s infinite;
}

.resistor {
    width: 60px;
    height: 20px;
    background: #ff6b6b;
    top: 50px;
    left: 50px;
}

.capacitor {
    width: 30px;
    height: 40px;
    background: #4ecdc4;
    top: 100px;
    right: 50px;
}

.ic {
    width: 80px;
    height: 60px;
    background: #45b7d1;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
}

.wire {
    position: absolute;
    background: #feca57;
    animation: flow 3s infinite;
}

.wire-1 {
    width: 100px;
    height: 3px;
    top: 60px;
    left: 110px;
}

.wire-2 {
    width: 3px;
    height: 80px;
    top: 120px;
    right: 65px;
}

.wire-3 {
    width: 120px;
    height: 3px;
    bottom: 80px;
    left: 50px;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes flow {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

/* Learning Path Section */
.learning-path {
    padding: 80px 20px;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.section-title i {
    color: #667eea;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.learning-levels {
    display: grid;
    gap: 2rem;
}

.level-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.level-card:hover {
    transform: translateY(-5px);
}

.level-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.level-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.level-header h3 {
    font-size: 1.8rem;
    color: #333;
    flex: 1;
}

.level-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.beginner { background: #e8f5e8; color: #2e7d32; }
.intermediate { background: #fff3e0; color: #f57c00; }
.advanced { background: #fce4ec; color: #c2185b; }
.expert { background: #e3f2fd; color: #1976d2; }

.level-content p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.topic-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.topic-item:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.topic-item i {
    font-size: 1.2rem;
    color: #667eea;
}

.topic-item:hover i {
    color: white;
}

.level-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

/* Simulator Section */
.simulator-section {
    padding: 80px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.simulator-section .section-title {
    color: white;
}

.simulator-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.simulator-preview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-top: 3rem;
}

.simulator-screen {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.simulator-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.tool-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-btn:hover,
.tool-btn.active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.simulator-canvas {
    height: 300px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.demo-circuit {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 30px;
}

.demo-component {
    width: 40px;
    height: 20px;
    border-radius: 5px;
    animation: pulse 2s infinite;
}

.demo-component.battery {
    background: #ff6b6b;
    width: 30px;
    height: 40px;
}

.demo-component.resistor {
    background: #4ecdc4;
    width: 60px;
}

.demo-component.led {
    background: #feca57;
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.demo-wire {
    width: 50px;
    height: 3px;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 20%;
    animation: flow 2s infinite;
}

.simulator-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.simulator-info ul {
    list-style: none;
    margin-bottom: 2rem;
}

.simulator-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.simulator-info i {
    color: #4ecdc4;
}

/* Features Section */
.features {
    padding: 80px 20px;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.main-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #667eea;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Enhanced Navigation Styles */
.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100vw;
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 999;
}

.mega-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
}

.mega-menu-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mega-menu-section ul {
    list-style: none;
    padding: 0;
}

.mega-menu-section li {
    margin-bottom: 12px;
}

.mega-menu-section a {
    text-decoration: none;
    color: #666;
    padding: 8px 0;
    display: block;
    transition: color 0.3s ease;
    border-radius: 5px;
    padding-left: 10px;
}

.mega-menu-section a:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.mega-menu-section.featured {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    color: white;
}

.mega-menu-section.featured h3 {
    color: white;
}

.featured-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.featured-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.featured-content h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.featured-content p {
    margin: 0 0 10px 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.featured-btn {
    background: white;
    color: #667eea;
    padding: 6px 12px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.featured-btn:hover {
    background: #f0f0f0;
    transform: translateY(-1px);
}

/* Search Overlay */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.search-container {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 600px;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.search-input {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 50px;
    font-size: 1.1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: #667eea;
}

.search-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-close:hover {
    background: #f0f0f0;
    color: #333;
}

.search-results {
    margin-top: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.search-result-item {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.search-result-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
    transform: translateY(-2px);
}

.result-category {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.result-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.result-description {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.no-results {
    text-align: center;
    padding: 40px;
    color: #999;
    font-style: italic;
}

/* Navigation Actions */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.search-btn, .theme-toggle {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.search-btn:hover, .theme-toggle:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .topics-grid {
        grid-template-columns: 1fr;
    }

    .level-header {
        flex-direction: column;
        text-align: center;
    }

    .simulator-preview {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .mega-menu {
        position: static;
        width: 100%;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        background: #f8f9fa;
        margin-top: 10px;
    }

    .mega-menu-content {
        grid-template-columns: 1fr;
        padding: 20px;
        gap: 20px;
    }

    .search-container {
        width: 95%;
        padding: 20px;
    }

    .nav-actions {
        gap: 10px;
    }
}
