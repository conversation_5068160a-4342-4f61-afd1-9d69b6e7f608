/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.main-header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.nav-logo i {
    font-size: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #667eea;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #667eea;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-text h2 {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: #fff;
    color: #667eea;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #fff;
}

.btn-secondary:hover {
    background: #fff;
    color: #667eea;
}

/* Hero Animation */
.hero-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.circuit-board {
    width: 300px;
    height: 300px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.component {
    position: absolute;
    border-radius: 8px;
    animation: pulse 2s infinite;
}

.resistor {
    width: 60px;
    height: 20px;
    background: #ff6b6b;
    top: 50px;
    left: 50px;
}

.capacitor {
    width: 30px;
    height: 40px;
    background: #4ecdc4;
    top: 100px;
    right: 50px;
}

.ic {
    width: 80px;
    height: 60px;
    background: #45b7d1;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
}

.wire {
    position: absolute;
    background: #feca57;
    animation: flow 3s infinite;
}

.wire-1 {
    width: 100px;
    height: 3px;
    top: 60px;
    left: 110px;
}

.wire-2 {
    width: 3px;
    height: 80px;
    top: 120px;
    right: 65px;
}

.wire-3 {
    width: 120px;
    height: 3px;
    bottom: 80px;
    left: 50px;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes flow {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

/* Learning Path Section */
.learning-path {
    padding: 80px 20px;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.section-title i {
    color: #667eea;
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.learning-levels {
    display: grid;
    gap: 2rem;
}

.level-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.level-card:hover {
    transform: translateY(-5px);
}

.level-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.level-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.level-header h3 {
    font-size: 1.8rem;
    color: #333;
    flex: 1;
}

.level-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.beginner { background: #e8f5e8; color: #2e7d32; }
.intermediate { background: #fff3e0; color: #f57c00; }
.advanced { background: #fce4ec; color: #c2185b; }
.expert { background: #e3f2fd; color: #1976d2; }

.level-content p {
    color: #666;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.topic-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.topic-item:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.topic-item i {
    font-size: 1.2rem;
    color: #667eea;
}

.topic-item:hover i {
    color: white;
}

.level-progress {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

/* Simulator Section */
.simulator-section {
    padding: 80px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.simulator-section .section-title {
    color: white;
}

.simulator-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.simulator-preview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-top: 3rem;
}

.simulator-screen {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.simulator-toolbar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
}

.tool-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tool-btn:hover,
.tool-btn.active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.simulator-canvas {
    height: 300px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.demo-circuit {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 30px;
}

.demo-component {
    width: 40px;
    height: 20px;
    border-radius: 5px;
    animation: pulse 2s infinite;
}

.demo-component.battery {
    background: #ff6b6b;
    width: 30px;
    height: 40px;
}

.demo-component.resistor {
    background: #4ecdc4;
    width: 60px;
}

.demo-component.led {
    background: #feca57;
    width: 30px;
    height: 30px;
    border-radius: 50%;
}

.demo-wire {
    width: 50px;
    height: 3px;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 20%;
    animation: flow 2s infinite;
}

.simulator-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.simulator-info ul {
    list-style: none;
    margin-bottom: 2rem;
}

.simulator-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.simulator-info i {
    color: #4ecdc4;
}

/* Features Section */
.features {
    padding: 80px 20px;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: #f8f9fa;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.feature-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.main-footer {
    background: #2c3e50;
    color: white;
    padding: 40px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #667eea;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .topics-grid {
        grid-template-columns: 1fr;
    }

    .level-header {
        flex-direction: column;
        text-align: center;
    }

    .simulator-preview {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .presentations-grid {
        grid-template-columns: 1fr;
    }

    .presentation-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Presentations Showcase Section */
.presentations-showcase {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
    overflow: hidden;
}

.presentations-showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.presentations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.presentation-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.presentation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.presentation-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
}

.presentation-preview {
    margin-bottom: 1.5rem;
}

.preview-screen {
    background: #1a1a2e;
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.preview-screen::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 15px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ff5f56;
    box-shadow: 20px 0 0 #ffbd2e, 40px 0 0 #27ca3f;
}

/* Animated Circuit Elements */
.animated-circuit {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.circuit-element {
    width: 60px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid #4facfe;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

.circuit-element.battery {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.circuit-element.resistor {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.voltage-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 10px;
    height: 10px;
    background: #4facfe;
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

.heat-indicator {
    position: absolute;
    bottom: -5px;
    right: -5px;
    width: 8px;
    height: 8px;
    background: #ff6b6b;
    border-radius: 50%;
    animation: heat 2s ease-in-out infinite;
}

.circuit-wire {
    width: 40px;
    height: 3px;
    background: #4facfe;
    position: relative;
    border-radius: 2px;
}

.current-flow {
    position: absolute;
    top: 50%;
    left: 0;
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 50%;
    transform: translateY(-50%);
    animation: currentFlow 2s linear infinite;
}

/* Interactive Calculator */
.interactive-calculator {
    color: white;
    text-align: center;
}

.calc-display {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #4facfe;
}

.calc-inputs {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.input-field {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-field label {
    color: #ccc;
    font-weight: 500;
}

.input-field input {
    width: 60px;
    padding: 0.25rem 0.5rem;
    border: 1px solid #4facfe;
    border-radius: 4px;
    background: rgba(79, 172, 254, 0.1);
    color: white;
    text-align: center;
}

/* Medical Device */
.medical-device {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: white;
}

.device-icon {
    font-size: 2rem;
    color: #4facfe;
    animation: heartbeat 1.5s ease-in-out infinite;
}

.signal-wave {
    width: 100%;
    height: 40px;
}

/* Presentation Info */
.presentation-info h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.75rem;
}

.presentation-info p {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.presentation-features {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Presentation Stats */
.presentation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    position: relative;
    z-index: 1;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #4a5568;
    font-weight: 600;
    font-size: 1rem;
}

/* Animations */
@keyframes currentFlow {
    0% { left: 0; opacity: 1; }
    50% { opacity: 0.5; }
    100% { left: calc(100% - 8px); opacity: 1; }
}

@keyframes heat {
    0%, 100% {
        transform: scale(1);
        background: #ff6b6b;
    }
    50% {
        transform: scale(1.3);
        background: #ff9999;
    }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
