// Main JavaScript for Virtual Electronics Lab

// Dynamic Navigation System
class NavigationManager {
    constructor() {
        this.currentPage = this.getCurrentPage();
        this.navigationHistory = [];
        this.breadcrumbs = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeMegaMenu();
        this.initializeSearch();
        this.initializeTheme();
        this.updateBreadcrumbs();
        this.trackPageVisit();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return filename.replace('.html', '');
    }

    setupEventListeners() {
        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        if (hamburger) {
            hamburger.addEventListener('click', () => this.toggleMobileMenu());
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => this.handleAnchorClick(e));
        });

        // Handle back button
        window.addEventListener('popstate', (e) => this.handlePopState(e));

        // Track external links
        document.querySelectorAll('a[href^="http"]').forEach(link => {
            link.addEventListener('click', (e) => this.trackExternalLink(e));
        });
    }

    toggleMobileMenu() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');

        // Close mega menus when mobile menu opens
        document.querySelectorAll('.mega-menu').forEach(menu => {
            menu.classList.remove('active');
        });
    }

    handleAnchorClick(e) {
        const targetId = e.target.getAttribute('href');
        const targetSection = document.querySelector(targetId);

        if (targetSection) {
            e.preventDefault();
            targetSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Update URL without page reload
            history.pushState(null, null, targetId);

            // Close mobile menu if open
            this.closeMobileMenu();
        }
    }

    closeMobileMenu() {
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        if (navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            hamburger.classList.remove('active');
        }
    }

    initializeMegaMenu() {
        const megaMenuItems = document.querySelectorAll('.mega-menu-item');

        megaMenuItems.forEach(item => {
            const trigger = item.querySelector('.nav-link');
            const menu = item.querySelector('.mega-menu');

            if (trigger && menu) {
                // Desktop hover
                item.addEventListener('mouseenter', () => {
                    menu.classList.add('active');
                    this.closeOtherMegaMenus(menu);
                });

                item.addEventListener('mouseleave', () => {
                    setTimeout(() => {
                        if (!item.matches(':hover')) {
                            menu.classList.remove('active');
                        }
                    }, 100);
                });

                // Mobile click
                trigger.addEventListener('click', (e) => {
                    if (window.innerWidth <= 768) {
                        e.preventDefault();
                        menu.classList.toggle('active');
                        this.closeOtherMegaMenus(menu);
                    }
                });
            }
        });

        // Close mega menus when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.mega-menu-item')) {
                this.closeAllMegaMenus();
            }
        });
    }

    closeOtherMegaMenus(currentMenu) {
        document.querySelectorAll('.mega-menu').forEach(menu => {
            if (menu !== currentMenu) {
                menu.classList.remove('active');
            }
        });
    }

    closeAllMegaMenus() {
        document.querySelectorAll('.mega-menu').forEach(menu => {
            menu.classList.remove('active');
        });
    }

    initializeSearch() {
        const searchBtn = document.querySelector('.search-btn');
        const searchOverlay = document.querySelector('#searchOverlay');
        const searchInput = document.querySelector('#searchInput');
        const searchResults = document.querySelector('#searchResults');

        if (searchBtn && searchOverlay) {
            searchBtn.addEventListener('click', () => this.toggleSearch());

            // Close search with escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && searchOverlay.classList.contains('active')) {
                    this.toggleSearch();
                }
            });

            // Search functionality
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    this.performSearch(e.target.value, searchResults);
                });
            }
        }
    }

    toggleSearch() {
        const searchOverlay = document.querySelector('#searchOverlay');
        const searchInput = document.querySelector('#searchInput');

        if (searchOverlay) {
            searchOverlay.classList.toggle('active');

            if (searchOverlay.classList.contains('active')) {
                searchInput?.focus();
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
                searchInput.value = '';
                this.clearSearchResults();
            }
        }
    }

    performSearch(query, resultsContainer) {
        if (!query.trim() || !resultsContainer) return;

        const searchData = this.getSearchData();
        const results = searchData.filter(item =>
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase()) ||
            item.keywords.some(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
        );

        this.displaySearchResults(results, resultsContainer);
    }

    getSearchData() {
        return [
            // Fundamentals
            { title: 'Basic Electronics Concepts', description: 'Learn fundamental electronics principles', url: 'html/presentations/basic-concepts-presentation.html', category: 'Fundamentals', keywords: ['voltage', 'current', 'resistance', 'basics'] },
            { title: "Ohm's Law", description: 'Master voltage, current, and resistance relationships', url: 'html/presentations/ohms-law-presentation.html', category: 'Fundamentals', keywords: ['ohm', 'voltage', 'current', 'resistance', 'calculation'] },
            { title: 'Passive Components', description: 'Resistors, capacitors, and inductors', url: 'html/presentations/passive-components-presentation.html', category: 'Fundamentals', keywords: ['resistor', 'capacitor', 'inductor', 'passive'] },

            // Semiconductor Devices
            { title: 'BJT Transistors', description: 'Bipolar Junction Transistor fundamentals', url: 'html/level2/bjt-transistors.html', category: 'Semiconductors', keywords: ['bjt', 'transistor', 'amplifier', 'switching'] },
            { title: 'Diodes', description: 'Diode characteristics and applications', url: 'html/level2/diodes.html', category: 'Semiconductors', keywords: ['diode', 'rectifier', 'forward', 'reverse'] },

            // Medical Electronics
            { title: 'Medical Device Circuits', description: 'Electronics in medical devices', url: 'html/level3/medical-device-circuits.html', category: 'Medical', keywords: ['medical', 'biomedical', 'patient', 'monitoring'] },
            { title: 'Biosignal Processing', description: 'Processing biological signals', url: 'html/level3/biosignal-processing.html', category: 'Medical', keywords: ['biosignal', 'ecg', 'eeg', 'signal'] },

            // Tools
            { title: 'Circuit Simulator', description: 'Virtual circuit design and simulation', url: 'simulator.html', category: 'Tools', keywords: ['simulator', 'circuit', 'design', 'virtual'] },
            { title: 'Electronics Fundamentals Course', description: 'Complete fundamentals course', url: 'html/courses/fundamentals.html', category: 'Courses', keywords: ['course', 'fundamentals', 'complete'] }
        ];
    }

    displaySearchResults(results, container) {
        if (results.length === 0) {
            container.innerHTML = '<div class="no-results">No results found. Try different keywords.</div>';
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" onclick="navigateToPage('${result.url}')">
                <div class="result-category">${result.category}</div>
                <div class="result-title">${result.title}</div>
                <div class="result-description">${result.description}</div>
            </div>
        `).join('');

        container.innerHTML = resultsHTML;
    }

    clearSearchResults() {
        const searchResults = document.querySelector('#searchResults');
        if (searchResults) {
            searchResults.innerHTML = '';
        }
    }

    initializeTheme() {
        const themeToggle = document.querySelector('.theme-toggle');
        const savedTheme = localStorage.getItem('theme') || 'light';

        this.setTheme(savedTheme);

        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
    }

    toggleTheme() {
        const currentTheme = document.body.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }

    setTheme(theme) {
        document.body.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);

        const themeIcon = document.querySelector('.theme-toggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    updateBreadcrumbs() {
        const breadcrumbContainer = document.querySelector('.breadcrumbs');
        if (!breadcrumbContainer) return;

        const pathSegments = window.location.pathname.split('/').filter(segment => segment);
        const breadcrumbs = ['Home'];

        // Build breadcrumb path
        let currentPath = '';
        pathSegments.forEach(segment => {
            currentPath += '/' + segment;
            if (segment !== 'index.html') {
                breadcrumbs.push(this.formatBreadcrumbName(segment));
            }
        });

        this.renderBreadcrumbs(breadcrumbs, breadcrumbContainer);
    }

    formatBreadcrumbName(segment) {
        const nameMap = {
            'html': 'Content',
            'level1': 'Fundamentals',
            'level2': 'Semiconductors',
            'level3': 'Medical Electronics',
            'level4': 'Advanced',
            'courses': 'Courses',
            'presentations': 'Presentations'
        };

        return nameMap[segment] || segment.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    renderBreadcrumbs(breadcrumbs, container) {
        const breadcrumbHTML = breadcrumbs.map((crumb, index) => {
            const isLast = index === breadcrumbs.length - 1;
            return `
                <span class="breadcrumb-item ${isLast ? 'active' : ''}" ${!isLast ? `onclick="navigateBack(${index})"` : ''}>
                    ${crumb}
                </span>
                ${!isLast ? '<i class="fas fa-chevron-right breadcrumb-separator"></i>' : ''}
            `;
        }).join('');

        container.innerHTML = breadcrumbHTML;
    }

    trackPageVisit() {
        const pageData = {
            page: this.currentPage,
            timestamp: new Date().toISOString(),
            referrer: document.referrer
        };

        // Store in navigation history
        this.navigationHistory.push(pageData);

        // Limit history to last 50 pages
        if (this.navigationHistory.length > 50) {
            this.navigationHistory.shift();
        }

        // Save to localStorage
        localStorage.setItem('navigationHistory', JSON.stringify(this.navigationHistory));
    }

    handlePopState(e) {
        // Handle browser back/forward buttons
        this.currentPage = this.getCurrentPage();
        this.updateBreadcrumbs();
    }

    trackExternalLink(e) {
        const url = e.target.href;
        console.log('External link clicked:', url);

        // Track external link analytics here if needed
        // Analytics.track('external_link_click', { url });
    }
}

// Initialize Navigation Manager
document.addEventListener('DOMContentLoaded', function() {
    window.navManager = new NavigationManager();

    // Initialize other components
    initializeProgress();
    initializeAnimations();
    initializeInteractiveElements();
});

// Module navigation function
function openModule(moduleId) {
    const moduleMap = {
        // Level 1 - Fundamentals
        'basic-concepts': 'html/level1/basic-concepts-new.html',
        'ohms-law': 'html/level1/ohms-law.html',
        'passive-components': 'html/level1/passive-components.html',
        'circuit-analysis': 'html/level1/circuit-analysis.html',

        // Level 2 - Semiconductor Devices
        'diodes': 'html/level2/diodes.html',
        'bjt': 'html/level2/bjt-transistors.html',
        'fet': 'html/level2/fet-transistors.html',
        'opamps': 'html/level2/operational-amplifiers.html',

        // Level 3 - Medical Electronics
        'biosignals': 'html/level3/biosignal-processing.html',
        'medical-devices': 'html/level3/medical-device-circuits.html',
        'safety': 'html/level3/safety-standards.html',
        'sensors': 'html/level3/medical-sensors.html',

        // Level 4 - Advanced Applications
        'digital-electronics': 'html/level4/digital-electronics.html',
        'microcontrollers': 'html/level4/microcontrollers.html',
        'advanced-circuits': 'html/level4/advanced-circuits.html',
        'project-design': 'html/level4/project-design.html'
    };

    const moduleUrl = moduleMap[moduleId];
    if (moduleUrl) {
        // Check if file exists, if not, show coming soon message
        fetch(moduleUrl, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    window.open(moduleUrl, '_blank');
                } else {
                    showComingSoonModal(moduleId);
                }
            })
            .catch(() => {
                showComingSoonModal(moduleId);
            });
    } else {
        showComingSoonModal(moduleId);
    }
}

// Open Electronics Fundamentals main page
function openFundamentals() {
    window.open('html/level1/electronics-fundamentals.html', '_blank');
}

// Show coming soon modal
function showComingSoonModal(moduleId) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-construction"></i> Module Coming Soon</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>The <strong>${formatModuleName(moduleId)}</strong> module is currently under development.</p>
                <p>It will be available soon with interactive simulations and comprehensive learning materials.</p>
                <div class="modal-features">
                    <h4>What to expect:</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> Interactive circuit simulations</li>
                        <li><i class="fas fa-check"></i> Step-by-step tutorials</li>
                        <li><i class="fas fa-check"></i> Practice exercises</li>
                        <li><i class="fas fa-check"></i> Real-world applications</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">Got it!</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add modal styles if not already present
    if (!document.querySelector('#modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'modal-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                animation: slideIn 0.3s ease;
            }
            
            .modal-header {
                padding: 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-header h3 {
                margin: 0;
                color: #667eea;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .modal-features {
                margin-top: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            
            .modal-features h4 {
                margin: 0 0 10px 0;
                color: #333;
            }
            
            .modal-features ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            
            .modal-features li {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 8px;
            }
            
            .modal-features i {
                color: #4ecdc4;
            }
            
            .modal-footer {
                padding: 20px;
                border-top: 1px solid #eee;
                text-align: center;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Format module name for display
function formatModuleName(moduleId) {
    const names = {
        'basic-concepts': 'Basic Electronics Concepts',
        'ohms-law': "Ohm's Law & Circuit Analysis",
        'passive-components': 'Passive Components',
        'circuit-analysis': 'Circuit Analysis',
        'diodes': 'Diodes & Applications',
        'bjt': 'BJT Transistors',
        'fet': 'FET Transistors',
        'opamps': 'Operational Amplifiers',
        'biosignals': 'Biosignal Processing',
        'medical-devices': 'Medical Device Circuits',
        'safety': 'Safety & Standards',
        'sensors': 'Medical Sensors',
        'digital-electronics': 'Digital Electronics',
        'microcontrollers': 'Microcontrollers',
        'advanced-circuits': 'Advanced Circuit Design',
        'project-design': 'Project Design'
    };
    return names[moduleId] || moduleId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Initialize progress tracking
function initializeProgress() {
    // Load progress from localStorage
    const savedProgress = localStorage.getItem('learningProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {};
    
    // Update progress bars
    updateProgressBars(progress);
}

// Update progress bars
function updateProgressBars(progress) {
    const levels = document.querySelectorAll('.level-card');
    
    levels.forEach(level => {
        const levelNum = level.dataset.level;
        const progressBar = level.querySelector('.progress-fill');
        const progressText = level.querySelector('.progress-text');
        
        const levelProgress = progress[`level${levelNum}`] || 0;
        
        if (progressBar && progressText) {
            progressBar.style.width = `${levelProgress}%`;
            progressText.textContent = `${levelProgress}% Complete`;
        }
    });
}

// Initialize animations
function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.level-card, .feature-card');
    animateElements.forEach(el => observer.observe(el));
    
    // Add animation styles
    const animationStyles = document.createElement('style');
    animationStyles.textContent = `
        .level-card, .feature-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .level-card.animate-in, .feature-card.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    document.head.appendChild(animationStyles);
}

// Utility function to update learning progress
function updateProgress(level, moduleId, completed = true) {
    const savedProgress = localStorage.getItem('learningProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {};
    
    if (!progress[`level${level}`]) {
        progress[`level${level}`] = {};
    }
    
    progress[`level${level}`][moduleId] = completed;
    
    // Calculate level completion percentage
    const levelModules = Object.keys(progress[`level${level}`]);
    const completedModules = levelModules.filter(module => progress[`level${level}`][module]);
    const completionPercentage = Math.round((completedModules.length / 4) * 100); // 4 modules per level
    
    progress[`level${level}Percentage`] = completionPercentage;
    
    localStorage.setItem('learningProgress', JSON.stringify(progress));
    updateProgressBars(progress);
}

// Global Navigation Functions
function navigateToPage(url, options = {}) {
    if (options.newTab !== false) {
        window.open(url, '_blank');
    } else {
        window.location.href = url;
    }
}

function navigateBack(steps = 1) {
    window.history.go(-steps);
}

function openPresentation() {
    navigateToPage('html/presentations/fundamentals-presentation.html');
}

function openSimulator() {
    navigateToPage('simulator.html');
}

function openTool(toolId) {
    const toolUrls = {
        'ohms-calculator': 'tools/ohms-calculator.html',
        'power-calculator': 'tools/power-calculator.html',
        'resistor-calculator': 'tools/resistor-calculator.html',
        'capacitor-calculator': 'tools/capacitor-calculator.html',
        'frequency-calculator': 'tools/frequency-calculator.html',
        'circuit-simulator': 'simulator.html',
        'logic-simulator': 'tools/logic-simulator.html',
        'filter-designer': 'tools/filter-designer.html',
        'waveform-generator': 'tools/waveform-generator.html',
        'pcb-designer': 'tools/pcb-designer.html',
        'bode-plotter': 'tools/bode-plotter.html',
        'fft-analyzer': 'tools/fft-analyzer.html',
        'noise-analyzer': 'tools/noise-analyzer.html',
        'stability-analyzer': 'tools/stability-analyzer.html',
        'distortion-analyzer': 'tools/distortion-analyzer.html'
    };

    const toolUrl = toolUrls[toolId];
    if (toolUrl) {
        // Check if tool exists
        fetch(toolUrl, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    navigateToPage(toolUrl);
                } else {
                    showComingSoonModal(toolId);
                }
            })
            .catch(() => {
                showComingSoonModal(toolId);
            });
    } else {
        showComingSoonModal(toolId);
    }
}

// Initialize interactive elements
function initializeInteractiveElements() {
    // Add click handlers for topic items
    document.querySelectorAll('.topic-item').forEach(item => {
        item.addEventListener('click', function() {
            this.classList.add('clicked');
            setTimeout(() => {
                this.classList.remove('clicked');
            }, 200);
        });
    });

    // Add hover effects for cards
    document.querySelectorAll('.level-card, .feature-card, .presentation-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Initialize tooltips
    initializeTooltips();
}

function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');

    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', function(e) {
            showTooltip(e.target, e.target.getAttribute('data-tooltip'));
        });

        element.addEventListener('mouseleave', function() {
            hideTooltip();
        });
    });
}

function showTooltip(element, text) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';

    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);

    // Store reference for cleanup
    element._tooltip = tooltip;
}

function hideTooltip() {
    const tooltips = document.querySelectorAll('.tooltip');
    tooltips.forEach(tooltip => {
        tooltip.style.opacity = '0';
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        }, 300);
    });
}

// Enhanced search functionality
function toggleSearch() {
    if (window.navManager) {
        window.navManager.toggleSearch();
    }
}

function toggleTheme() {
    if (window.navManager) {
        window.navManager.toggleTheme();
    }
}

function toggleMobileMenu() {
    if (window.navManager) {
        window.navManager.toggleMobileMenu();
    }
}

// Export functions for use in other modules
window.openModule = openModule;
window.openFundamentals = openFundamentals;
window.closeModal = closeModal;
window.updateProgress = updateProgress;
window.navigateToPage = navigateToPage;
window.navigateBack = navigateBack;
window.openPresentation = openPresentation;
window.openSimulator = openSimulator;
window.openTool = openTool;
window.toggleSearch = toggleSearch;
window.toggleTheme = toggleTheme;
window.toggleMobileMenu = toggleMobileMenu;
