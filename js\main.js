// Main JavaScript for Virtual Electronics Lab

// Navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
            
            // Close mobile menu if open
            if (navMenu.classList.contains('active')) {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            }
        });
    });

    // Initialize progress tracking
    initializeProgress();
    
    // Initialize animations
    initializeAnimations();
});

// Module navigation function
function openModule(moduleId) {
    const moduleMap = {
        // Level 1 - Fundamentals
        'basic-concepts': 'html/presentations/basic-concepts-presentation.html',
        'ohms-law': 'html/presentations/ohms-law-presentation.html',
        'passive-components': 'html/presentations/passive-components-presentation.html',
        'circuit-analysis': 'html/level1/circuit-analysis.html',

        // Level 2 - Semiconductor Devices
        'diodes': 'html/level2/diodes.html',
        'bjt': 'html/level2/bjt-transistors.html',
        'fet': 'html/level2/fet-transistors.html',
        'opamps': 'html/level2/operational-amplifiers.html',

        // Level 3 - Medical Electronics
        'biosignals': 'html/level3/biosignal-processing.html',
        'medical-devices': 'html/level3/medical-device-circuits.html',
        'safety': 'html/level3/safety-standards.html',
        'sensors': 'html/level3/medical-sensors.html',

        // Level 4 - Advanced Applications
        'digital-electronics': 'html/level4/digital-electronics.html',
        'microcontrollers': 'html/level4/microcontrollers.html',
        'advanced-circuits': 'html/level4/advanced-circuits.html',
        'project-design': 'html/level4/project-design.html'
    };

    const moduleUrl = moduleMap[moduleId];
    if (moduleUrl) {
        // Check if file exists, if not, show coming soon message
        fetch(moduleUrl, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    window.open(moduleUrl, '_blank');
                } else {
                    showComingSoonModal(moduleId);
                }
            })
            .catch(() => {
                showComingSoonModal(moduleId);
            });
    } else {
        showComingSoonModal(moduleId);
    }
}

// Open Electronics Fundamentals main page
function openFundamentals() {
    window.open('html/level1/electronics-fundamentals.html', '_blank');
}

// Show coming soon modal
function showComingSoonModal(moduleId) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-construction"></i> Module Coming Soon</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>The <strong>${formatModuleName(moduleId)}</strong> module is currently under development.</p>
                <p>It will be available soon with interactive simulations and comprehensive learning materials.</p>
                <div class="modal-features">
                    <h4>What to expect:</h4>
                    <ul>
                        <li><i class="fas fa-check"></i> Interactive circuit simulations</li>
                        <li><i class="fas fa-check"></i> Step-by-step tutorials</li>
                        <li><i class="fas fa-check"></i> Practice exercises</li>
                        <li><i class="fas fa-check"></i> Real-world applications</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal()">Got it!</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add modal styles if not already present
    if (!document.querySelector('#modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'modal-styles';
        styles.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }
            
            .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                animation: slideIn 0.3s ease;
            }
            
            .modal-header {
                padding: 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .modal-header h3 {
                margin: 0;
                color: #667eea;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            }
            
            .modal-body {
                padding: 20px;
            }
            
            .modal-features {
                margin-top: 20px;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            
            .modal-features h4 {
                margin: 0 0 10px 0;
                color: #333;
            }
            
            .modal-features ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            
            .modal-features li {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 8px;
            }
            
            .modal-features i {
                color: #4ecdc4;
            }
            
            .modal-footer {
                padding: 20px;
                border-top: 1px solid #eee;
                text-align: center;
            }
            
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Format module name for display
function formatModuleName(moduleId) {
    const names = {
        'basic-concepts': 'Basic Electronics Concepts',
        'ohms-law': "Ohm's Law & Circuit Analysis",
        'passive-components': 'Passive Components',
        'circuit-analysis': 'Circuit Analysis',
        'diodes': 'Diodes & Applications',
        'bjt': 'BJT Transistors',
        'fet': 'FET Transistors',
        'opamps': 'Operational Amplifiers',
        'biosignals': 'Biosignal Processing',
        'medical-devices': 'Medical Device Circuits',
        'safety': 'Safety & Standards',
        'sensors': 'Medical Sensors',
        'digital-electronics': 'Digital Electronics',
        'microcontrollers': 'Microcontrollers',
        'advanced-circuits': 'Advanced Circuit Design',
        'project-design': 'Project Design'
    };
    return names[moduleId] || moduleId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Initialize progress tracking
function initializeProgress() {
    // Load progress from localStorage
    const savedProgress = localStorage.getItem('learningProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {};
    
    // Update progress bars
    updateProgressBars(progress);
}

// Update progress bars
function updateProgressBars(progress) {
    const levels = document.querySelectorAll('.level-card');
    
    levels.forEach(level => {
        const levelNum = level.dataset.level;
        const progressBar = level.querySelector('.progress-fill');
        const progressText = level.querySelector('.progress-text');
        
        const levelProgress = progress[`level${levelNum}`] || 0;
        
        if (progressBar && progressText) {
            progressBar.style.width = `${levelProgress}%`;
            progressText.textContent = `${levelProgress}% Complete`;
        }
    });
}

// Initialize animations
function initializeAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.level-card, .feature-card');
    animateElements.forEach(el => observer.observe(el));
    
    // Add animation styles
    const animationStyles = document.createElement('style');
    animationStyles.textContent = `
        .level-card, .feature-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .level-card.animate-in, .feature-card.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    document.head.appendChild(animationStyles);
}

// Utility function to update learning progress
function updateProgress(level, moduleId, completed = true) {
    const savedProgress = localStorage.getItem('learningProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {};
    
    if (!progress[`level${level}`]) {
        progress[`level${level}`] = {};
    }
    
    progress[`level${level}`][moduleId] = completed;
    
    // Calculate level completion percentage
    const levelModules = Object.keys(progress[`level${level}`]);
    const completedModules = levelModules.filter(module => progress[`level${level}`][module]);
    const completionPercentage = Math.round((completedModules.length / 4) * 100); // 4 modules per level
    
    progress[`level${level}Percentage`] = completionPercentage;
    
    localStorage.setItem('learningProgress', JSON.stringify(progress));
    updateProgressBars(progress);
}

// Export functions for use in other modules
window.openModule = openModule;
window.openFundamentals = openFundamentals;
window.closeModal = closeModal;
window.updateProgress = updateProgress;
