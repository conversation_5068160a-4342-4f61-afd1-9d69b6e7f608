// Virtual Electronics Lab - System Configuration
// معمل الإلكترونيات الافتراضي - إعدادات النظام

const SystemConfig = {
    // System Information
    version: "2.0.0",
    name: "Virtual Electronics Lab",
    nameAr: "معمل الإلكترونيات الافتراضي",
    
    // Paths Configuration
    paths: {
        base: "./",
        courses: "./html/courses/",
        modules: "./html/level1/",
        presentations: "./html/presentation/",
        css: "./css/",
        js: "./js/",
        assets: "./assets/"
    },
    
    // Course Structure
    courses: {
        fundamentals: {
            id: "fundamentals",
            title: "Electronics Fundamentals",
            titleAr: "أساسيات الإلكترونيات",
            modules: 5,
            duration: "3-4 hours",
            level: "beginner",
            path: "html/courses/fundamentals.html"
        },
        digital: {
            id: "digital-electronics", 
            title: "Digital Electronics",
            titleAr: "الإلكترونيات الرقمية",
            modules: 6,
            duration: "4-5 hours", 
            level: "intermediate",
            path: "html/courses/digital-electronics.html"
        },
        analog: {
            id: "analog-electronics",
            title: "Analog Electronics", 
            titleAr: "الإلكترونيات التناظرية",
            modules: 7,
            duration: "5-6 hours",
            level: "advanced", 
            path: "html/courses/analog-electronics.html"
        },
        biomedical: {
            id: "biomedical-applications",
            title: "Biomedical Applications",
            titleAr: "التطبيقات الطبية الحيوية", 
            modules: 8,
            duration: "6-7 hours",
            level: "expert",
            path: "html/courses/biomedical-applications.html"
        }
    },
    
    // Interactive Modules
    modules: {
        basicConcepts: {
            id: "basic-concepts",
            title: "Basic Electrical Concepts",
            titleAr: "المفاهيم الكهربائية الأساسية",
            path: "html/level1/basic-concepts-new.html",
            hasCalculator: false,
            hasQuiz: true,
            hasPresentation: true
        },
        ohmsLaw: {
            id: "ohms-law", 
            title: "Ohm's Law",
            titleAr: "قانون أوم",
            path: "html/level1/ohms-law-new.html",
            hasCalculator: true,
            hasQuiz: true,
            hasPresentation: true
        },
        passiveComponents: {
            id: "passive-components",
            title: "Passive Components", 
            titleAr: "المكونات السلبية",
            path: "html/level1/passive-components-new.html",
            hasCalculator: true,
            hasQuiz: true,
            hasPresentation: true
        },
        circuitAnalysis: {
            id: "circuit-analysis",
            title: "Circuit Analysis",
            titleAr: "تحليل الدوائر",
            path: "html/level1/circuit-analysis.html", 
            hasCalculator: false,
            hasQuiz: true,
            hasPresentation: true
        },
        solvedExamples: {
            id: "solved-examples",
            title: "Solved Examples",
            titleAr: "أمثلة محلولة", 
            path: "html/level1/solved-examples.html",
            hasCalculator: true,
            hasQuiz: false,
            hasPresentation: false
        }
    },
    
    // Tools and Calculators
    tools: {
        ohmsCalculator: {
            id: "ohms-calculator",
            title: "Ohm's Law Calculator",
            titleAr: "حاسبة قانون أوم",
            type: "calculator"
        },
        resistorCalculator: {
            id: "resistor-calculator", 
            title: "Resistor Color Code Calculator",
            titleAr: "حاسبة رموز ألوان المقاومات",
            type: "calculator"
        },
        powerCalculator: {
            id: "power-calculator",
            title: "Power Calculator", 
            titleAr: "حاسبة القدرة",
            type: "calculator"
        },
        circuitSimulator: {
            id: "circuit-simulator",
            title: "Circuit Simulator",
            titleAr: "محاكي الدوائر",
            type: "simulator",
            path: "simulator.html"
        }
    },
    
    // UI Configuration
    ui: {
        theme: {
            primary: "#667eea",
            secondary: "#764ba2", 
            success: "#28a745",
            warning: "#ffc107",
            danger: "#dc3545",
            info: "#17a2b8"
        },
        animations: {
            enabled: true,
            duration: 300,
            easing: "ease"
        },
        responsive: {
            breakpoints: {
                mobile: 768,
                tablet: 1024,
                desktop: 1200
            }
        }
    },
    
    // Features Configuration
    features: {
        progressTracking: true,
        certificates: true,
        darkMode: true,
        multiLanguage: true,
        offlineMode: false,
        analytics: true,
        search: true,
        bookmarks: true
    },
    
    // Performance Settings
    performance: {
        lazyLoading: true,
        imageOptimization: true,
        caching: true,
        compression: true,
        preloading: ["css/main.css", "js/main.js"]
    },
    
    // API Configuration (if needed)
    api: {
        baseUrl: "",
        timeout: 5000,
        retries: 3
    },
    
    // Error Handling
    errorHandling: {
        logErrors: true,
        showUserFriendlyMessages: true,
        fallbackPages: {
            404: "error-404.html",
            500: "error-500.html"
        }
    },
    
    // Analytics (if needed)
    analytics: {
        enabled: false,
        trackPageViews: true,
        trackInteractions: true,
        trackPerformance: true
    }
};

// System Status Check
const SystemStatus = {
    checkSystemHealth() {
        const checks = {
            coreFiles: this.checkCoreFiles(),
            courses: this.checkCourses(),
            modules: this.checkModules(),
            tools: this.checkTools(),
            assets: this.checkAssets()
        };
        
        return {
            overall: Object.values(checks).every(check => check.status === 'ok'),
            details: checks,
            timestamp: new Date().toISOString()
        };
    },
    
    checkCoreFiles() {
        const coreFiles = [
            'index.html',
            'simulator.html', 
            'css/main.css',
            'js/main.js'
        ];
        
        // In a real implementation, you would check if files exist
        return {
            status: 'ok',
            files: coreFiles,
            missing: []
        };
    },
    
    checkCourses() {
        const courseCount = Object.keys(SystemConfig.courses).length;
        return {
            status: courseCount >= 4 ? 'ok' : 'warning',
            count: courseCount,
            expected: 4
        };
    },
    
    checkModules() {
        const moduleCount = Object.keys(SystemConfig.modules).length;
        return {
            status: moduleCount >= 5 ? 'ok' : 'warning',
            count: moduleCount,
            expected: 5
        };
    },
    
    checkTools() {
        const toolCount = Object.keys(SystemConfig.tools).length;
        return {
            status: toolCount >= 4 ? 'ok' : 'warning',
            count: toolCount,
            expected: 4
        };
    },
    
    checkAssets() {
        return {
            status: 'ok',
            css: 'loaded',
            js: 'loaded',
            fonts: 'loaded'
        };
    }
};

// Utility Functions
const SystemUtils = {
    // Navigate to a specific module
    navigateToModule(moduleId) {
        const module = SystemConfig.modules[moduleId];
        if (module) {
            window.location.href = module.path;
        } else {
            console.error(`Module ${moduleId} not found`);
        }
    },
    
    // Navigate to a specific course
    navigateToCourse(courseId) {
        const course = SystemConfig.courses[courseId];
        if (course) {
            window.location.href = course.path;
        } else {
            console.error(`Course ${courseId} not found`);
        }
    },
    
    // Open a tool
    openTool(toolId) {
        const tool = SystemConfig.tools[toolId];
        if (tool) {
            if (tool.path) {
                window.location.href = tool.path;
            } else {
                // Handle inline tools
                console.log(`Opening tool: ${tool.title}`);
            }
        } else {
            console.error(`Tool ${toolId} not found`);
        }
    },
    
    // Get system information
    getSystemInfo() {
        return {
            name: SystemConfig.name,
            version: SystemConfig.version,
            courses: Object.keys(SystemConfig.courses).length,
            modules: Object.keys(SystemConfig.modules).length,
            tools: Object.keys(SystemConfig.tools).length,
            features: SystemConfig.features
        };
    },
    
    // Initialize system
    initialize() {
        console.log(`🚀 Initializing ${SystemConfig.name} v${SystemConfig.version}`);
        
        // Check system health
        const health = SystemStatus.checkSystemHealth();
        if (health.overall) {
            console.log('✅ System health check passed');
        } else {
            console.warn('⚠️ System health check found issues:', health.details);
        }
        
        // Initialize features
        this.initializeFeatures();
        
        console.log('🎉 System initialization complete');
        return health;
    },
    
    initializeFeatures() {
        // Initialize progress tracking
        if (SystemConfig.features.progressTracking) {
            this.initializeProgressTracking();
        }
        
        // Initialize dark mode
        if (SystemConfig.features.darkMode) {
            this.initializeDarkMode();
        }
        
        // Initialize search
        if (SystemConfig.features.search) {
            this.initializeSearch();
        }
    },
    
    initializeProgressTracking() {
        // Progress tracking implementation
        console.log('📊 Progress tracking initialized');
    },
    
    initializeDarkMode() {
        // Dark mode implementation
        console.log('🌙 Dark mode support initialized');
    },
    
    initializeSearch() {
        // Search functionality implementation
        console.log('🔍 Search functionality initialized');
    }
};

// Export for global access
if (typeof window !== 'undefined') {
    window.SystemConfig = SystemConfig;
    window.SystemStatus = SystemStatus;
    window.SystemUtils = SystemUtils;
}

// Auto-initialize on load
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        SystemUtils.initialize();
    });
}
