<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Electronics - Interactive Presentation</title>
    <link rel="stylesheet" href="../../css/presentation.css">
    <link rel="stylesheet" href="../../css/digital-presentation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="presentation-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="presentation-title">Digital Electronics</div>
            <div class="nav-controls">
                <button class="nav-btn" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" onclick="toggleAutoPlay()">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Presentation Container -->
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active" data-slide="0">
            <div class="slide-content">
                <div class="title-slide">
                    <div class="slide-header">
                        <h1>Digital Electronics</h1>
                        <h2>Logic Gates, Boolean Algebra & Digital Systems</h2>
                        <p>Master the fundamentals of digital circuits and their applications in biomedical devices</p>
                    </div>
                    <div class="slide-visual">
                        <div class="digital-showcase">
                            <div class="logic-gates-demo">
                                <div class="gate-item and-gate">
                                    <div class="gate-inputs">
                                        <div class="input-pin high" data-state="1">1</div>
                                        <div class="input-pin low" data-state="0">0</div>
                                    </div>
                                    <div class="gate-symbol">AND</div>
                                    <div class="gate-output low" data-output="0">0</div>
                                </div>
                                <div class="gate-item or-gate">
                                    <div class="gate-inputs">
                                        <div class="input-pin high" data-state="1">1</div>
                                        <div class="input-pin low" data-state="0">0</div>
                                    </div>
                                    <div class="gate-symbol">OR</div>
                                    <div class="gate-output high" data-output="1">1</div>
                                </div>
                                <div class="gate-item not-gate">
                                    <div class="gate-inputs">
                                        <div class="input-pin high" data-state="1">1</div>
                                    </div>
                                    <div class="gate-symbol">NOT</div>
                                    <div class="gate-output low" data-output="0">0</div>
                                </div>
                            </div>
                            <div class="binary-counter">
                                <div class="counter-display">
                                    <div class="bit-display">
                                        <span class="bit" id="bit3">0</span>
                                        <span class="bit" id="bit2">0</span>
                                        <span class="bit" id="bit1">0</span>
                                        <span class="bit" id="bit0">0</span>
                                    </div>
                                    <div class="counter-label">4-bit Counter</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Number Systems -->
        <div class="slide" data-slide="1">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-calculator"></i> Number Systems</h1>
                        <p>Understanding Binary, Decimal, and Hexadecimal representations</p>
                    </div>
                    <div class="slide-body">
                        <div class="concept-explanation">
                            <div class="explanation-text">
                                <h3>Digital Number Systems</h3>
                                <p>Digital systems use different number bases to represent information. Understanding these systems is crucial for digital electronics.</p>
                                
                                <h3>Common Number Systems</h3>
                                <div class="number-systems">
                                    <div class="system-card">
                                        <h4>Binary (Base 2)</h4>
                                        <p>Uses only 0 and 1</p>
                                        <div class="example">1010₂ = 10₁₀</div>
                                    </div>
                                    <div class="system-card">
                                        <h4>Decimal (Base 10)</h4>
                                        <p>Standard counting system</p>
                                        <div class="example">10₁₀ = 10</div>
                                    </div>
                                    <div class="system-card">
                                        <h4>Hexadecimal (Base 16)</h4>
                                        <p>Uses 0-9 and A-F</p>
                                        <div class="example">A₁₆ = 10₁₀</div>
                                    </div>
                                </div>
                            </div>
                            <div class="explanation-visual">
                                <div class="number-converter">
                                    <h4>Interactive Number Converter</h4>
                                    <div class="converter-input">
                                        <label>Decimal:</label>
                                        <input type="number" id="decimalInput" value="10" min="0" max="255">
                                    </div>
                                    <div class="conversion-results">
                                        <div class="result-item">
                                            <span class="label">Binary:</span>
                                            <span class="value" id="binaryResult">1010</span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">Hexadecimal:</span>
                                            <span class="value" id="hexResult">A</span>
                                        </div>
                                        <div class="result-item">
                                            <span class="label">BCD:</span>
                                            <span class="value" id="bcdResult">0001 0000</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="binary-visualization">
                                    <h4>Binary Place Values</h4>
                                    <div class="place-values">
                                        <div class="place-value">
                                            <div class="bit-position">2³</div>
                                            <div class="bit-value" id="bit-8">0</div>
                                            <div class="place-label">8</div>
                                        </div>
                                        <div class="place-value">
                                            <div class="bit-position">2²</div>
                                            <div class="bit-value" id="bit-4">0</div>
                                            <div class="place-label">4</div>
                                        </div>
                                        <div class="place-value">
                                            <div class="bit-position">2¹</div>
                                            <div class="bit-value" id="bit-2">1</div>
                                            <div class="place-label">2</div>
                                        </div>
                                        <div class="place-value">
                                            <div class="bit-position">2⁰</div>
                                            <div class="bit-value" id="bit-1">0</div>
                                            <div class="place-label">1</div>
                                        </div>
                                    </div>
                                    <div class="calculation">
                                        <span id="calculation-display">0×8 + 0×4 + 1×2 + 0×1 = 2</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: Logic Gates -->
        <div class="slide" data-slide="2">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-sitemap"></i> Logic Gates</h1>
                        <p>Building blocks of digital circuits</p>
                    </div>
                    <div class="slide-body">
                        <div class="gates-grid">
                            <div class="gate-demo and-demo">
                                <div class="gate-header">
                                    <h3>AND Gate</h3>
                                    <p>Output is 1 only when ALL inputs are 1</p>
                                </div>
                                <div class="gate-visual">
                                    <div class="gate-symbol-large and-symbol">
                                        <div class="gate-inputs-large">
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">A</span>
                                                <span class="input-value">0</span>
                                            </div>
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">B</span>
                                                <span class="input-value">0</span>
                                            </div>
                                        </div>
                                        <div class="gate-body">AND</div>
                                        <div class="output-terminal">
                                            <span class="output-value" id="and-output">0</span>
                                            <span class="output-label">Y</span>
                                        </div>
                                    </div>
                                    <div class="truth-table">
                                        <table>
                                            <thead>
                                                <tr><th>A</th><th>B</th><th>Y</th></tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>0</td><td>0</td><td>0</td></tr>
                                                <tr><td>0</td><td>1</td><td>0</td></tr>
                                                <tr><td>1</td><td>0</td><td>0</td></tr>
                                                <tr><td>1</td><td>1</td><td>1</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="gate-demo or-demo">
                                <div class="gate-header">
                                    <h3>OR Gate</h3>
                                    <p>Output is 1 when ANY input is 1</p>
                                </div>
                                <div class="gate-visual">
                                    <div class="gate-symbol-large or-symbol">
                                        <div class="gate-inputs-large">
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">A</span>
                                                <span class="input-value">0</span>
                                            </div>
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">B</span>
                                                <span class="input-value">0</span>
                                            </div>
                                        </div>
                                        <div class="gate-body">OR</div>
                                        <div class="output-terminal">
                                            <span class="output-value" id="or-output">0</span>
                                            <span class="output-label">Y</span>
                                        </div>
                                    </div>
                                    <div class="truth-table">
                                        <table>
                                            <thead>
                                                <tr><th>A</th><th>B</th><th>Y</th></tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>0</td><td>0</td><td>0</td></tr>
                                                <tr><td>0</td><td>1</td><td>1</td></tr>
                                                <tr><td>1</td><td>0</td><td>1</td></tr>
                                                <tr><td>1</td><td>1</td><td>1</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="gate-demo not-demo">
                                <div class="gate-header">
                                    <h3>NOT Gate (Inverter)</h3>
                                    <p>Output is opposite of input</p>
                                </div>
                                <div class="gate-visual">
                                    <div class="gate-symbol-large not-symbol">
                                        <div class="gate-inputs-large">
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">A</span>
                                                <span class="input-value">0</span>
                                            </div>
                                        </div>
                                        <div class="gate-body">NOT</div>
                                        <div class="output-terminal">
                                            <span class="output-value" id="not-output">1</span>
                                            <span class="output-label">Y</span>
                                        </div>
                                    </div>
                                    <div class="truth-table">
                                        <table>
                                            <thead>
                                                <tr><th>A</th><th>Y</th></tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>0</td><td>1</td></tr>
                                                <tr><td>1</td><td>0</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="gate-demo xor-demo">
                                <div class="gate-header">
                                    <h3>XOR Gate</h3>
                                    <p>Output is 1 when inputs are different</p>
                                </div>
                                <div class="gate-visual">
                                    <div class="gate-symbol-large xor-symbol">
                                        <div class="gate-inputs-large">
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">A</span>
                                                <span class="input-value">0</span>
                                            </div>
                                            <div class="input-terminal" onclick="toggleInput(this)" data-state="0">
                                                <span class="input-label">B</span>
                                                <span class="input-value">0</span>
                                            </div>
                                        </div>
                                        <div class="gate-body">XOR</div>
                                        <div class="output-terminal">
                                            <span class="output-value" id="xor-output">0</span>
                                            <span class="output-label">Y</span>
                                        </div>
                                    </div>
                                    <div class="truth-table">
                                        <table>
                                            <thead>
                                                <tr><th>A</th><th>B</th><th>Y</th></tr>
                                            </thead>
                                            <tbody>
                                                <tr><td>0</td><td>0</td><td>0</td></tr>
                                                <tr><td>0</td><td>1</td><td>1</td></tr>
                                                <tr><td>1</td><td>0</td><td>1</td></tr>
                                                <tr><td>1</td><td>1</td><td>0</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Boolean Algebra -->
        <div class="slide" data-slide="3">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-function"></i> Boolean Algebra</h1>
                        <p>Mathematical foundation of digital logic</p>
                    </div>
                    <div class="slide-body">
                        <div class="boolean-content">
                            <div class="laws-section">
                                <h3>Boolean Laws</h3>
                                <div class="laws-grid">
                                    <div class="law-card">
                                        <h4>Identity Laws</h4>
                                        <div class="law-equations">
                                            <div class="equation">A + 0 = A</div>
                                            <div class="equation">A · 1 = A</div>
                                        </div>
                                    </div>
                                    <div class="law-card">
                                        <h4>Null Laws</h4>
                                        <div class="law-equations">
                                            <div class="equation">A + 1 = 1</div>
                                            <div class="equation">A · 0 = 0</div>
                                        </div>
                                    </div>
                                    <div class="law-card">
                                        <h4>Complement Laws</h4>
                                        <div class="law-equations">
                                            <div class="equation">A + Ā = 1</div>
                                            <div class="equation">A · Ā = 0</div>
                                        </div>
                                    </div>
                                    <div class="law-card">
                                        <h4>De Morgan's Laws</h4>
                                        <div class="law-equations">
                                            <div class="equation">(A + B)' = A' · B'</div>
                                            <div class="equation">(A · B)' = A' + B'</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="simplification-demo">
                                <h3>Circuit Simplification</h3>
                                <div class="simplification-example">
                                    <div class="original-circuit">
                                        <h4>Original Expression</h4>
                                        <div class="expression">F = A·B + A·B' + A'·B</div>
                                        <div class="circuit-diagram">
                                            <div class="complex-circuit">
                                                <!-- Complex circuit representation -->
                                                <div class="gate-level">
                                                    <div class="and-gate-small">A·B</div>
                                                    <div class="and-gate-small">A·B'</div>
                                                    <div class="and-gate-small">A'·B</div>
                                                </div>
                                                <div class="or-gate-final">OR</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="simplification-arrow">
                                        <i class="fas fa-arrow-right"></i>
                                        <span>Simplify</span>
                                    </div>
                                    <div class="simplified-circuit">
                                        <h4>Simplified Expression</h4>
                                        <div class="expression">F = A + B</div>
                                        <div class="circuit-diagram">
                                            <div class="simple-circuit">
                                                <div class="or-gate-simple">A + B</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Medical Applications -->
        <div class="slide" data-slide="4">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-heartbeat"></i> Medical Applications</h1>
                        <p>Digital electronics in biomedical devices</p>
                    </div>
                    <div class="slide-body">
                        <div class="medical-applications">
                            <div class="application-card">
                                <div class="app-header">
                                    <h3><i class="fas fa-heartbeat"></i> Digital ECG Processing</h3>
                                </div>
                                <div class="app-content">
                                    <div class="app-diagram">
                                        <div class="signal-flow">
                                            <div class="stage">
                                                <div class="stage-icon">
                                                    <i class="fas fa-heart"></i>
                                                </div>
                                                <div class="stage-label">Analog ECG</div>
                                            </div>
                                            <div class="arrow">→</div>
                                            <div class="stage">
                                                <div class="stage-icon">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </div>
                                                <div class="stage-label">ADC</div>
                                            </div>
                                            <div class="arrow">→</div>
                                            <div class="stage">
                                                <div class="stage-icon">
                                                    <i class="fas fa-microchip"></i>
                                                </div>
                                                <div class="stage-label">Digital Processing</div>
                                            </div>
                                            <div class="arrow">→</div>
                                            <div class="stage">
                                                <div class="stage-icon">
                                                    <i class="fas fa-chart-line"></i>
                                                </div>
                                                <div class="stage-label">Display</div>
                                            </div>
                                        </div>
                                        <div class="ecg-waveform">
                                            <svg width="300" height="100" viewBox="0 0 300 100">
                                                <path d="M10,50 L50,50 L60,30 L70,70 L80,20 L90,50 L130,50 L140,45 L150,55 L160,50 L290,50" 
                                                      stroke="#4facfe" stroke-width="2" fill="none">
                                                    <animate attributeName="stroke-dasharray" 
                                                             values="0,600;300,300;600,0" 
                                                             dur="3s" 
                                                             repeatCount="indefinite"/>
                                                </path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="app-description">
                                        <h4>Digital Signal Processing</h4>
                                        <ul>
                                            <li>Analog-to-Digital Conversion</li>
                                            <li>Digital Filtering</li>
                                            <li>Feature Extraction</li>
                                            <li>Arrhythmia Detection</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="application-card">
                                <div class="app-header">
                                    <h3><i class="fas fa-hand-holding-heart"></i> Pulse Oximeter Logic</h3>
                                </div>
                                <div class="app-content">
                                    <div class="app-diagram">
                                        <div class="oximeter-demo">
                                            <div class="sensor-section">
                                                <div class="led-array">
                                                    <div class="led red-led">Red LED</div>
                                                    <div class="led ir-led">IR LED</div>
                                                </div>
                                                <div class="finger">
                                                    <i class="fas fa-hand-paper"></i>
                                                </div>
                                                <div class="photodiode">
                                                    <i class="fas fa-eye"></i>
                                                    <span>Photodiode</span>
                                                </div>
                                            </div>
                                            <div class="processing-section">
                                                <div class="digital-processor">
                                                    <h4>Digital Processing Unit</h4>
                                                    <div class="processing-steps">
                                                        <div class="step">Signal Amplification</div>
                                                        <div class="step">A/D Conversion</div>
                                                        <div class="step">Algorithm Processing</div>
                                                        <div class="step">SpO₂ Calculation</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="spo2-display">
                                            <div class="display-screen">
                                                <div class="reading">
                                                    <span class="value" id="spo2-value">98</span>
                                                    <span class="unit">%</span>
                                                </div>
                                                <div class="label">SpO₂</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Summary -->
        <div class="slide" data-slide="5">
            <div class="slide-content">
                <div class="summary-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-check-circle"></i> Summary</h1>
                        <p>Key concepts in Digital Electronics</p>
                    </div>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <h3>Number Systems</h3>
                            <p>Binary, decimal, and hexadecimal representations</p>
                            <ul>
                                <li>Base conversion methods</li>
                                <li>Binary arithmetic</li>
                                <li>BCD encoding</li>
                            </ul>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-sitemap"></i>
                            </div>
                            <h3>Logic Gates</h3>
                            <p>Building blocks of digital circuits</p>
                            <ul>
                                <li>AND, OR, NOT gates</li>
                                <li>XOR, NAND, NOR gates</li>
                                <li>Truth tables</li>
                            </ul>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-function"></i>
                            </div>
                            <h3>Boolean Algebra</h3>
                            <p>Mathematical foundation of digital logic</p>
                            <ul>
                                <li>Boolean laws and theorems</li>
                                <li>Circuit simplification</li>
                                <li>De Morgan's laws</li>
                            </ul>
                        </div>
                        <div class="summary-item">
                            <div class="summary-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <h3>Medical Applications</h3>
                            <p>Digital systems in healthcare</p>
                            <ul>
                                <li>ECG signal processing</li>
                                <li>Pulse oximetry</li>
                                <li>Medical device control</li>
                            </ul>
                        </div>
                    </div>
                    <div class="next-steps">
                        <h3>Continue Learning</h3>
                        <div class="next-buttons">
                            <button class="next-btn" onclick="openCombinationalCircuits()">
                                <i class="fas fa-puzzle-piece"></i>
                                Combinational Circuits
                            </button>
                            <button class="next-btn" onclick="openSequentialCircuits()">
                                <i class="fas fa-history"></i>
                                Sequential Circuits
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide Navigation -->
    <div class="slide-navigation">
        <button class="nav-btn prev" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="slide-indicators">
            <span class="indicator active" data-slide="0"></span>
            <span class="indicator" data-slide="1"></span>
            <span class="indicator" data-slide="2"></span>
            <span class="indicator" data-slide="3"></span>
            <span class="indicator" data-slide="4"></span>
            <span class="indicator" data-slide="5"></span>
        </div>
        <button class="nav-btn next" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script src="../../js/presentation.js"></script>
    <script src="../../js/digital-presentation.js"></script>
</body>
</html>
