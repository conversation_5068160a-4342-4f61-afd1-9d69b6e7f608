<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Circuit Simulator - Electronics Lab</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/simulator.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="simulator-nav">
        <div class="nav-container">
            <a href="index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
            <h1><i class="fas fa-flask"></i> Virtual Circuit Simulator</h1>
            <div class="simulator-controls">
                <button id="saveCircuit" class="btn btn-secondary">
                    <i class="fas fa-save"></i> Save
                </button>
                <button id="loadCircuit" class="btn btn-secondary">
                    <i class="fas fa-folder-open"></i> Load
                </button>
                <button id="clearCircuit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Clear
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Simulator Interface -->
    <main class="simulator-main">
        <!-- Component Palette -->
        <aside class="component-palette">
            <h3><i class="fas fa-toolbox"></i> Components</h3>
            
            <div class="component-category">
                <h4>Power Sources</h4>
                <div class="component-grid">
                    <div class="component-item" data-type="battery" draggable="true">
                        <div class="component-icon battery-icon"></div>
                        <span>Battery</span>
                    </div>
                    <div class="component-item" data-type="ac-source" draggable="true">
                        <div class="component-icon ac-source-icon"></div>
                        <span>AC Source</span>
                    </div>
                </div>
            </div>

            <div class="component-category">
                <h4>Passive Components</h4>
                <div class="component-grid">
                    <div class="component-item" data-type="resistor" draggable="true">
                        <div class="component-icon resistor-icon"></div>
                        <span>Resistor</span>
                    </div>
                    <div class="component-item" data-type="capacitor" draggable="true">
                        <div class="component-icon capacitor-icon"></div>
                        <span>Capacitor</span>
                    </div>
                    <div class="component-item" data-type="inductor" draggable="true">
                        <div class="component-icon inductor-icon"></div>
                        <span>Inductor</span>
                    </div>
                </div>
            </div>

            <div class="component-category">
                <h4>Semiconductors</h4>
                <div class="component-grid">
                    <div class="component-item" data-type="diode" draggable="true">
                        <div class="component-icon diode-icon"></div>
                        <span>Diode</span>
                    </div>
                    <div class="component-item" data-type="led" draggable="true">
                        <div class="component-icon led-icon"></div>
                        <span>LED</span>
                    </div>
                    <div class="component-item" data-type="transistor" draggable="true">
                        <div class="component-icon transistor-icon"></div>
                        <span>Transistor</span>
                    </div>
                </div>
            </div>

            <div class="component-category">
                <h4>Measurement</h4>
                <div class="component-grid">
                    <div class="component-item" data-type="voltmeter" draggable="true">
                        <div class="component-icon voltmeter-icon"></div>
                        <span>Voltmeter</span>
                    </div>
                    <div class="component-item" data-type="ammeter" draggable="true">
                        <div class="component-icon ammeter-icon"></div>
                        <span>Ammeter</span>
                    </div>
                    <div class="component-item" data-type="oscilloscope" draggable="true">
                        <div class="component-icon oscilloscope-icon"></div>
                        <span>Oscilloscope</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Circuit Canvas -->
        <section class="circuit-workspace">
            <div class="workspace-toolbar">
                <div class="tool-group">
                    <button class="tool-btn active" data-tool="select" title="Select Tool">
                        <i class="fas fa-mouse-pointer"></i>
                    </button>
                    <button class="tool-btn" data-tool="wire" title="Wire Tool">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="tool-btn" data-tool="delete" title="Delete Tool">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                
                <div class="tool-group">
                    <button class="tool-btn" id="zoomIn" title="Zoom In">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="tool-btn" id="zoomOut" title="Zoom Out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button class="tool-btn" id="resetZoom" title="Reset Zoom">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                </div>

                <div class="tool-group">
                    <button class="tool-btn simulation-btn" id="runSimulation" title="Run Simulation">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="tool-btn simulation-btn" id="pauseSimulation" title="Pause Simulation" disabled>
                        <i class="fas fa-pause"></i>
                    </button>
                    <button class="tool-btn simulation-btn" id="stopSimulation" title="Stop Simulation" disabled>
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>

            <div class="circuit-canvas" id="circuitCanvas">
                <div class="grid-background"></div>
                <svg class="circuit-svg" width="100%" height="100%">
                    <defs>
                        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e0e0e0" stroke-width="1" opacity="0.5"/>
                        </pattern>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#ff6b6b" />
                        </marker>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
                
                <!-- Welcome message for empty canvas -->
                <div class="welcome-message" id="welcomeMessage">
                    <div class="welcome-content">
                        <i class="fas fa-microchip"></i>
                        <h3>Welcome to the Circuit Simulator!</h3>
                        <p>Drag components from the palette to start building your circuit.</p>
                        <div class="quick-start">
                            <h4>Quick Start:</h4>
                            <ol>
                                <li>Drag a battery from the Power Sources section</li>
                                <li>Add a resistor and LED</li>
                                <li>Connect them with wires</li>
                                <li>Click the play button to simulate</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Properties Panel -->
        <aside class="properties-panel">
            <h3><i class="fas fa-cog"></i> Properties</h3>
            
            <div class="property-section" id="componentProperties">
                <div class="no-selection">
                    <i class="fas fa-hand-pointer"></i>
                    <p>Select a component to view its properties</p>
                </div>
            </div>

            <div class="property-section">
                <h4>Simulation Status</h4>
                <div class="status-display">
                    <div class="status-item">
                        <span class="status-label">State:</span>
                        <span class="status-value" id="simulationState">Stopped</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Time:</span>
                        <span class="status-value" id="simulationTime">0.00s</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Components:</span>
                        <span class="status-value" id="componentCount">0</span>
                    </div>
                </div>
            </div>

            <div class="property-section">
                <h4>Measurements</h4>
                <div class="measurements-display" id="measurementsDisplay">
                    <p class="no-measurements">No measurements available</p>
                </div>
            </div>

            <div class="property-section">
                <h4>Circuit Analysis</h4>
                <div class="analysis-display" id="analysisDisplay">
                    <p class="no-analysis">Run simulation to see analysis</p>
                </div>
            </div>
        </aside>
    </main>

    <!-- Component Property Modal -->
    <div class="modal-overlay" id="componentModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Component Properties</h3>
                <button class="modal-close" onclick="closeComponentModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Dynamic content based on component type -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeComponentModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveComponentProperties()">Apply</button>
            </div>
        </div>
    </div>

    <!-- Tutorial Overlay -->
    <div class="tutorial-overlay" id="tutorialOverlay">
        <div class="tutorial-content">
            <h3><i class="fas fa-graduation-cap"></i> Interactive Tutorial</h3>
            <p>Would you like to take a guided tour of the circuit simulator?</p>
            <div class="tutorial-buttons">
                <button class="btn btn-secondary" onclick="skipTutorial()">Skip</button>
                <button class="btn btn-primary" onclick="startTutorial()">Start Tutorial</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/simulator.js"></script>
    <script>
        // Initialize simulator when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeSimulator();
            
            // Show tutorial for first-time users
            if (!localStorage.getItem('simulatorTutorialCompleted')) {
                document.getElementById('tutorialOverlay').style.display = 'flex';
            }
        });

        function initializeSimulator() {
            setupDragAndDrop();
            setupToolbar();
            setupCanvas();
            updateComponentCount();
        }

        function setupDragAndDrop() {
            const componentItems = document.querySelectorAll('.component-item');
            const canvas = document.getElementById('circuitCanvas');

            componentItems.forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', this.dataset.type);
                    this.classList.add('dragging');
                });

                item.addEventListener('dragend', function() {
                    this.classList.remove('dragging');
                });
            });

            canvas.addEventListener('dragover', function(e) {
                e.preventDefault();
            });

            canvas.addEventListener('drop', function(e) {
                e.preventDefault();
                const componentType = e.dataTransfer.getData('text/plain');
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                addComponent(componentType, x, y);
                hideWelcomeMessage();
            });
        }

        function setupToolbar() {
            const toolButtons = document.querySelectorAll('.tool-btn[data-tool]');
            
            toolButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove active class from all tool buttons
                    toolButtons.forEach(b => b.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Set current tool
                    setCurrentTool(this.dataset.tool);
                });
            });

            // Simulation controls
            document.getElementById('runSimulation').addEventListener('click', startSimulation);
            document.getElementById('pauseSimulation').addEventListener('click', pauseSimulation);
            document.getElementById('stopSimulation').addEventListener('click', stopSimulation);
            
            // Zoom controls
            document.getElementById('zoomIn').addEventListener('click', () => zoomCanvas(1.2));
            document.getElementById('zoomOut').addEventListener('click', () => zoomCanvas(0.8));
            document.getElementById('resetZoom').addEventListener('click', resetZoom);
        }

        function setupCanvas() {
            const canvas = document.getElementById('circuitCanvas');
            
            canvas.addEventListener('click', function(e) {
                const currentTool = getCurrentTool();
                
                if (currentTool === 'select') {
                    // Handle component selection
                    handleComponentSelection(e);
                } else if (currentTool === 'delete') {
                    // Handle component deletion
                    handleComponentDeletion(e);
                }
            });
        }

        function addComponent(type, x, y) {
            const canvas = document.getElementById('circuitCanvas');
            const component = createComponentElement(type, x, y);
            canvas.appendChild(component);
            updateComponentCount();
        }

        function createComponentElement(type, x, y) {
            const component = document.createElement('div');
            component.className = `circuit-component ${type}`;
            component.style.left = `${x - 25}px`;
            component.style.top = `${y - 25}px`;
            component.dataset.type = type;
            component.dataset.id = generateComponentId();
            
            // Add component-specific content
            component.innerHTML = getComponentHTML(type);
            
            // Make component draggable
            makeComponentDraggable(component);
            
            // Add double-click handler for properties
            component.addEventListener('dblclick', function() {
                openComponentProperties(this);
            });
            
            return component;
        }

        function getComponentHTML(type) {
            const htmlMap = {
                'battery': '<div class="battery-symbol"><div class="positive"></div><div class="negative"></div></div>',
                'resistor': '<div class="resistor-symbol"><div class="zigzag"></div></div>',
                'capacitor': '<div class="capacitor-symbol"><div class="plate1"></div><div class="plate2"></div></div>',
                'led': '<div class="led-symbol"><div class="led-body"></div><div class="led-arrow"></div></div>',
                'diode': '<div class="diode-symbol"><div class="triangle"></div><div class="line"></div></div>',
                'transistor': '<div class="transistor-symbol"><div class="base"></div><div class="collector"></div><div class="emitter"></div></div>',
                'voltmeter': '<div class="meter-symbol"><div class="meter-face">V</div></div>',
                'ammeter': '<div class="meter-symbol"><div class="meter-face">A</div></div>'
            };
            
            return htmlMap[type] || '<div class="generic-symbol"></div>';
        }

        function makeComponentDraggable(component) {
            let isDragging = false;
            let startX, startY, initialX, initialY;

            component.addEventListener('mousedown', function(e) {
                if (getCurrentTool() === 'select') {
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    initialX = parseInt(component.style.left);
                    initialY = parseInt(component.style.top);
                    
                    component.style.zIndex = '1000';
                    e.preventDefault();
                }
            });

            document.addEventListener('mousemove', function(e) {
                if (isDragging) {
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    component.style.left = `${initialX + deltaX}px`;
                    component.style.top = `${initialY + deltaY}px`;
                }
            });

            document.addEventListener('mouseup', function() {
                if (isDragging) {
                    isDragging = false;
                    component.style.zIndex = '';
                }
            });
        }

        function generateComponentId() {
            return 'comp_' + Math.random().toString(36).substr(2, 9);
        }

        function getCurrentTool() {
            const activeTool = document.querySelector('.tool-btn.active[data-tool]');
            return activeTool ? activeTool.dataset.tool : 'select';
        }

        function setCurrentTool(tool) {
            // Update cursor based on tool
            const canvas = document.getElementById('circuitCanvas');
            canvas.className = `circuit-canvas tool-${tool}`;
        }

        function hideWelcomeMessage() {
            const welcomeMessage = document.getElementById('welcomeMessage');
            if (welcomeMessage) {
                welcomeMessage.style.display = 'none';
            }
        }

        function updateComponentCount() {
            const components = document.querySelectorAll('.circuit-component');
            document.getElementById('componentCount').textContent = components.length;
        }

        function handleComponentSelection(e) {
            const component = e.target.closest('.circuit-component');
            if (component) {
                // Remove selection from other components
                document.querySelectorAll('.circuit-component.selected').forEach(c => {
                    c.classList.remove('selected');
                });
                
                // Select this component
                component.classList.add('selected');
                showComponentProperties(component);
            }
        }

        function handleComponentDeletion(e) {
            const component = e.target.closest('.circuit-component');
            if (component) {
                component.remove();
                updateComponentCount();
                clearComponentProperties();
            }
        }

        function showComponentProperties(component) {
            const propertiesPanel = document.getElementById('componentProperties');
            const type = component.dataset.type;
            
            propertiesPanel.innerHTML = `
                <h4>${type.charAt(0).toUpperCase() + type.slice(1)} Properties</h4>
                <div class="property-form">
                    ${getPropertyFormHTML(type)}
                </div>
            `;
        }

        function getPropertyFormHTML(type) {
            const forms = {
                'battery': `
                    <div class="property-field">
                        <label>Voltage (V):</label>
                        <input type="number" value="12" min="0" max="100" step="0.1">
                    </div>
                `,
                'resistor': `
                    <div class="property-field">
                        <label>Resistance (Ω):</label>
                        <input type="number" value="1000" min="1" max="1000000" step="1">
                    </div>
                `,
                'capacitor': `
                    <div class="property-field">
                        <label>Capacitance (μF):</label>
                        <input type="number" value="100" min="0.1" max="10000" step="0.1">
                    </div>
                `,
                'led': `
                    <div class="property-field">
                        <label>Forward Voltage (V):</label>
                        <input type="number" value="2.1" min="1" max="5" step="0.1">
                    </div>
                    <div class="property-field">
                        <label>Color:</label>
                        <select>
                            <option value="red">Red</option>
                            <option value="green">Green</option>
                            <option value="blue">Blue</option>
                            <option value="yellow">Yellow</option>
                        </select>
                    </div>
                `
            };
            
            return forms[type] || '<p>No configurable properties</p>';
        }

        function clearComponentProperties() {
            const propertiesPanel = document.getElementById('componentProperties');
            propertiesPanel.innerHTML = `
                <div class="no-selection">
                    <i class="fas fa-hand-pointer"></i>
                    <p>Select a component to view its properties</p>
                </div>
            `;
        }

        function startSimulation() {
            document.getElementById('runSimulation').disabled = true;
            document.getElementById('pauseSimulation').disabled = false;
            document.getElementById('stopSimulation').disabled = false;
            document.getElementById('simulationState').textContent = 'Running';
            
            // Start simulation timer
            startSimulationTimer();
            
            // Add visual effects to show current flow
            addCurrentFlowAnimation();
        }

        function pauseSimulation() {
            document.getElementById('runSimulation').disabled = false;
            document.getElementById('pauseSimulation').disabled = true;
            document.getElementById('simulationState').textContent = 'Paused';
            
            // Pause animations
            pauseCurrentFlowAnimation();
        }

        function stopSimulation() {
            document.getElementById('runSimulation').disabled = false;
            document.getElementById('pauseSimulation').disabled = true;
            document.getElementById('stopSimulation').disabled = true;
            document.getElementById('simulationState').textContent = 'Stopped';
            document.getElementById('simulationTime').textContent = '0.00s';
            
            // Stop all animations
            stopCurrentFlowAnimation();
        }

        let simulationStartTime;
        let simulationTimer;

        function startSimulationTimer() {
            simulationStartTime = Date.now();
            simulationTimer = setInterval(() => {
                const elapsed = (Date.now() - simulationStartTime) / 1000;
                document.getElementById('simulationTime').textContent = elapsed.toFixed(2) + 's';
            }, 100);
        }

        function addCurrentFlowAnimation() {
            const components = document.querySelectorAll('.circuit-component');
            components.forEach(component => {
                component.classList.add('animated');
            });
        }

        function pauseCurrentFlowAnimation() {
            const components = document.querySelectorAll('.circuit-component.animated');
            components.forEach(component => {
                component.style.animationPlayState = 'paused';
            });
        }

        function stopCurrentFlowAnimation() {
            if (simulationTimer) {
                clearInterval(simulationTimer);
                simulationTimer = null;
            }
            
            const components = document.querySelectorAll('.circuit-component.animated');
            components.forEach(component => {
                component.classList.remove('animated');
                component.style.animationPlayState = '';
            });
        }

        function zoomCanvas(factor) {
            const canvas = document.getElementById('circuitCanvas');
            const currentScale = parseFloat(canvas.style.transform.replace(/[^0-9.]/g, '')) || 1;
            const newScale = Math.max(0.5, Math.min(2, currentScale * factor));
            canvas.style.transform = `scale(${newScale})`;
        }

        function resetZoom() {
            const canvas = document.getElementById('circuitCanvas');
            canvas.style.transform = 'scale(1)';
        }

        function openComponentProperties(component) {
            // This would open a detailed properties modal
            console.log('Opening properties for', component.dataset.type);
        }

        function closeComponentModal() {
            document.getElementById('componentModal').style.display = 'none';
        }

        function saveComponentProperties() {
            // Save component properties
            closeComponentModal();
        }

        function startTutorial() {
            document.getElementById('tutorialOverlay').style.display = 'none';
            localStorage.setItem('simulatorTutorialCompleted', 'true');
            // Start interactive tutorial
            console.log('Starting tutorial...');
        }

        function skipTutorial() {
            document.getElementById('tutorialOverlay').style.display = 'none';
            localStorage.setItem('simulatorTutorialCompleted', 'true');
        }
    </script>
</body>
</html>
