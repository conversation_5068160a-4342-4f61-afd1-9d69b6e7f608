<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Noto+Sans+Arabic:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Noto Sans Arabic', 'Inter', sans-serif;
            overflow: hidden;
            background-color: #f0f4f8; /* Light blue-gray background */
        }
        .slide-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        .slide {
            position: absolute;
            width: 90%;
            height: 90%;
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            box-sizing: border-box;
            opacity: 0;
            transform: scale(0.95);
            transition: opacity 0.7s ease-in-out, transform 0.7s ease-in-out;
            text-align: center;
            color: #334155; /* Slate 700 */
        }
        .slide.active {
            opacity: 1;
            transform: scale(1);
            z-index: 10;
        }
        .slide h1, .slide h2 {
            color: #1e3a8a; /* Dark blue */
            margin-bottom: 20px;
        }
        .slide p {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 15px;
        }
        .slide ul {
            list-style: none;
            padding: 0;
            text-align: right;
            width: 100%;
            max-width: 800px;
        }
        .slide ul li {
            font-size: 1.1rem;
            margin-bottom: 10px;
            padding-right: 25px;
            position: relative;
        }
        .slide ul li::before {
            content: '•';
            color: #3b82f6; /* Blue 500 */
            font-size: 1.5rem;
            position: absolute;
            right: 0;
            top: -5px;
        }
        .navigation-buttons {
            position: absolute;
            bottom: 30px;
            display: flex;
            gap: 20px;
            z-index: 20;
        }
        .nav-button {
            background-color: #3b82f6; /* Blue 500 */
            color: white;
            padding: 12px 25px;
            border-radius: 15px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .nav-button:hover {
            background-color: #2563eb; /* Blue 600 */
            transform: translateY(-2px);
        }
        .nav-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        /* Specific styles for interactive elements and diagrams */
        .calculator-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
            background-color: #e0f2fe; /* Light blue */
            padding: 25px;
            border-radius: 15px;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
            width: 80%;
            max-width: 500px;
        }
        .calculator-container label {
            font-weight: 600;
            color: #1e3a8a;
            margin-bottom: 5px;
            text-align: right;
            display: block;
        }
        .calculator-container input {
            padding: 10px 15px;
            border: 1px solid #93c5fd; /* Blue 300 */
            border-radius: 10px;
            font-size: 1rem;
            width: 100%;
            box-sizing: border-box;
            text-align: right;
        }
        .calculator-container button {
            background-color: #10b981; /* Emerald 500 */
            color: white;
            padding: 12px 25px;
            border-radius: 15px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            margin-top: 10px;
        }
        .calculator-container button:hover {
            background-color: #059669; /* Emerald 600 */
            transform: translateY(-2px);
        }
        .calculator-result {
            margin-top: 20px;
            font-size: 1.2rem;
            font-weight: 700;
            color: #065f46; /* Green 800 */
        }

        /* SVG Icon Styling (Tinkercad-like) */
        .circuit-icon {
            stroke: #334155; /* Slate 700 */
            stroke-width: 2;
            fill: none;
            transition: all 0.3s ease-in-out;
        }
        .circuit-icon.fill-color {
            fill: #60a5fa; /* Blue 400 */
        }
        .circuit-icon.active-flow {
            stroke: #ef4444; /* Red 500 for active current */
            stroke-width: 3;
            animation: pulse-stroke 1s infinite alternate;
        }
        .circuit-icon.active-fill {
            fill: #fcd34d; /* Amber 300 for active component */
            animation: pulse-fill 1s infinite alternate;
        }

        @keyframes pulse-stroke {
            from { stroke-width: 2; }
            to { stroke-width: 3.5; }
        }
        @keyframes pulse-fill {
            from { fill: #60a5fa; }
            to { fill: #fcd34d; }
        }

        .diagram-container {
            margin-top: 20px;
            background-color: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.05);
            width: 90%;
            max-width: 900px;
            overflow: auto; /* For larger diagrams */
        }

        .block-diagram-svg {
            width: 100%;
            height: auto;
            max-height: 400px;
        }

        .block-diagram-svg rect {
            fill: #bfdbfe; /* Blue 200 */
            stroke: #3b82f6; /* Blue 500 */
            stroke-width: 2;
            rx: 8; /* Rounded corners */
            ry: 8;
        }
        .block-diagram-svg text {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 16px;
            fill: #1e3a8a; /* Dark blue */
            text-anchor: middle;
            dominant-baseline: central;
        }
        .block-diagram-svg line {
            stroke: #475569; /* Slate 600 */
            stroke-width: 2;
            marker-end: url(#arrowhead);
        }
        .block-diagram-svg .arrowhead {
            fill: #475569;
        }

        /* Circuit diagram SVG */
        .circuit-diagram-svg {
            width: 100%;
            height: auto;
            max-height: 500px;
            border: 1px solid #cbd5e1; /* Slate 300 */
            border-radius: 10px;
            background-color: #f0f4f8;
        }
        .circuit-diagram-svg .component-box {
            fill: #e2e8f0; /* Gray 200 */
            stroke: #64748b; /* Gray 600 */
            stroke-width: 1;
            rx: 5;
            ry: 5;
        }
        .circuit-diagram-svg .line {
            stroke: #475569; /* Slate 600 */
            stroke-width: 2;
            fill: none;
        }
        .circuit-diagram-svg .label {
            font-family: 'Noto Sans Arabic', sans-serif;
            font-size: 12px;
            fill: #334155; /* Slate 700 */
            text-anchor: middle;
        }
        .circuit-diagram-svg .value-label {
            font-size: 10px;
            fill: #475569;
        }
        .circuit-diagram-svg .node {
            fill: #475569;
            r: 3;
        }
        .circuit-diagram-svg .power-label {
            font-size: 14px;
            font-weight: bold;
            fill: #1e3a8a;
        }
        .circuit-diagram-svg .ground-symbol {
            stroke: #475569;
            stroke-width: 2;
            fill: #475569;
        }
        .circuit-diagram-svg .transistor-base-dot {
            fill: #334155;
            r: 2;
        }
        .circuit-diagram-svg .transistor-arrow {
            fill: #334155;
            stroke: none;
        }
        .circuit-diagram-svg .led-fill {
            fill: #fcd34d; /* Amber 300 */
        }
        .circuit-diagram-svg .led-active {
            fill: #facc15; /* Yellow 400 */
            animation: led-glow 1s infinite alternate;
        }
        @keyframes led-glow {
            from { opacity: 0.7; }
            to { opacity: 1; }
        }

        /* Interactive BJT/MOSFET Circuit */
        .interactive-circuit {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        .interactive-circuit svg {
            width: 300px;
            height: 200px;
            border: 1px solid #cbd5e1;
            border-radius: 10px;
            background-color: #f8fafc;
            margin-bottom: 15px;
        }
        .interactive-circuit button {
            background-color: #10b981;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .interactive-circuit button:hover {
            background-color: #059669;
        }
        .interactive-circuit .led-circle {
            fill: #fcd34d; /* Amber 300 */
            transition: fill 0.3s ease;
        }
        .interactive-circuit .led-on {
            fill: #facc15; /* Yellow 400 */
            animation: led-glow 1s infinite alternate;
        }
        .interactive-circuit .transistor-active {
            fill: #fcd34d; /* Amber 300 */
            animation: pulse-fill 1s infinite alternate;
        }

    </style>
</head>
<body class="bg-f0f4f8">
    <div class="slide-container">
        <div class="slide active" id="slide-1">
            <h1 class="text-5xl font-bold mb-4">أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</h1>
            <p class="text-2xl mb-2">المستوى الأول | الحلقة 3</p>
            <p class="text-xl mb-8">قيادة لوحة مؤشرات حالة (Driving a Status Indicator Panel)</p>
            <p class="text-lg font-semibold">د. محمد يعقوب إسماعيل</p>
            <p class="text-md text-gray-600">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 1 / 12</div>
        </div>

        <div class="slide" id="slide-2">
            <h2 class="text-4xl font-bold mb-6">مقدمة ومفاهيم أساسية</h2>
            <p class="text-lg text-gray-700 mb-6">
                ننتقل اليوم إلى جانب مهم جداً في واجهة المستخدم لأي جهاز طبي: <span class="font-bold text-blue-600">قيادة لوحة مؤشرات الحالة</span>.
                هذه اللوحة، التي تتكون عادةً من مجموعة من المؤشرات الضوئية (LEDs)، توفر معلومات حيوية للمستخدم حول حالة الجهاز.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-2xl text-right">
                <div class="bg-blue-50 p-6 rounded-lg shadow-md flex items-center justify-end gap-4">
                    <span class="text-blue-600 text-3xl"><i class="fas fa-info-circle"></i></span>
                    <p class="text-gray-800 text-lg font-semibold">توفير معلومات واضحة وفورية</p>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg shadow-md flex items-center justify-end gap-4">
                    <span class="text-blue-600 text-3xl"><i class="fas fa-shield-alt"></i></span>
                    <p class="text-gray-800 text-lg font-semibold">تعزيز السلامة (إشارة للخطأ)</p>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg shadow-md flex items-center justify-end gap-4">
                    <span class="text-blue-600 text-3xl"><i class="fas fa-user-check"></i></span>
                    <p class="text-gray-800 text-lg font-semibold">تحسين تجربة المستخدم</p>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg shadow-md flex items-center justify-end gap-4">
                    <span class="text-blue-600 text-3xl"><i class="fas fa-stethoscope"></i></span>
                    <p class="text-gray-800 text-lg font-semibold">المساعدة في التشخيص الأولي</p>
                </div>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 2 / 12</div>
        </div>

        <div class="slide" id="slide-3">
            <h2 class="text-4xl font-bold mb-6">خصائص الـ LED وحساب مقاومة تحديد التيار</h2>
            <p class="text-lg text-gray-700 mb-4">
                الـ LED هو دايود باعث للضوء. له جهد أمامي ($V_f$) وتيار أمامي أقصى ($I_{f\_max}$).
                يجب دائماً استخدام مقاومة على التوالي لتحديد التيار المار خلاله.
            </p>
            <div class="calculator-container">
                <h3 class="text-2xl font-semibold text-center mb-4">حاسبة مقاومة تحديد التيار ($R_{limit}$)</h3>
                <div class="flex flex-col items-end">
                    <label for="vs">جهد المصدر ($V_s$):</label>
                    <input type="number" id="vs" value="5" step="0.1" class="mb-2">
                </div>
                <div class="flex flex-col items-end">
                    <label for="vf">الجهد الأمامي للـ LED ($V_f$):</label>
                    <input type="number" id="vf" value="2.0" step="0.1" class="mb-2">
                </div>
                <div class="flex flex-col items-end">
                    <label for="if_desired">التيار الأمامي المرغوب ($I_{f\_desired}$ بالمللي أمبير):</label>
                    <input type="number" id="if_desired" value="20" step="1" class="mb-2">
                </div>
                <button onclick="calculateResistor()">احسب المقاومة</button>
                <div id="result" class="calculator-result text-center"></div>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 3 / 12</div>
        </div>

        <div class="slide" id="slide-4">
            <h2 class="text-4xl font-bold mb-6">طرق قيادة الـ LEDs: القيادة المباشرة</h2>
            <p class="text-lg text-gray-700 mb-4">
                بعض البوابات المنطقية يمكنها توفير تيار خرج كافٍ لقيادة LED واحد مباشرة (مع مقاومة).
            </p>
            <div class="interactive-circuit">
                <svg viewBox="0 0 300 200">
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="10" refX="5" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="#475569" />
                        </marker>
                    </defs>
                    <rect x="20" y="20" width="20" height="40" fill="#60a5fa" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <line x1="30" y1="20" x2="30" y2="10" stroke="#475569" stroke-width="2"/>
                    <text x="30" y="5" text-anchor="middle" fill="#334155" font-size="12">VCC</text>
                    <line x1="30" y1="60" x2="30" y2="70" stroke="#475569" stroke-width="2"/>
                    <text x="30" y="75" text-anchor="middle" fill="#334155" font-size="12">GND</text>

                    <rect x="80" y="40" width="50" height="30" fill="#bfdbfe" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <text x="105" y="55" text-anchor="middle" fill="#1e3a8a" font-size="12">بوابة منطقية</text>
                    <line x1="130" y1="55" x2="150" y2="55" stroke="#475569" stroke-width="2" marker-end="url(#arrow)"/>

                    <rect x="150" y="45" width="30" height="20" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <text x="165" y="75" text-anchor="middle" fill="#334155" font-size="12">مقاومة</text>
                    <line x1="180" y1="55" x2="200" y2="55" stroke="#475569" stroke-width="2" marker-end="url(#arrow)"/>

                    <circle cx="215" cy="55" r="10" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="direct-led"/>
                    <path d="M210 50 L220 60 M220 50 L210 60" stroke="#475569" stroke-width="1.5"/>
                    <text x="215" y="85" text-anchor="middle" fill="#334155" font-size="12">LED</text>
                    <line x1="215" y1="65" x2="215" y2="75" stroke="#475569" stroke-width="2"/>
                    <text x="215" y="90" text-anchor="middle" fill="#334155" font-size="12">GND</text>

                    <line x1="30" y1="70" x2="215" y2="75" stroke="#475569" stroke-width="2"/>
                    <line x1="105" y1="70" x2="105" y2="80" stroke="#475569" stroke-width="2"/>
                    <line x1="105" y1="80" x2="215" y2="75" stroke="#475569" stroke-width="2"/>

                    <line x1="30" y1="50" x2="80" y2="50" stroke="#475569" stroke-width="2"/>
                </svg>
                <button onclick="toggleDirectLED()">تشغيل / إيقاف LED</button>
            </div>
            <p class="text-lg text-gray-700 mt-4">
                <span class="font-bold text-red-600">المحدودية:</span> تيار الخرج قد لا يكون كافياً لسطوع جيد، أو قد يتم تجاوز قدرة البوابة.
            </p>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 4 / 12</div>
        </div>

        <div class="slide" id="slide-5">
            <h2 class="text-4xl font-bold mb-6">طرق قيادة الـ LEDs: استخدام الترانزستور (BJT)</h2>
            <p class="text-lg text-gray-700 mb-4">
                تيار صغير يمر عبر قاعدة-باعث الترانزستور يتحكم في تيار أكبر بكثير يمر عبر مجمع-باعث.
                (Low-Side Switching)
            </p>
            <div class="interactive-circuit">
                <svg viewBox="0 0 300 200">
                    <defs>
                        <marker id="bjt-arrow" markerWidth="10" markerHeight="10" refX="5" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="#475569" />
                        </marker>
                    </defs>
                    <line x1="50" y1="20" x2="50" y2="40" stroke="#475569" stroke-width="2"/>
                    <text x="50" y="15" text-anchor="middle" fill="#334155" font-size="12">VCC</text>

                    <circle cx="50" cy="70" r="10" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="bjt-led"/>
                    <path d="M45 65 L55 75 M55 65 L45 75" stroke="#475569" stroke-width="1.5"/>
                    <text x="50" y="95" text-anchor="middle" fill="#334155" font-size="12">LED</text>
                    <line x1="50" y1="40" x2="50" y2="60" stroke="#475569" stroke-width="2"/>
                    <line x1="50" y1="80" x2="50" y2="100" stroke="#475569" stroke-width="2"/>

                    <rect x="40" y="40" width="20" height="20" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <text x="50" y="35" text-anchor="middle" fill="#334155" font-size="10">R_LED</text>

                    <path d="M150 100 L150 140 L170 140 L170 100 Z" stroke="#475569" stroke-width="2" fill="none"/>
                    <circle cx="160" cy="120" r="20" stroke="#475569" stroke-width="2" fill="none"/>
                    <line x1="160" y1="100" x2="160" y2="120" stroke="#475569" stroke-width="2"/> <line x1="160" y1="120" x2="160" y2="140" stroke="#475569" stroke-width="2"/> <line x1="140" y1="120" x2="160" y2="120" stroke="#475569" stroke-width="2"/> <polygon points="160,130 155,135 165,135" fill="#475569" transform="rotate(90 160 130)"/> <text x="160" y="170" text-anchor="middle" fill="#334155" font-size="12">NPN BJT</text>
                    <rect x="150" y="100" width="20" height="40" rx="5" ry="5" class="circuit-icon fill-color" id="bjt-body"/>

                    <rect x="180" y="110" width="20" height="20" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <text x="190" y="105" text-anchor="middle" fill="#334155" font-size="10">R_base</text>
                    <line x1="170" y1="120" x2="180" y2="120" stroke="#475569" stroke-width="2"/>

                    <line x1="200" y1="120" x2="220" y2="120" stroke="#475569" stroke-width="2"/>
                    <text x="240" y="120" text-anchor="start" fill="#334155" font-size="12">إشارة تحكم</text>

                    <line x1="160" y1="140" x2="160" y2="160" stroke="#475569" stroke-width="2"/>
                    <path d="M150 160 H170 M155 165 H165 M160 170 H160" stroke="#475569" stroke-width="2"/>
                    <text x="160" y="180" text-anchor="middle" fill="#334155" font-size="12">GND</text>

                    <line x1="50" y1="100" x2="160" y2="100" stroke="#475569" stroke-width="2"/>
                    <line x1="50" y1="100" x2="50" y2="100" stroke="#475569" stroke-width="2"/>
                </svg>
                <button onclick="toggleBJT()">تشغيل / إيقاف الترانزستور</button>
            </div>
            <p class="text-lg text-gray-700 mt-4">
                <span class="font-bold text-blue-600">الدرس المستفاد:</span> ضمان إشباع الترانزستور عند استخدامه كمفتاح أمر حيوي لتقليل تبديد الطاقة.
            </p>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 5 / 12</div>
        </div>

        <div class="slide" id="slide-6">
            <h2 class="text-4xl font-bold mb-6">طرق قيادة الـ LEDs: استخدام الترانزستور (MOSFET)</h2>
            <p class="text-lg text-gray-700 mb-4">
                جهد على طرف البوابة يتحكم في التوصيل بين طرفي المصرف والمنبع. (Low-Side Switching)
            </p>
            <div class="interactive-circuit">
                <svg viewBox="0 0 300 200">
                    <defs>
                        <marker id="mosfet-arrow" markerWidth="10" markerHeight="10" refX="5" refY="5" orient="auto">
                            <path d="M0,0 L10,5 L0,10 Z" fill="#475569" />
                        </marker>
                    </defs>
                    <line x1="50" y1="20" x2="50" y2="40" stroke="#475569" stroke-width="2"/>
                    <text x="50" y="15" text-anchor="middle" fill="#334155" font-size="12">VCC</text>

                    <circle cx="50" cy="70" r="10" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="mosfet-led"/>
                    <path d="M45 65 L55 75 M55 65 L45 75" stroke="#475569" stroke-width="1.5"/>
                    <text x="50" y="95" text-anchor="middle" fill="#334155" font-size="12">LED</text>
                    <line x1="50" y1="40" x2="50" y2="60" stroke="#475569" stroke-width="2"/>
                    <line x1="50" y1="80" x2="50" y2="100" stroke="#475569" stroke-width="2"/>

                    <rect x="40" y="40" width="20" height="20" rx="5" ry="5" class="circuit-icon fill-color"/>
                    <text x="50" y="35" text-anchor="middle" fill="#334155" font-size="10">R_LED</text>

                    <path d="M150 100 L150 140 L170 140 L170 100 Z" stroke="#475569" stroke-width="2" fill="none"/>
                    <line x1="160" y1="100" x2="160" y2="120" stroke="#475569" stroke-width="2"/> <line x1="160" y1="120" x2="160" y2="140" stroke="#475569" stroke-width="2"/> <line x1="140" y1="120" x2="150" y2="120" stroke="#475569" stroke-width="2"/> <line x1="150" y1="110" x2="150" y2="130" stroke="#475569" stroke-width="2"/> <text x="160" y="170" text-anchor="middle" fill="#334155" font-size="12">N-Channel MOSFET</text>
                    <rect x="150" y="100" width="20" height="40" rx="5" ry="5" class="circuit-icon fill-color" id="mosfet-body"/>

                    <line x1="140" y1="120" x2="120" y2="120" stroke="#475569" stroke-width="2"/>
                    <text x="100" y="120" text-anchor="end" fill="#334155" font-size="12">إشارة تحكم</text>

                    <line x1="160" y1="140" x2="160" y2="160" stroke="#475569" stroke-width="2"/>
                    <path d="M150 160 H170 M155 165 H165 M160 170 H160" stroke="#475569" stroke-width="2"/>
                    <text x="160" y="180" text-anchor="middle" fill="#334155" font-size="12">GND</text>

                    <line x1="50" y1="100" x2="160" y2="100" stroke="#475569" stroke-width="2"/>
                    <line x1="50" y1="100" x2="50" y2="100" stroke="#475569" stroke-width="2"/>
                </svg>
                <button onclick="toggleMOSFET()">تشغيل / إيقاف الترانزستور</button>
            </div>
            <p class="text-lg text-gray-700 mt-4">
                <span class="font-bold text-blue-600">الميزة:</span> لا تحتاج إلى حساب تيار قاعدة، وهي فعالة جداً كمفاتيح.
            </p>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 6 / 12</div>
        </div>

        <div class="slide" id="slide-7">
            <h2 class="text-4xl font-bold mb-6">المخطط الصندوقي: قيادة لوحة مؤشرات حالة</h2>
            <p class="text-lg text-gray-700 mb-8">
                مثال لـ 3 مؤشرات حالة
            </p>
            <div class="diagram-container">
                <svg class="block-diagram-svg" viewBox="0 0 800 400">
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" class="arrowhead" />
                        </marker>
                    </defs>

                    <rect x="50" y="150" width="180" height="80" />
                    <text x="140" y="190">وحدة التحكم المنطقية</text>
                    <text x="140" y="210">(e.g., متحكم دقيق)</text>

                    <rect x="300" y="50" width="150" height="60" />
                    <text x="375" y="80">دائرة قيادة المؤشر 1</text>
                    <rect x="300" y="170" width="150" height="60" />
                    <text x="375" y="200">دائرة قيادة المؤشر 2</text>
                    <rect x="300" y="290" width="150" height="60" />
                    <text x="375" y="320">دائرة قيادة المؤشر 3</text>

                    <rect x="550" y="50" width="150" height="60" />
                    <text x="625" y="80">مؤشر LED 1</text>
                    <text x="625" y="95">(e.g., Power ON)</text>
                    <rect x="550" y="170" width="150" height="60" />
                    <text x="625" y="200">مؤشر LED 2</text>
                    <text x="625" y="215">(e.g., Battery Low)</text>
                    <rect x="550" y="290" width="150" height="60" />
                    <text x="625" y="320">مؤشر LED 3</text>
                    <text x="625" y="335">(e.g., Error)</text>

                    <rect x="50" y="50" width="150" height="60" />
                    <text x="125" y="80">مصدر تغذية</text>
                    <text x="125" y="95">(e.g., 5V)</text>

                    <line x1="230" y1="190" x2="300" y2="80" marker-end="url(#arrowhead)" />
                    <text x="265" y="135" font-size="12" fill="#475569" text-anchor="middle">إشارة تحكم 1</text>

                    <line x1="230" y1="190" x2="300" y2="200" marker-end="url(#arrowhead)" />
                    <text x="265" y="210" font-size="12" fill="#475569" text-anchor="middle">إشارة تحكم 2</text>

                    <line x1="230" y1="190" x2="300" y2="320" marker-end="url(#arrowhead)" />
                    <text x="265" y="265" font-size="12" fill="#475569" text-anchor="middle">إشارة تحكم 3</text>

                    <line x1="450" y1="80" x2="550" y2="80" marker-end="url(#arrowhead)" />
                    <text x="500" y="65" font-size="12" fill="#475569" text-anchor="middle">تيار محدد</text>
                    <line x1="450" y1="200" x2="550" y2="200" marker-end="url(#arrowhead)" />
                    <text x="500" y="185" font-size="12" fill="#475569" text-anchor="middle">تيار محدد</text>
                    <line x1="450" y1="320" x2="550" y2="320" marker-end="url(#arrowhead)" />
                    <text x="500" y="305" font-size="12" fill="#475569" text-anchor="middle">تيار محدد</text>

                    <line x1="125" y1="110" x2="125" y2="150" marker-end="url(#arrowhead)" />
                    <line x1="125" y1="150" x2="140" y2="150" marker-end="url(#arrowhead)" />

                    <line x1="125" y1="110" x2="330" y2="110" marker-end="url(#arrowhead)" />
                    <line x1="330" y1="110" x2="330" y2="80" marker-end="url(#arrowhead)" />

                    <line x1="125" y1="110" x2="330" y2="110" marker-end="url(#arrowhead)" />
                    <line x1="330" y1="110" x2="330" y2="80" marker-end="url(#arrowhead)" />

                    <line x1="125" y1="110" x2="330" y2="110" marker-end="url(#arrowhead)" />
                    <line x1="330" y1="110" x2="330" y2="200" marker-end="url(#arrowhead)" />

                    <line x1="125" y1="110" x2="330" y2="110" marker-end="url(#arrowhead)" />
                    <line x1="330" y1="110" x2="330" y2="320" marker-end="url(#arrowhead)" />

                </svg>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 7 / 12</div>
        </div>

        <div class="slide" id="slide-8">
            <h2 class="text-4xl font-bold mb-6">المخطط الكهربائي: قيادة 3 LEDs باستخدام ترانزستورات NPN BJT</h2>
            <p class="text-lg text-gray-700 mb-4">
                مثال على Low-Side Switching
            </p>
            <div class="diagram-container">
                <svg class="circuit-diagram-svg" viewBox="0 0 700 450">
                    <!-- <rect x="0" y="0" width="700" height="450" fill="none" stroke="lightgray" stroke-width="0.5"/>
                    <g stroke="lightgray" stroke-width="0.5">
                        <line x1="100" y1="0" x2="100" y2="450"/>
                        <line x1="200" y1="0" x2="200" y2="450"/>
                        <line x1="300" y1="0" x2="300" y2="450"/>
                        <line x1="400" y1="0" x2="400" y2="450"/>
                        <line x1="500" y1="0" x2="500" y2="450"/>
                        <line x1="600" y1="0" x2="600" y2="450"/>
                        <line x1="0" y1="50" x2="700" y2="50"/>
                        <line x1="0" y1="100" x2="700" y2="100"/>
                        <line x1="0" y1="150" x2="700" y2="150"/>
                        <line x1="0" y1="200" x2="700" y2="200"/>
                        <line x1="0" y1="250" x2="700" y2="250"/>
                        <line x1="0" y1="300" x2="700" y2="300"/>
                        <line x1="0" y1="350" x2="700" y2="350"/>
                        <line x1="0" y1="400" x2="700" y2="400"/>
                    </g> -->

                    <text x="50" y="30" class="power-label">+5V (VCC)</text>
                    <line x1="50" y1="40" x2="50" y2="60" class="line"/>
                    <line x1="40" y1="60" x2="60" y2="60" class="line"/>
                    <line x1="45" y1="65" x2="55" y2="65" class="line"/>
                    <line x1="48" y1="70" x2="52" y2="70" class="line"/>
                    <text x="50" y="85" class="power-label">GND</text>

                    <text x="150" y="100" class="label">CTRL1</text>
                    <text x="150" y="220" class="label">CTRL2</text>
                    <text x="150" y="340" class="label">CTRL3</text>

                    <rect x="250" y="50" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="265" y="40" class="value-label">150 Ω</text>
                    <text x="265" y="75" class="label">R_LED1</text>
                    <line x1="265" y1="50" x2="265" y2="20" class="line"/>
                    <line x1="265" y1="20" x2="50" y2="20" class="line"/>
                    <line x1="50" y1="20" x2="50" y2="40" class="line"/>

                    <circle cx="265" cy="110" r="8" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="schematic-led1"/>
                    <path d="M260 105 L270 115 M270 105 L260 115" stroke="#475569" stroke-width="1.5"/>
                    <text x="265" y="130" class="label">LED1 (أخضر)</text>
                    <line x1="265" y1="65" x2="265" y2="102" class="line"/>
                    <line x1="265" y1="118" x2="265" y2="150" class="line"/>

                    <path d="M265 150 L265 200 M265 200 L280 200 M280 150 L280 200 Z" stroke="#475569" stroke-width="2" fill="none"/>
                    <circle cx="272.5" cy="175" r="15" stroke="#475569" stroke-width="2" fill="none"/>
                    <line x1="265" y1="175" x2="250" y2="175" stroke="#475569" stroke-width="2"/> <polygon points="272.5,185 267.5,190 277.5,190" fill="#475569" transform="rotate(90 272.5 185)"/> <text x="272.5" y="215" class="label">Q1 (NPN)</text>
                    <line x1="272.5" y1="200" x2="272.5" y2="250" class="line"/>
                    <line x1="272.5" y1="250" x2="50" y2="250" class="line"/>
                    <line x1="50" y1="250" x2="50" y2="70" class="line"/>

                    <rect x="180" y="167.5" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="195" y="160" class="value-label">2.2k Ω</text>
                    <text x="195" y="190" class="label">R_base1</text>
                    <line x1="195" y1="175" x2="250" y2="175" class="line"/>
                    <line x1="195" y1="175" x2="150" y2="175" class="line"/>
                    <line x1="150" y1="175" x2="150" y2="100" class="line"/>

                    <rect x="400" y="50" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="415" y="40" class="value-label">150 Ω</text>
                    <text x="415" y="75" class="label">R_LED2</text>
                    <line x1="415" y1="50" x2="415" y2="20" class="line"/>
                    <line x1="415" y1="20" x2="50" y2="20" class="line"/>

                    <circle cx="415" cy="110" r="8" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="schematic-led2"/>
                    <path d="M410 105 L420 115 M420 105 L410 115" stroke="#475569" stroke-width="1.5"/>
                    <text x="415" y="130" class="label">LED2 (أصفر)</text>
                    <line x1="415" y1="65" x2="415" y2="102" class="line"/>
                    <line x1="415" y1="118" x2="415" y2="150" class="line"/>

                    <path d="M415 150 L415 200 M415 200 L430 200 M430 150 L430 200 Z" stroke="#475569" stroke-width="2" fill="none"/>
                    <circle cx="422.5" cy="175" r="15" stroke="#475569" stroke-width="2" fill="none"/>
                    <line x1="415" y1="175" x2="400" y2="175" stroke="#475569" stroke-width="2"/> <polygon points="422.5,185 417.5,190 427.5,190" fill="#475569" transform="rotate(90 422.5 185)"/> <text x="422.5" y="215" class="label">Q2 (NPN)</text>
                    <line x1="422.5" y1="200" x2="422.5" y2="250" class="line"/>
                    <line x1="422.5" y1="250" x2="50" y2="250" class="line"/>

                    <rect x="330" y="167.5" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="345" y="160" class="value-label">2.2k Ω</text>
                    <text x="345" y="190" class="label">R_base2</text>
                    <line x1="345" y1="175" x2="400" y2="175" class="line"/>
                    <line x1="345" y1="175" x2="300" y2="175" class="line"/>
                    <line x1="300" y1="175" x2="300" y2="220" class="line"/>

                    <rect x="550" y="50" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="565" y="40" class="value-label">180 Ω</text>
                    <text x="565" y="75" class="label">R_LED3</text>
                    <line x1="565" y1="50" x2="565" y2="20" class="line"/>
                    <line x1="565" y1="20" x2="50" y2="20" class="line"/>

                    <circle cx="565" cy="110" r="8" stroke="#475569" stroke-width="2" fill="#fcd34d" class="led-circle" id="schematic-led3"/>
                    <path d="M560 105 L570 115 M570 105 L560 115" stroke="#475569" stroke-width="1.5"/>
                    <text x="565" y="130" class="label">LED3 (أحمر)</text>
                    <line x1="565" y1="65" x2="565" y2="102" class="line"/>
                    <line x1="565" y1="118" x2="565" y2="150" class="line"/>

                    <path d="M565 150 L565 200 M565 200 L580 200 M580 150 L580 200 Z" stroke="#475569" stroke-width="2" fill="none"/>
                    <circle cx="572.5" cy="175" r="15" stroke="#475569" stroke-width="2" fill="none"/>
                    <line x1="565" y1="175" x2="550" y2="175" stroke="#475569" stroke-width="2"/> <polygon points="572.5,185 567.5,190 577.5,190" fill="#475569" transform="rotate(90 572.5 185)"/> <text x="572.5" y="215" class="label">Q3 (NPN)</text>
                    <line x1="572.5" y1="200" x2="572.5" y2="250" class="line"/>
                    <line x1="572.5" y1="250" x2="50" y2="250" class="line"/>

                    <rect x="480" y="167.5" width="30" height="15" rx="3" ry="3" class="circuit-icon fill-color"/>
                    <text x="495" y="160" class="value-label">2.2k Ω</text>
                    <text x="495" y="190" class="label">R_base3</text>
                    <line x1="495" y1="175" x2="550" y2="175" class="line"/>
                    <line x1="495" y1="175" x2="450" y2="175" class="line"/>
                    <line x1="450" y1="175" x2="450" y2="340" class="line"/>

                    <line x1="50" y1="70" x2="50" y2="250" class="line"/>
                    <line x1="50" y1="250" x2="272.5" y2="250" class="line"/>
                    <line x1="50" y1="250" x2="422.5" y2="250" class="line"/>
                    <line x1="50" y1="250" x2="572.5" y2="250" class="line"/>

                    <path d="M50 250 H70 M55 255 H65 M60 260 H60" stroke="#475569" stroke-width="2" class="ground-symbol"/>
                </svg>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 8 / 12</div>
        </div>

        <div class="slide" id="slide-9">
            <h2 class="text-4xl font-bold mb-6">مواصفات المكونات الرئيسية: LEDs والترانزستورات</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl text-right">
                <div class="bg-blue-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700"><i class="fas fa-lightbulb mr-2"></i> المؤشرات الضوئية (LEDs)</h3>
                    <ul class="list-disc pr-5">
                        <li>اللون، الجهد الأمامي ($V_f$)، التيار الأمامي ($I_f$)</li>
                        <li>شدة الإضاءة، زاوية الرؤية</li>
                        <li><span class="font-bold">اعتبارات طبية:</span> سطوع كافٍ، عمر تشغيلي طويل، ألوان متناسقة.</li>
                    </ul>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700"><i class="fas fa-microchip mr-2"></i> الترانزستورات (BJT/MOSFET)</h3>
                    <ul class="list-disc pr-5">
                        <li><span class="font-bold">BJT:</span> أقصى تيار للمجمع ($I_c$)، أقصى جهد ($V_{ceo}$)، كسب التيار ($h_{FE}$)، جهد الإشباع ($V_{ce\_sat}$).</li>
                        <li><span class="font-bold">MOSFET:</span> أقصى تيار للمصرف ($I_d$)، أقصى جهد ($V_{ds}$)، مقاومة التشغيل ($R_{ds\_on}$)، جهد العتبة ($V_{gs\_th}$).</li>
                        <li><span class="font-bold">اعتبارات طبية:</span> الموثوقية، التوفر، الحجم، الكفاءة.</li>
                    </ul>
                </div>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 9 / 12</div>
        </div>

        <div class="slide" id="slide-10">
            <h2 class="text-4xl font-bold mb-6">مواصفات المكونات الرئيسية: المقاومات ومصفوفات الترانزستورات</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-4xl text-right">
                <div class="bg-blue-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700"><i class="fas fa-ruler-combined mr-2"></i> المقاومات (Resistors)</h3>
                    <ul class="list-disc pr-5">
                        <li>القيمة الأومية، السماحية (±5% كافية).</li>
                        <li>القدرة المقننة (1/8W أو 1/4W كافية).</li>
                        <li><span class="font-bold">اعتبارات طبية:</span> الاستقرار والموثوقية.</li>
                    </ul>
                </div>
                <div class="bg-blue-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700"><i class="fas fa-layer-group mr-2"></i> مصفوفات الترانزستورات (Transistor Arrays)</h3>
                    <p class="mb-2 text-gray-700">
                        مثل ULN2003A: دوائر متكاملة تحتوي على عدة ترانزستورات دارلينجتون مع دايودات حماية.
                    </p>
                    <ul class="list-disc pr-5">
                        <li><span class="font-bold">الميزة:</span> توفر مساحة وتبسط التصميم عند قيادة عدة مؤشرات.</li>
                        <li><span class="font-bold">متى تستخدم:</span> عند قيادة 5 مؤشرات أو أكثر.</li>
                    </ul>
                </div>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 10 / 12</div>
        </div>

        <div class="slide" id="slide-11">
            <h2 class="text-4xl font-bold mb-6">الخلاصة والمهارات المكتسبة</h2>
            <p class="text-lg text-gray-700 mb-6">
                قيادة لوحة مؤشرات الحالة هي مهارة أساسية في تصميم واجهات المستخدم للأجهزة الإلكترونية، وخاصة الطبية منها.
            </p>
            <h3 class="text-2xl font-semibold mb-4 text-blue-700"><i class="fas fa-graduation-cap mr-2"></i> المهارات المتوقع اكتسابها:</h3>
            <ul class="list-disc pr-5 text-right w-full max-w-2xl">
                <li>حساب مقاومة تحديد التيار اللازمة لـ LED.</li>
                <li>فهم مبدأ عمل الترانزستور BJT و MOSFET كمفتاح.</li>
                <li>حساب مقاومة القاعدة اللازمة لإشباع ترانزستور BJT.</li>
                <li>تصميم دائرة لقيادة LED باستخدام ترانزستور (Low-Side Switching).</li>
                <li>القدرة على اختيار ترانزستور مناسب لتطبيق قيادة LED.</li>
                <li>الوعي بوجود حلول مدمجة مثل مصفوفات الترانزستورات.</li>
            </ul>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 11 / 12</div>
        </div>

        <div class="slide" id="slide-12">
            <h2 class="text-4xl font-bold mb-6">خبرات عملية سابقة ودروس مستفادة</h2>
            <div class="grid grid-cols-1 gap-6 w-full max-w-4xl text-right">
                <div class="bg-red-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-red-700"><i class="fas fa-exclamation-triangle mr-2"></i> "الـ LEDs المحترقة أو الخافتة"</h3>
                    <p class="text-gray-800">
                        <span class="font-bold">المشكلة:</span> عدم استخدام مقاومات تحديد تيار مناسبة أو استخدام نفس القيمة لـ LEDs مختلفة.
                        <span class="font-bold text-green-700">الدرس المستفاد:</span> لا تفترض أن جميع الـ LEDs متشابهة؛ دائماً تحقق من الداتا شيت واحسب المقاومة بدقة.
                    </p>
                </div>
                <div class="bg-red-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-red-700"><i class="fas fa-fire mr-2"></i> "الترانزستور الساخن"</h3>
                    <p class="text-gray-800">
                        <span class="font-bold">المشكلة:</span> مقاومة القاعدة عالية جداً، مما منع إشباع الترانزستور.
                        <span class="font-bold text-green-700">الدرس المستفاد:</span> ضمان إشباع الترانزستور عند استخدامه كمفتاح أمر حيوي لتقليل تبديد الطاقة ومنع ارتفاع درجة الحرارة.
                    </p>
                </div>
                <div class="bg-red-50 p-6 rounded-lg shadow-md">
                    <h3 class="text-2xl font-semibold mb-3 text-red-700"><i class="fas fa-bolt mr-2"></i> "قيادة LED مباشرة من متحكم دقيق ضعيف"</h3>
                    <p class="text-gray-800">
                        <span class="font-bold">المشكلة:</span> المتحكم الدقيق لا يستطيع توفير تيار كافٍ.
                        <span class="font-bold text-green-700">الدرس المستفاد:</span> تحقق دائماً من قدرة تيار الخرج لمصدر إشارة التحكم قبل توصيله مباشرة بحمل.
                    </p>
                </div>
            </div>
            <div class="absolute bottom-10 left-10 text-gray-400 text-sm">Slide 12 / 12</div>
        </div>

    </div>

    <div class="navigation-buttons">
        <button id="prevBtn" class="nav-button"><i class="fas fa-arrow-right mr-2"></i> السابق</button>
        <button id="nextBtn" class="nav-button">التالي <i class="fas fa-arrow-left ml-2"></i></button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active');
                if (i === index) {
                    slide.classList.add('active');
                }
            });
            updateNavigationButtons();
            updateSlideNumber();
        }

        function updateNavigationButtons() {
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function updateSlideNumber() {
            slides.forEach((slide, i) => {
                const slideNumDiv = slide.querySelector('.absolute.bottom-10.left-10');
                if (slideNumDiv) {
                    slideNumDiv.textContent = `Slide ${i + 1} / ${totalSlides}`;
                }
            });
        }

        document.getElementById('nextBtn').addEventListener('click', () => {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        });

        document.getElementById('prevBtn').addEventListener('click', () => {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        });

        // Initial load
        showSlide(currentSlide);

        // LED Resistor Calculator
        function calculateResistor() {
            const vs = parseFloat(document.getElementById('vs').value);
            const vf = parseFloat(document.getElementById('vf').value);
            const ifDesired = parseFloat(document.getElementById('if_desired').value) / 1000; // Convert mA to A

            if (isNaN(vs) || isNaN(vf) || isNaN(ifDesired) || vs <= vf || ifDesired <= 0) {
                document.getElementById('result').textContent = 'الرجاء إدخال قيم صحيحة. (Vs > Vf)';
                document.getElementById('result').classList.remove('text-green-800');
                document.getElementById('result').classList.add('text-red-600');
                return;
            }

            const rLimit = (vs - vf) / ifDesired;
            document.getElementById('result').textContent = `مقاومة تحديد التيار المطلوبة: ${rLimit.toFixed(2)} أوم`;
            document.getElementById('result').classList.remove('text-red-600');
            document.getElementById('result').classList.add('text-green-800');
        }

        // Interactive Direct LED
        let isDirectLEDOn = false;
        function toggleDirectLED() {
            isDirectLEDOn = !isDirectLEDOn;
            const led = document.getElementById('direct-led');
            if (isDirectLEDOn) {
                led.classList.add('led-on');
            } else {
                led.classList.remove('led-on');
            }
        }

        // Interactive BJT Circuit
        let isBJTOn = false;
        function toggleBJT() {
            isBJTOn = !isBJTOn;
            const led = document.getElementById('bjt-led');
            const bjtBody = document.getElementById('bjt-body');
            if (isBJTOn) {
                led.classList.add('led-on');
                bjtBody.classList.add('transistor-active');
            } else {
                led.classList.remove('led-on');
                bjtBody.classList.remove('transistor-active');
            }
        }

        // Interactive MOSFET Circuit
        let isMOSFETOn = false;
        function toggleMOSFET() {
            isMOSFETOn = !isMOSFETOn;
            const led = document.getElementById('mosfet-led');
            const mosfetBody = document.getElementById('mosfet-body');
            if (isMOSFETOn) {
                led.classList.add('led-on');
                mosfetBody.classList.add('transistor-active');
            } else {
                led.classList.remove('led-on');
                mosfetBody.classList.remove('transistor-active');
            }
        }

    </script>
</body>
</html>
