/* Electronics Fundamentals Presentation Styles */

/* Import modern color variables */
:root {
    /* Primary Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --dark-gradient: linear-gradient(135deg, #232526 0%, #414345 100%);
    
    /* Solid Colors */
    --primary-color: #667eea;
    --secondary-color: #f093fb;
    --accent-color: #4facfe;
    --success-color: #43e97b;
    --danger-color: #ff6b6b;
    
    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #a0aec0;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --bg-dark: #1a202c;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-dark);
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Navigation */
.presentation-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 32, 44, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    max-width: 100%;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-white);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-2px);
}

.presentation-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-white);
}

.presentation-controls {
    display: flex;
    gap: 12px;
}

.control-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Presentation Container */
.presentation-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding-top: 80px;
}

/* Slide Navigation */
.slide-navigation {
    background: rgba(26, 32, 44, 0.9);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.slide-counter {
    color: var(--text-white);
    font-weight: 600;
    font-size: 1.1rem;
}

.slide-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.slide-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.slide-btn:hover:not(:disabled) {
    background: var(--primary-gradient);
    transform: scale(1.1);
}

.slide-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.play-btn {
    background: var(--success-gradient);
    width: 50px;
    height: 50px;
}

.slide-progress {
    width: 200px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--accent-gradient);
    width: 5%;
    transition: width 0.5s ease;
}

/* Slides Wrapper */
.slides-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
}

/* Individual Slide */
.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-content {
    width: 100%;
    height: 100%;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

/* Title Slide */
.title-slide {
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
}

.title-animation {
    z-index: 2;
    position: relative;
}

.main-title {
    font-size: 4rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: titleSlideIn 1s ease-out;
}

.subtitle {
    font-size: 2rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    animation: subtitleSlideIn 1s ease-out 0.3s both;
}

.title-icons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.icon-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    animation: iconFloat 1s ease-out both;
}

.icon-item:nth-child(1) { animation-delay: 0.6s; }
.icon-item:nth-child(2) { animation-delay: 0.8s; }
.icon-item:nth-child(3) { animation-delay: 1.0s; }
.icon-item:nth-child(4) { animation-delay: 1.2s; }

.icon-item:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.icon-item i {
    font-size: 3rem;
    color: white;
    margin-bottom: 1rem;
    display: block;
}

.icon-item span {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Animated Background */
.animated-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#titleCanvas {
    width: 100%;
    height: 100%;
}

/* Content Slides */
.slide-header {
    margin-bottom: 2rem;
}

.slide-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.slide-header h2 i {
    font-size: 2rem;
    color: var(--accent-color);
}

.slide-body {
    flex: 1;
    display: flex;
    align-items: center;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    width: 100%;
    align-items: center;
}

.text-content {
    color: white;
}

.text-content h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.95);
}

.text-content p {
    font-size: 1.2rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.85);
}

/* Animated List */
.animated-list {
    list-style: none;
    padding: 0;
}

.animated-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    opacity: 0;
    transform: translateX(-30px);
    animation: listItemSlideIn 0.6s ease-out both;
}

.animated-list li i {
    font-size: 1.5rem;
    color: var(--accent-color);
    min-width: 24px;
}

/* Visual Content */
.visual-content {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Interactive Diagram */
.interactive-diagram {
    width: 100%;
    max-width: 500px;
}

.water-analogy {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
}

.water-tank {
    position: relative;
    width: 200px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.water-level {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 70%;
    background: linear-gradient(180deg, #4facfe 0%, #00f2fe 100%);
    animation: waterRipple 2s ease-in-out infinite;
}

.pressure-gauge {
    position: absolute;
    top: 20px;
    right: -80px;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: var(--radius-lg);
    color: white;
    text-align: center;
    min-width: 80px;
}

.gauge-needle {
    width: 40px;
    height: 2px;
    background: var(--accent-color);
    margin: 10px auto;
    transform-origin: left center;
    animation: needleSwing 3s ease-in-out infinite;
}

.water-pipe {
    position: relative;
    width: 300px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.water-flow {
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent 0%, #4facfe 50%, transparent 100%);
    animation: flowAnimation 2s linear infinite;
}

.flow-meter {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: var(--radius-lg);
    color: white;
    text-align: center;
}

.flow-indicator {
    width: 60px;
    height: 20px;
    background: var(--success-gradient);
    margin: 10px auto;
    border-radius: var(--radius-sm);
    animation: flowPulse 1s ease-in-out infinite;
}

.water-valve {
    position: relative;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.water-valve:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.valve-control {
    width: 40px;
    height: 40px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: valveRotate 4s linear infinite;
}

/* Voltage Simulator */
.voltage-simulator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.battery-demo {
    position: relative;
}

.battery-visual {
    width: 200px;
    height: 100px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border-radius: var(--radius-lg);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-xl);
}

.positive-terminal, .negative-terminal {
    position: absolute;
    width: 30px;
    height: 60px;
    background: #c0c0c0;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    color: #333;
}

.positive-terminal {
    right: -15px;
    background: #ff6b6b;
    color: white;
}

.negative-terminal {
    left: -15px;
    background: #4ecdc4;
    color: white;
}

.voltage-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.electric-field {
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    height: 200px;
}

.field-lines {
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, transparent 30%, rgba(255, 255, 255, 0.1) 70%);
    animation: fieldPulse 2s ease-in-out infinite;
}

.voltage-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.voltage-controls label {
    color: white;
    font-weight: 600;
}

.voltage-controls input[type="range"] {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    outline: none;
    -webkit-appearance: none;
}

.voltage-controls input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    cursor: pointer;
}

.voltage-readout {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
    background: rgba(0, 0, 0, 0.3);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
}

/* Animations */
@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes subtitleSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes iconFloat {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes listItemSlideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes waterRipple {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.95); }
}

@keyframes needleSwing {
    0%, 100% { transform: rotate(-30deg); }
    50% { transform: rotate(30deg); }
}

@keyframes flowAnimation {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes flowPulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

@keyframes valveRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes fieldPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* Current Flow Animation */
.current-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.wire-container {
    position: relative;
    width: 400px;
    height: 200px;
}

.wire {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(90deg, #c0c0c0 0%, #e0e0e0 50%, #c0c0c0 100%);
    border-radius: var(--radius-sm);
    transform: translateY(-50%);
    overflow: hidden;
}

.electron {
    position: absolute;
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #ffeb3b 0%, #ffc107 100%);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    animation: electronMove 2s linear infinite;
    box-shadow: 0 0 10px rgba(255, 235, 59, 0.6);
}

.current-direction, .electron-direction {
    position: absolute;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
}

.current-direction {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.electron-direction {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.current-meter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.meter-display {
    position: relative;
    width: 150px;
    height: 75px;
    background: radial-gradient(circle at bottom, #2d3748 0%, #1a202c 100%);
    border-radius: 75px 75px 0 0;
    border: 3px solid #4a5568;
    overflow: hidden;
}

.meter-needle {
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 2px;
    height: 60px;
    background: var(--danger-color);
    transform-origin: bottom center;
    transform: translateX(-50%) rotate(-45deg);
    animation: meterSwing 3s ease-in-out infinite;
}

.meter-scale {
    position: absolute;
    bottom: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    color: white;
    font-size: 0.8rem;
}

.meter-label {
    color: white;
    font-weight: 600;
    text-align: center;
}

/* Ohm's Law Interactive */
.ohms-triangle-interactive {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.triangle-container {
    position: relative;
}

.ohms-triangle {
    width: 300px;
    height: 260px;
    position: relative;
    cursor: pointer;
}

.triangle-section {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    cursor: pointer;
}

.triangle-section:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.voltage-section {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background: var(--danger-color);
}

.current-section {
    bottom: 0;
    left: 0;
    width: 100px;
    height: 100px;
    background: var(--accent-color);
}

.resistance-section {
    bottom: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: var(--success-color);
}

.triangle-section .variable {
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.triangle-section .unit {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.formula-display {
    margin-top: 2rem;
    padding: 1rem 2rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: var(--radius-lg);
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Calculator Styles */
.calculator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    max-width: 800px;
    margin: 0 auto;
}

.calculator-inputs {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-group label {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.input-group input {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 1rem;
    border-radius: var(--radius-lg);
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--accent-color);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
}

.input-group .unit {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    pointer-events: none;
}

.calculator-results {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.result-display {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
}

.result-item .label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
}

.result-item .value {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.2rem;
}

/* Component Showcase */
.components-showcase {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}

.component-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.component-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.component-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.component-card:hover::before {
    left: 100%;
}

.component-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.component-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.component-card h3 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.component-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
}

.component-animation {
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.value-display {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.1rem;
    background: rgba(0, 0, 0, 0.3);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
}

/* Resistor Visual */
.resistor-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.resistor-body {
    width: 80px;
    height: 20px;
    background: #d4af37;
    border-radius: var(--radius-sm);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 5px;
}

.color-band {
    width: 8px;
    height: 100%;
    border-radius: 1px;
}

.color-band.brown { background: #8b4513; }
.color-band.black { background: #000000; }
.color-band.red { background: #ff0000; }
.color-band.gold { background: #ffd700; }

/* Capacitor Visual */
.capacitor-visual {
    position: relative;
    width: 60px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.capacitor-plates {
    display: flex;
    gap: 4px;
}

.plate {
    width: 20px;
    height: 40px;
    border-radius: var(--radius-sm);
}

.plate.positive {
    background: var(--danger-color);
}

.plate.negative {
    background: var(--accent-color);
}

.electric-field-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(ellipse at center, transparent 30%, rgba(255, 255, 255, 0.2) 70%);
    animation: fieldPulse 2s ease-in-out infinite;
}

/* Inductor Visual */
.inductor-visual {
    position: relative;
    width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.coil {
    width: 60px;
    height: 20px;
    border: 3px solid #c0c0c0;
    border-radius: 50%;
    position: relative;
}

.coil::before,
.coil::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #c0c0c0;
    border-radius: 50%;
    top: -3px;
}

.coil::before {
    left: -10px;
}

.coil::after {
    right: -10px;
}

.magnetic-field {
    position: absolute;
    width: 100%;
    height: 60px;
    background: radial-gradient(ellipse at center, transparent 40%, rgba(76, 175, 80, 0.3) 80%);
    animation: magneticPulse 2s ease-in-out infinite;
}

/* Simulation Modal */
.simulation-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
}

.simulation-modal.active {
    display: flex;
}

.modal-content {
    background: var(--bg-dark);
    border-radius: var(--radius-2xl);
    width: 95%;
    height: 90%;
    max-width: 1400px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.modal-header {
    background: var(--dark-gradient);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.close-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.modal-body {
    flex: 1;
    overflow: hidden;
}

/* Workbench Styles */
.workbench-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.workbench-toolbar {
    background: rgba(45, 55, 72, 0.9);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
    gap: 1rem;
}

.tool-group, .view-group, .action-group {
    display: flex;
    gap: 0.5rem;
}

.tool-btn, .view-btn, .action-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.tool-btn:hover, .view-btn:hover, .action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.tool-btn.active, .view-btn.active {
    background: var(--primary-gradient);
    box-shadow: var(--shadow-md);
}

.action-btn {
    background: var(--success-gradient);
}

.action-btn:hover {
    background: var(--accent-gradient);
}

.workbench-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.component-library {
    width: 300px;
    background: rgba(26, 32, 44, 0.95);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    overflow-y: auto;
}

.component-library h3 {
    color: white;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-categories {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.category {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.category.active {
    background: rgba(255, 255, 255, 0.1);
}

.category h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    padding: 1rem;
    margin: 0;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.category h4:hover {
    background: rgba(255, 255, 255, 0.15);
}

.components-list {
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.component-item {
    background: rgba(255, 255, 255, 0.05);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.component-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(5px);
}

.component-item i {
    font-size: 1.2rem;
    color: var(--accent-color);
    min-width: 20px;
}

.design-canvas {
    flex: 1;
    position: relative;
    background: var(--bg-dark);
    overflow: hidden;
}

#designCanvas {
    width: 100%;
    height: 100%;
    cursor: crosshair;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.grid-overlay {
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Slide Thumbnails */
.slide-thumbnails {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 100;
    max-height: 60vh;
    overflow-y: auto;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.slide-thumbnail {
    width: 80px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    overflow: hidden;
    position: relative;
}

.slide-thumbnail:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.slide-thumbnail.active {
    border-color: var(--accent-color);
    background: rgba(79, 172, 254, 0.2);
}

.thumbnail-preview {
    width: 100%;
    height: 70%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.thumbnail-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.7rem;
    padding: 2px 4px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Interactive Elements */
.voltage-examples {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.example-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
}

.example-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(10px);
}

.example-item i {
    font-size: 1.5rem;
    color: var(--accent-color);
    min-width: 24px;
}

.current-facts {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.fact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--accent-color);
}

.fact-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.current-types {
    margin-top: 2rem;
}

.type-selector {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.type-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.type-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.type-btn.active {
    background: var(--accent-gradient);
    box-shadow: var(--shadow-md);
}

.ohms-law-explanation {
    margin-bottom: 2rem;
}

.formula-container {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    text-align: center;
    margin-top: 1.5rem;
}

.main-formula {
    font-size: 3rem;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(79, 172, 254, 0.3);
}

.formula-variations {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
}

.variation {
    font-size: 1.5rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
}

/* Enhanced Animations */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

/* Slide-specific animations */
.slide[data-slide="1"] .icon-item {
    animation: rotateIn 1s ease-out both;
}

.slide[data-slide="2"] .animated-list li {
    animation: slideInFromLeft 0.6s ease-out both;
}

.slide[data-slide="3"] .voltage-simulator {
    animation: slideInFromRight 0.8s ease-out both;
}

.slide[data-slide="4"] .current-animation {
    animation: slideInFromBottom 0.8s ease-out both;
}

.slide[data-slide="5"] .ohms-triangle-interactive {
    animation: scaleIn 0.8s ease-out both;
}

.slide[data-slide="6"] .calculator-container {
    animation: slideInFromBottom 0.8s ease-out both;
}

.slide[data-slide="7"] .component-grid {
    animation: slideInFromLeft 0.8s ease-out both;
}

/* Example Container Styles */
.example-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    height: 100%;
}

.problem-statement {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.problem-box {
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    margin-top: 1rem;
}

.problem-box ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.problem-box li {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.solution-steps {
    flex: 1;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1rem;
}

.step {
    display: flex;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    border-left: 4px solid var(--accent-color);
    transition: all 0.3s ease;
}

.step:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(10px);
}

.step-number {
    background: var(--accent-gradient);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.step-content h4 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.step-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.calculation {
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: var(--radius-md);
    font-family: 'Courier New', monospace;
}

.calc-line {
    color: var(--accent-color);
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.circuit-diagram {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
}

.pacemaker-circuit-visual {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.circuit-components {
    display: flex;
    align-items: center;
    justify-content: space-around;
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-lg);
    position: relative;
}

.battery-component, .resistor-component, .tissue-component {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.battery-symbol, .resistor-symbol, .tissue-symbol {
    background: var(--primary-gradient);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: bold;
    font-size: 1.2rem;
}

.resistor-symbol {
    background: var(--success-gradient);
}

.tissue-symbol {
    background: var(--secondary-gradient);
}

.current-indicator {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: bold;
}

.safety-notes {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
}

.safety-notes h4 {
    color: #ffc107;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.safety-notes ul {
    list-style: none;
    padding: 0;
}

.safety-notes li {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.safety-notes li::before {
    content: '⚠️';
    position: absolute;
    left: 0;
}

/* Color Code Styles */
.color-code-table {
    margin-top: 1.5rem;
}

.color-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
}

.color-row {
    display: grid;
    grid-template-columns: 40px 1fr 60px;
    gap: 1rem;
    align-items: center;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.color-row:hover {
    background: rgba(255, 255, 255, 0.1);
}

.color-sample {
    width: 30px;
    height: 20px;
    border-radius: var(--radius-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.color-sample.black { background: #000000; }
.color-sample.brown { background: #8b4513; }
.color-sample.red { background: #ff0000; }
.color-sample.orange { background: #ffa500; }
.color-sample.yellow { background: #ffff00; }
.color-sample.green { background: #008000; }

.color-name {
    color: white;
    font-weight: 600;
}

.color-value {
    color: var(--accent-color);
    font-weight: bold;
    text-align: center;
}

/* Interactive Resistor Styles */
.interactive-resistor {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.resistor-builder {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
}

.resistor-visual-large {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.resistor-body-large {
    width: 200px;
    height: 40px;
    background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
    border-radius: var(--radius-md);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 20px;
    box-shadow: var(--shadow-lg);
}

.color-band-large {
    width: 20px;
    height: 100%;
    border-radius: 2px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.resistor-leads {
    display: flex;
    gap: 240px;
}

.lead {
    width: 40px;
    height: 4px;
    background: #c0c0c0;
    border-radius: 2px;
}

.band-selectors {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    width: 100%;
}

.band-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.band-selector label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.band-selector select {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
}

.resistor-value-display {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.value-result {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.value-range {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* Resistance Demo Styles */
.resistance-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.wire-demo {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    width: 100%;
}

.wire-container-demo {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
}

.wire-cross-section {
    position: relative;
    width: 300px;
    height: 60px;
    background: linear-gradient(90deg, #c0c0c0 0%, #e0e0e0 50%, #c0c0c0 100%);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.wire-material {
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #b87333 0%, #daa520 50%, #b87333 100%);
    transition: all 0.3s ease;
}

.current-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 0, 0.3) 2px, transparent 2px);
    background-size: 20px 20px;
    animation: particleFlow 2s linear infinite;
}

.wire-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    width: 100%;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
}

.control-group label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--accent-color);
    border-radius: 50%;
    cursor: pointer;
}

.control-group select {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.5rem;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
}

.resistance-result {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.result-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Power Calculator Styles */
.power-calculator {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    width: 100%;
}

.power-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.power-results {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
}

/* Capacitor Demo Styles */
.capacitor-demo {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.charging-circuit {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
}

.circuit-elements {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
}

.voltage-source-cap {
    background: var(--danger-color);
    color: white;
    padding: 1rem;
    border-radius: 50%;
    font-weight: bold;
}

.switch-element {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.switch-body {
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    position: relative;
    transition: all 0.3s ease;
}

.switch-body::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.switch-element.on .switch-body {
    background: var(--success-color);
}

.switch-element.on .switch-body::before {
    transform: translateX(30px);
}

.resistor-element {
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: bold;
}

.capacitor-element {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.cap-plates {
    display: flex;
    gap: 4px;
}

.plate {
    width: 20px;
    height: 40px;
    border-radius: var(--radius-sm);
}

.positive-plate {
    background: var(--danger-color);
}

.negative-plate {
    background: var(--accent-color);
}

.cap-label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.charging-graph {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: var(--radius-lg);
    text-align: center;
}

.time-constant {
    margin-top: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.charging-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.demo-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.demo-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Inductor Demo Styles */
.inductor-demo {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.magnetic-field-demo {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.coil-visualization {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.coil-turns {
    display: flex;
    gap: 2px;
}

.turn {
    width: 30px;
    height: 60px;
    border: 3px solid #c0c0c0;
    border-radius: 50%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
}

.magnetic-field-lines {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.field-line {
    width: 200px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, var(--success-color) 50%, transparent 100%);
    border-radius: 1px;
    animation: fieldPulse 2s ease-in-out infinite;
}

.field-line:nth-child(2) { animation-delay: 0.5s; }
.field-line:nth-child(3) { animation-delay: 1s; }
.field-line:nth-child(4) { animation-delay: 1.5s; }

.current-direction {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    font-weight: 600;
    background: rgba(0, 0, 0, 0.5);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
}

.inductance-calculator {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.inductance-calculator h5 {
    color: white;
    text-align: center;
    margin-bottom: 1rem;
}

.calc-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.calc-result {
    text-align: center;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
    border-radius: var(--radius-lg);
}

.calc-result p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.inductance-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

/* Additional Animations */
@keyframes particleFlow {
    0% { background-position-x: 0; }
    100% { background-position-x: 20px; }
}

/* Additional Animations */
@keyframes electronMove {
    0% { left: -12px; }
    100% { left: calc(100% + 12px); }
}

@keyframes meterSwing {
    0%, 100% { transform: translateX(-50%) rotate(-45deg); }
    50% { transform: translateX(-50%) rotate(45deg); }
}

@keyframes magneticPulse {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 0.7; transform: scale(1.2); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .main-title {
        font-size: 3rem;
    }

    .title-icons {
        grid-template-columns: repeat(2, 1fr);
    }

    .component-grid {
        grid-template-columns: 1fr;
    }

    .calculator-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .slide-navigation {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .slide-content {
        padding: 1rem;
    }

    .main-title {
        font-size: 2rem;
    }

    .subtitle {
        font-size: 1.2rem;
    }

    .title-icons {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .modal-content {
        width: 98%;
        height: 95%;
    }
}
