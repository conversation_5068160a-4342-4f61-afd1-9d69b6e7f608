<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دائرة أولوية نداء الممرضة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f0f4f8;
            overflow: hidden; /* Hide scrollbars for slide transitions */
        }
        .slide-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            width: 100vw;
            position: relative;
            overflow: hidden;
        }
        .slide {
            background-color: #ffffff;
            border-radius: 1.5rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 90%;
            max-width: 1200px;
            height: 85%;
            padding: 2.5rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: all 0.7s ease-in-out;
            text-align: center;
            overflow-y: auto; /* Allow scrolling for long content */
        }
        .slide.active {
            opacity: 1;
            z-index: 10;
        }
        .slide.prev {
            transform: translate(-150%, -50%);
            opacity: 0;
        }
        .slide.next {
            transform: translate(50%, -50%); /* Start from right for next */
            opacity: 0;
        }
        .slide.active.from-right {
            transform: translate(-50%, -50%); /* Animate in from right */
            animation: slideInFromRight 0.7s forwards;
        }
        .slide.active.from-left {
            transform: translate(-50%, -50%); /* Animate in from left */
            animation: slideInFromLeft 0.7s forwards;
        }
        @keyframes slideInFromRight {
            from { transform: translate(150%, -50%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }
        @keyframes slideInFromLeft {
            from { transform: translate(-150%, -50%); opacity: 0; }
            to { transform: translate(-50%, -50%); opacity: 1; }
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: #3b82f6;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 9999px;
            font-size: 1.5rem;
            cursor: pointer;
            z-index: 20;
            transition: background-color 0.3s ease, transform 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .nav-button:hover {
            background-color: #2563eb;
            transform: translateY(-50%) scale(1.05);
        }
        .nav-button.left {
            left: 2rem;
        }
        .nav-button.right {
            right: 2rem;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            width: 100%;
            margin-top: 2rem;
        }
        .grid-item {
            background-color: #e0f2fe;
            padding: 1.5rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .grid-item:hover {
            transform: translateY(-5px);
        }
        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #1d4ed8;
        }

        .animated-text {
            animation: fadeIn 1s ease-in-out forwards;
            opacity: 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .interactive-table {
            width: 100%;
            margin-top: 2rem;
            border-collapse: collapse;
        }
        .interactive-table th, .interactive-table td {
            border: 1px solid #cbd5e1;
            padding: 0.75rem;
            text-align: center;
        }
        .interactive-table th {
            background-color: #bfdbfe;
            color: #1e40af;
        }
        .interactive-table tr:nth-child(even) {
            background-color: #eff6ff;
        }
        .interactive-table td.highlight {
            background-color: #dbeafe;
            font-weight: bold;
        }
        .circuit-diagram-container {
            width: 100%;
            max-width: 800px;
            margin: 2rem auto;
            background-color: #f9fafb;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            overflow-x: auto; /* Allow horizontal scrolling for wide diagrams */
        }
        .circuit-diagram-svg {
            width: 100%;
            height: auto;
            min-width: 600px; /* Ensure a minimum width for legibility */
        }
        .diagram-label {
            font-size: 0.8rem;
            fill: #4b5563;
        }
        .diagram-component {
            stroke: #1e40af;
            stroke-width: 2;
            fill: #eff6ff;
        }
        .diagram-line {
            stroke: #3b82f6;
            stroke-width: 3; /* Thicker lines for Tinkercad style */
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        .diagram-text {
            font-size: 0.9rem;
            fill: #1e40af;
        }
        /* Tinkercad style components */
        .diagram-led {
            fill: #f87171; /* Red */
            stroke: #b91c1c;
            stroke-width: 2;
            filter: drop-shadow(0 0 5px rgba(248, 113, 113, 0.7)); /* Glow effect */
        }
        .diagram-led.yellow { 
            fill: #facc15; 
            stroke: #a16207; 
            filter: drop-shadow(0 0 5px rgba(250, 204, 21, 0.7));
        }
        .diagram-led.green { 
            fill: #4ade80; 
            stroke: #166534; 
            filter: drop-shadow(0 0 5px rgba(74, 222, 128, 0.7));
        }
        .diagram-switch {
            fill: #60a5fa;
            stroke: #2563eb;
            stroke-width: 2;
            rx: 5; ry: 5; /* Rounded corners */
        }
        .diagram-resistor {
            fill: #d1d5db;
            stroke: #4b5563;
            stroke-width: 2;
            rx: 5; ry: 5; /* Rounded corners */
        }
        .diagram-gate {
            fill: #a7f3d0;
            stroke: #047857;
            stroke-width: 2;
            rx: 10; ry: 10; /* Rounded corners */
        }
        .diagram-pin {
            fill: #1e40af;
            font-size: 0.7rem;
        }
        .diagram-ic-body {
            fill: #e0e7ff;
            stroke: #4f46e5;
            stroke-width: 3; /* Thicker border for ICs */
            rx: 15; ry: 15; /* More rounded ICs */
        }
        .diagram-power-point {
            fill: #ef4444; /* Red for VCC */
            stroke: #b91c1c;
            stroke-width: 2;
        }
        .diagram-ground-point {
            fill: #3b82f6; /* Blue for GND */
            stroke: #1e40af;
            stroke-width: 2;
        }
        .diagram-power-line {
            stroke: #dc2626; /* Red for VCC lines */
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        .diagram-ground-line {
            stroke: #2563eb; /* Blue for GND lines */
            stroke-width: 3;
            stroke-linecap: round;
            stroke-linejoin: round;
        }


        /* Interactive elements for lessons learned */
        .lesson-card {
            background-color: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 1rem;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: right;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .lesson-card:hover {
            transform: translateY(-5px) scale(1.02);
            background-color: #e0f2fe;
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
        }
        .lesson-card h4 {
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 0.5rem;
        }
        .lesson-card p {
            font-size: 0.9rem;
            color: #334155;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .modal.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background-color: white;
            padding: 2rem;
            border-radius: 1rem;
            width: 80%;
            max-width: 600px;
            text-align: right;
            position: relative;
            animation: zoomIn 0.3s ease-out;
        }
        @keyframes zoomIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        .modal-close-button {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
        }
        .modal-close-button:hover {
            color: #1e293b;
        }
        .modal-content h3 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #1e40af;
        }
        .modal-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
            color: #334155;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .slide {
                padding: 1.5rem;
                width: 95%;
                height: 90%;
            }
            .nav-button {
                padding: 0.75rem 1rem;
                font-size: 1.2rem;
            }
            .nav-button.left { left: 0.5rem; }
            .nav-button.right { right: 0.5rem; }
            .grid-container {
                grid-template-columns: 1fr;
            }
            .icon {
                font-size: 2.5rem;
            }
            .modal-content {
                width: 95%;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>

    <div class="slide-container">
        <div id="slide-1" class="slide active">
            <h1 class="text-5xl font-extrabold text-blue-800 mb-6 animated-text">أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</h1>
            <h2 class="text-3xl font-bold text-blue-600 mb-4 animated-text" style="animation-delay: 0.3s;">المستوى الأول: تصميم دوائر بسيطة</h2>
            <h3 class="text-4xl font-bold text-blue-700 mb-8 animated-text" style="animation-delay: 0.6s;">الحلقة 2: دائرة "أولوية نداء الممرضة"</h3>
            <p class="text-xl text-gray-700 animated-text" style="animation-delay: 0.9s;">تقديم: د. محمد يعقوب إسماعيل</p>
            <p class="text-lg text-gray-600 animated-text" style="animation-delay: 1.2s;">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
        </div>

        <div id="slide-2" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">مقدمة ومفاهيم أساسية</h2>
            <div class="grid-container">
                <div class="grid-item animated-text" style="animation-delay: 0.2s;">
                    <span class="icon">🏥</span>
                    <h4 class="text-xl font-semibold">أهمية الدائرة</h4>
                    <p>تنظيم نداءات المرضى المتزامنة في المستشفيات ومراكز الرعاية.</p>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.4s;">
                    <span class="icon">💡</span>
                    <h4 class="text-xl font-semibold">الفكرة الأساسية</h4>
                    <p>تحديد النداء ذي الأولوية القصوى وعرضه بوضوح أو إرسال إشارة مميزة.</p>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.6s;">
                    <span class="icon">🧠</span>
                    <h4 class="text-xl font-semibold">المفاهيم المغطاة</h4>
                    <ul class="list-disc list-inside text-right">
                        <li>البوابات المنطقية الأساسية (AND, OR, NOT, XOR).</li>
                        <li>تصميم دوائر التشفير الأولوية البسيطة.</li>
                        <li>قيادة المؤشرات الضوئية (LEDs).</li>
                        <li>توسيع الدائرة لعدد أكبر من المدخلات.</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="slide-3" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">أهمية الدائرة في الأجهزة الطبية</h2>
            <div class="grid-container">
                <div class="grid-item animated-text" style="animation-delay: 0.2s;">
                    <span class="icon">⏱️</span>
                    <h4 class="text-xl font-semibold">تحسين كفاءة الاستجابة</h4>
                    <p>توجيه انتباه فريق التمريض للنداءات الأكثر أهمية أولاً.</p>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.4s;">
                    <span class="icon">🛡️</span>
                    <h4 class="text-xl font-semibold">تعزيز سلامة المرضى</h4>
                    <p>ضمان عدم إغفال النداءات الحرجة وسط نداءات أخرى.</p>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.6s;">
                    <span class="icon">📊</span>
                    <h4 class="text-xl font-semibold">تنظيم سير العمل</h4>
                    <p>مساعدة الممرضات على إدارة وقتهن بشكل أفضل.</p>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.8s;">
                    <span class="icon">🔗</span>
                    <h4 class="text-xl font-semibold">التكامل مع أنظمة الإنذار</h4>
                    <p>يمكن أن تكون جزءاً من نظام إنذار أكبر يتلقى إشارات من أجهزة مراقبة.</p>
                </div>
            </div>
        </div>

        <div id="slide-4" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">النظرية الرئيسية وآلية العمل</h2>
            <p class="text-xl text-gray-700 mb-6 animated-text" style="animation-delay: 0.2s;">تعتمد دوائر الأولوية على مبدأ: إذا كان هناك عدة طلبات متزامنة، يتم خدمة الطلب ذي الأولوية الأعلى أولاً.</p>

            <h3 class="text-2xl font-bold text-blue-600 mb-4 animated-text" style="animation-delay: 0.4s;">آلية عمل مقترحة (3 مدخلات)</h3>
            <div class="flex flex-col md:flex-row justify-center items-center gap-6 w-full animated-text" style="animation-delay: 0.6s;">
                <div class="bg-red-100 p-4 rounded-lg shadow-md w-full md:w-1/3">
                    <p class="font-semibold text-red-700">المدخل A: أولوية عالية (نداء طارئ)</p>
                </div>
                <div class="bg-yellow-100 p-4 rounded-lg shadow-md w-full md:w-1/3">
                    <p class="font-semibold text-yellow-700">المدخل B: أولوية متوسطة (مساعدة عادية)</p>
                </div>
                <div class="bg-green-100 p-4 rounded-lg shadow-md w-full md:w-1/3">
                    <p class="font-semibold text-green-700">المدخل C: أولوية منخفضة (طلب روتيني)</p>
                </div>
            </div>

            <h3 class="text-2xl font-bold text-blue-600 mt-8 mb-4 animated-text" style="animation-delay: 0.8s;">المنطق المطلوب (جدول الحقيقة التفاعلي)</h3>
            <table class="interactive-table animated-text" style="animation-delay: 1s;">
                <thead>
                    <tr>
                        <th>المدخل A (عالية)</th>
                        <th>المدخل B (متوسطة)</th>
                        <th>المدخل C (منخفضة)</th>
                        <th>خرج الأولوية A</th>
                        <th>خرج الأولوية B</th>
                        <th>خرج الأولوية C</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-a="0" data-b="0" data-c="0"><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr>
                    <tr data-a="0" data-b="0" data-c="1"><td>0</td><td>0</td><td>1</td><td>0</td><td>0</td><td class="highlight">1</td></tr>
                    <tr data-a="0" data-b="1" data-c="0"><td>0</td><td>1</td><td>0</td><td>0</td><td class="highlight">1</td><td>0</td></tr>
                    <tr data-a="0" data-b="1" data-c="1"><td>0</td><td>1</td><td>1</td><td>0</td><td class="highlight">1</td><td>0</td></tr>
                    <tr data-a="1" data-b="0" data-c="0"><td>1</td><td>0</td><td>0</td><td class="highlight">1</td><td>0</td><td>0</td></tr>
                    <tr data-a="1" data-b="0" data-c="1"><td>1</td><td>0</td><td>1</td><td class="highlight">1</td><td>0</td><td>0</td></tr>
                    <tr data-a="1" data-b="1" data-c="0"><td>1</td><td>1</td><td>0</td><td class="highlight">1</td><td>0</td><td>0</td></tr>
                    <tr data-a="1" data-b="1" data-c="1"><td>1</td><td>1</td><td>1</td><td class="highlight">1</td><td>0</td><td>0</td></tr>
                </tbody>
            </table>
            <p class="text-sm text-gray-500 mt-2">انقر على صف في الجدول لتسليط الضوء على حالة معينة.</p>
        </div>

        <div id="slide-5" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">تنفيذ المنطق باستخدام البوابات</h2>
            <div class="flex flex-col gap-8 w-full max-w-2xl animated-text" style="animation-delay: 0.2s;">
                <div class="bg-blue-50 p-6 rounded-lg shadow-md text-right">
                    <h3 class="text-2xl font-bold text-blue-800 mb-2">خرج الأولوية العالية (Output_A_High_Priority)</h3>
                    <p class="text-lg text-gray-700"><code>Output_A_High_Priority = A</code></p>
                    <p class="text-md text-gray-600 mt-2">ببساطة، إذا كان A نشطاً، فهذا هو الخرج.</p>
                    <div class="flex justify-center items-center mt-4">
                        <svg width="100" height="60" viewBox="0 0 100 60">
                            <rect x="0" y="20" width="40" height="20" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="5" ry="5"/>
                            <text x="20" y="35" text-anchor="middle" font-size="12" fill="#047857">A</text>
                            <circle cx="45" cy="30" r="5" fill="#047857"/>
                            <line x1="50" y1="30" x2="80" y2="30" stroke="#3b82f6" stroke-width="2"/>
                            <text x="90" y="35" text-anchor="middle" font-size="12" fill="#1e40af">Out A</text>
                        </svg>
                    </div>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg shadow-md text-right animated-text" style="animation-delay: 0.4s;">
                    <h3 class="text-2xl font-bold text-blue-800 mb-2">خرج الأولوية المتوسطة (Output_B_Medium_Priority)</h3>
                    <p class="text-lg text-gray-700"><code>Output_B_Medium_Priority = B AND (NOT A)</code></p>
                    <p class="text-md text-gray-600 mt-2">يجب أن يكون B نشطاً و A غير نشط.</p>
                    <div class="flex justify-center items-center mt-4">
                        <svg width="200" height="80" viewBox="0 0 200 80">
                            <rect x="0" y="10" width="40" height="20" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="5" ry="5"/>
                            <text x="20" y="25" text-anchor="middle" font-size="10" fill="#047857">A</text>
                            <circle cx="45" cy="20" r="5" fill="#047857"/>
                            <line x1="50" y1="20" x2="70" y2="20" stroke="#3b82f6" stroke-width="1.5"/>
                            <text x="60" y="15" font-size="10" fill="#4b5563">NOT A</text>

                            <line x1="0" y1="60" x2="70" y2="60" stroke="#3b82f6" stroke-width="1.5"/>
                            <text x="10" y="55" font-size="10" fill="#4b5563">B</text>

                            <path d="M70,20 L100,20 L100,60 L70,60 C80,50 80,30 70,20 Z" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="10" ry="10"/>
                            <text x="85" y="40" text-anchor="middle" font-size="12" fill="#047857">AND</text>
                            <line x1="100" y1="40" x2="130" y2="40" stroke="#3b82f6" stroke-width="2"/>
                            <text x="160" y="45" text-anchor="middle" font-size="12" fill="#1e40af">Out B</text>
                        </svg>
                    </div>
                </div>

                <div class="bg-blue-50 p-6 rounded-lg shadow-md text-right animated-text" style="animation-delay: 0.6s;">
                    <h3 class="text-2xl font-bold text-blue-800 mb-2">خرج الأولوية المنخفضة (Output_C_Low_Priority)</h3>
                    <p class="text-lg text-gray-700"><code>Output_C_Low_Priority = C AND (NOT A) AND (NOT B)</code></p>
                    <p class="text-md text-gray-600 mt-2">يجب أن يكون C نشطاً و A غير نشط و B غير نشط.</p>
                    <div class="flex justify-center items-center mt-4">
                        <svg width="250" height="100" viewBox="0 0 250 100">
                            <rect x="0" y="10" width="40" height="20" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="5" ry="5"/>
                            <text x="20" y="25" text-anchor="middle" font-size="10" fill="#047857">A</text>
                            <circle cx="45" cy="20" r="5" fill="#047857"/>
                            <line x1="50" y1="20" x2="90" y2="20" stroke="#3b82f6" stroke-width="1.5"/>
                            <text x="70" y="15" font-size="10" fill="#4b5563">NOT A</text>

                            <rect x="0" y="40" width="40" height="20" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="5" ry="5"/>
                            <text x="20" y="55" text-anchor="middle" font-size="10" fill="#047857">B</text>
                            <circle cx="45" cy="50" r="5" fill="#047857"/>
                            <line x1="50" y1="50" x2="90" y2="50" stroke="#3b82f6" stroke-width="1.5"/>
                            <text x="70" y="45" font-size="10" fill="#4b5563">NOT B</text>

                            <line x1="0" y1="80" x2="90" y2="80" stroke="#3b82f6" stroke-width="1.5"/>
                            <text x="10" y="75" font-size="10" fill="#4b5563">C</text>

                            <path d="M90,10 L140,10 L140,90 L90,90 C110,70 110,30 90,10 Z" fill="#a7f3d0" stroke="#047857" stroke-width="2" rx="10" ry="10"/>
                            <text x="115" y="50" text-anchor="middle" font-size="12" fill="#047857">AND</text>
                            <line x1="140" y1="50" x2="180" y2="50" stroke="#3b82f6" stroke-width="2"/>
                            <text x="210" y="55" text-anchor="middle" font-size="12" fill="#1e40af">Out C</text>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div id="slide-6" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">المخطط الصندوقي: دائرة أولوية نداء الممرضة (3 مدخلات)</h2>
            <div class="circuit-diagram-container animated-text" style="animation-delay: 0.2s;">
                <svg class="circuit-diagram-svg" viewBox="0 0 800 400">
                    <rect id="input-a-block" x="50" y="50" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="125" y="80" text-anchor="middle" class="diagram-text">مدخل نداء A (أولوية عالية)</text>

                    <rect id="input-b-block" x="50" y="150" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="125" y="180" text-anchor="middle" class="diagram-text">مدخل نداء B (أولوية متوسطة)</text>

                    <rect id="input-c-block" x="50" y="250" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="125" y="280" text-anchor="middle" class="diagram-text">مدخل نداء C (أولوية منخفضة)</text>

                    <rect id="logic-unit-block" x="300" y="125" width="200" height="100" rx="15" ry="15" class="diagram-component" />
                    <text x="400" y="180" text-anchor="middle" class="diagram-text font-bold text-xl">وحدة منطق الأولوية</text>

                    <rect id="output-a-block" x="600" y="50" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="675" y="80" text-anchor="middle" class="diagram-text">مؤشر/خرج أولوية A</text>

                    <rect id="output-b-block" x="600" y="150" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="675" y="180" text-anchor="middle" class="diagram-text">مؤشر/خرج أولوية B</text>

                    <rect id="output-c-block" x="600" y="250" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="675" y="280" text-anchor="middle" class="diagram-text">مؤشر/خرج أولوية C</text>

                    <rect id="power-supply-block" x="50" y="330" width="150" height="50" rx="10" ry="10" class="diagram-component" />
                    <text x="125" y="360" text-anchor="middle" class="diagram-text">مصدر تغذية (e.g., 5V)</text>

                    <line x1="200" y1="75" x2="300" y2="150" class="diagram-line" marker-end="url(#arrowhead)" />
                    <line x1="200" y1="175" x2="300" y2="175" class="diagram-line" marker-end="url(#arrowhead)" />
                    <line x1="200" y1="275" x2="300" y2="200" class="diagram-line" marker-end="url(#arrowhead)" />

                    <line x1="500" y1="160" x2="600" y2="75" class="diagram-line" marker-end="url(#arrowhead)" />
                    <text x="550" y="135" text-anchor="middle" class="diagram-label">إشارة أولوية عالية</text>

                    <line x1="500" y1="180" x2="600" y2="175" class="diagram-line" marker-end="url(#arrowhead)" />
                    <text x="550" y="200" text-anchor="middle" class="diagram-label">إشارة أولوية متوسطة</text>

                    <line x1="500" y1="200" x2="600" y2="275" class="diagram-line" marker-end="url(#arrowhead)" />
                    <text x="550" y="245" text-anchor="middle" class="diagram-label">إشارة أولوية منخفضة</text>

                    <line x1="125" y1="330" x2="125" y2="300" class="diagram-line" />
                    <line x1="125" y1="330" x2="125" y2="200" class="diagram-line" />
                    <line x1="125" y1="330" x2="125" y2="100" class="diagram-line" />
                    <line x1="125" y1="330" x2="350" y2="125" class="diagram-line" />
                    <line x1="125" y1="330" x2="650" y2="50" class="diagram-line" />
                    <line x1="125" y1="330" x2="650" y2="150" class="diagram-line" />
                    <line x1="125" y1="330" x2="650" y2="250" class="diagram-line" />

                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                        </marker>
                    </defs>
                </svg>
            </div>
        </div>

        <div id="slide-7" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">المخطط الكهربائي: نظرة عامة</h2>
            <p class="text-xl text-gray-700 mb-6 animated-text" style="animation-delay: 0.2s;">دائرة أولوية نداء الممرضة (3 مدخلات باستخدام بوابات منطقية CD4000 Series)</p>
            <div class="grid-container animated-text" style="animation-delay: 0.4s;">
                <div class="grid-item">
                    <span class="icon">🔌</span>
                    <h4 class="text-xl font-semibold">سلسلة CD4000 CMOS</h4>
                    <p>تعمل على نطاق واسع من جهود التغذية (3V-18V)، استهلاك منخفض للطاقة، مناعة جيدة للضوضاء.</p>
                </div>
                <div class="grid-item">
                    <span class="icon">➖</span>
                    <h4 class="text-xl font-semibold">بوابات NOT (العاكس)</h4>
                    <p>مثلاً: CD4069 Hex Inverter.</p>
                </div>
                <div class="grid-item">
                    <span class="icon">➕</span>
                    <h4 class="text-xl font-semibold">بوابات AND</h4>
                    <p>مثلاً: CD4081 Quad 2-Input AND Gate أو CD4073 Triple 3-Input AND Gate.</p>
                </div>
            </div>
            <p class="text-lg text-gray-600 mt-8 animated-text" style="animation-delay: 0.6s;">الآن دعونا نلقي نظرة على الرسم التخطيطي الكامل للدائرة.</p>
        </div>

        <div id="slide-8" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">المخطط الكهربائي التفصيلي</h2>
            <div class="circuit-diagram-container animated-text" style="animation-delay: 0.2s;">
                <svg class="circuit-diagram-svg" viewBox="0 0 900 600">
                    <circle cx="50" cy="50" r="15" class="diagram-power-point"/>
                    <text x="50" y="30" text-anchor="middle" class="diagram-label font-bold">VCC</text>
                    <line x1="50" y1="65" x2="50" y2="100" class="diagram-power-line"/>
                    
                    <circle cx="50" cy="150" r="15" class="diagram-ground-point"/>
                    <text x="50" y="130" text-anchor="middle" class="diagram-label font-bold">GND</text>
                    <line x1="50" y1="165" x2="50" y2="200" class="diagram-ground-line"/>
                    

                    <rect x="100" y="200" width="40" height="40" rx="10" ry="10" class="diagram-switch"/>
                    <circle cx="120" cy="220" r="10" fill="white" stroke="#2563eb" stroke-width="2"/>
                    <text x="120" y="225" text-anchor="middle" class="diagram-label font-bold text-xs">A</text>
                    <line x1="120" y1="200" x2="120" y2="170" class="diagram-line"/>
                    <line x1="120" y1="240" x2="120" y2="270" class="diagram-line"/>
                    <text x="120" y="190" text-anchor="middle" class="diagram-label">Call A</text>
                    <line x1="120" y1="170" x2="50" y2="50" class="diagram-power-line"/> <rect x="110" y="270" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="120" y="295" text-anchor="middle" class="diagram-label text-xs">10kΩ</text>
                    <line x1="120" y1="310" x2="120" y2="340" class="diagram-line"/>
                    <line x1="120" y1="340" x2="50" y2="150" class="diagram-ground-line"/> <rect x="100" y="300" width="40" height="40" rx="10" ry="10" class="diagram-switch"/>
                    <circle cx="120" cy="320" r="10" fill="white" stroke="#2563eb" stroke-width="2"/>
                    <text x="120" y="325" text-anchor="middle" class="diagram-label font-bold text-xs">B</text>
                    <line x1="120" y1="300" x2="120" y2="270" class="diagram-line"/>
                    <line x1="120" y1="340" x2="120" y2="370" class="diagram-line"/>
                    <text x="120" y="290" text-anchor="middle" class="diagram-label">Call B</text>
                    <line x1="120" y1="270" x2="50" y2="50" class="diagram-power-line"/> <rect x="110" y="370" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="120" y="395" text-anchor="middle" class="diagram-label text-xs">10kΩ</text>
                    <line x1="120" y1="410" x2="120" y2="440" class="diagram-line"/>
                    <line x1="120" y1="440" x2="50" y2="150" class="diagram-ground-line"/> <rect x="100" y="400" width="40" height="40" rx="10" ry="10" class="diagram-switch"/>
                    <circle cx="120" cy="420" r="10" fill="white" stroke="#2563eb" stroke-width="2"/>
                    <text x="120" y="425" text-anchor="middle" class="diagram-label font-bold text-xs">C</text>
                    <line x1="120" y1="400" x2="120" y2="370" class="diagram-line"/>
                    <line x1="120" y1="440" x2="120" y2="470" class="diagram-line"/>
                    <text x="120" y="390" text-anchor="middle" class="diagram-label">Call C</text>
                    <line x1="120" y1="370" x2="50" y2="50" class="diagram-power-line"/> <rect x="110" y="470" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="120" y="495" text-anchor="middle" class="diagram-label text-xs">10kΩ</text>
                    <line x1="120" y1="510" x2="120" y2="540" class="diagram-line"/>
                    <line x1="120" y1="540" x2="50" y2="150" class="diagram-ground-line"/> <rect x="250" y="180" width="120" height="180" rx="15" ry="15" class="diagram-ic-body"/>
                    <text x="310" y="200" text-anchor="middle" class="diagram-text font-bold">U1</text>
                    <text x="310" y="215" text-anchor="middle" class="diagram-label">CD4069</text>
                    <circle cx="240" cy="240" r="4" fill="#1e40af"/> <text x="225" y="245" class="diagram-pin">1</text>
                    <circle cx="380" cy="240" r="4" fill="#1e40af"/> <text x="395" y="245" class="diagram-pin">2</text>
                    <circle cx="240" cy="290" r="4" fill="#1e40af"/> <text x="225" y="295" class="diagram-pin">3</text>
                    <circle cx="380" cy="290" r="4" fill="#1e40af"/> <text x="395" y="295" class="diagram-pin">4</text>
                    <text x="310" y="170" text-anchor="middle" class="diagram-pin">VDD (14)</text>
                    <text x="310" y="375" text-anchor="middle" class="diagram-pin">VSS (7)</text>
                    <line x1="310" y1="180" x2="50" y2="50" class="diagram-power-line"/> <line x1="310" y1="360" x2="50" y2="150" class="diagram-ground-line"/> <line x1="120" y1="240" x2="240" y2="240" class="diagram-line"/> <line x1="120" y1="340" x2="240" y2="290" class="diagram-line"/> <line x1="380" y1="240" x2="420" y2="240" class="diagram-line"/> <line x1="380" y1="290" x2="420" y2="290" class="diagram-line"/> <rect x="450" y="180" width="120" height="250" rx="15" ry="15" class="diagram-ic-body"/>
                    <text x="510" y="200" text-anchor="middle" class="diagram-text font-bold">U2</text>
                    <text x="510" y="215" text-anchor="middle" class="diagram-label">CD4073</text>
                    <circle cx="440" cy="240" r="4" fill="#1e40af"/> <text x="425" y="245" class="diagram-pin">1</text>
                    <circle cx="440" cy="270" r="4" fill="#1e40af"/> <text x="425" y="275" class="diagram-pin">2</text>
                    <circle cx="440" cy="300" r="4" fill="#1e40af"/> <text x="425" y="305" class="diagram-pin">3</text>
                    <circle cx="440" cy="330" r="4" fill="#1e40af"/> <text x="425" y="335" class="diagram-pin">4</text>
                    <circle cx="440" cy="360" r="4" fill="#1e40af"/> <text x="425" y="365" class="diagram-pin">5</text>
                    <circle cx="580" cy="255" r="4" fill="#1e40af"/> <text x="595" y="260" class="diagram-pin">13</text>
                    <circle cx="580" cy="345" r="4" fill="#1e40af"/> <text x="595" y="350" class="diagram-pin">6</text>
                    <text x="510" y="170" text-anchor="middle" class="diagram-pin">VDD (14)</text>
                    <text x="510" y="440" text-anchor="middle" class="diagram-pin">VSS (7)</text>
                    <line x1="510" y1="180" x2="50" y2="50" class="diagram-power-line"/> <line x1="510" y1="430" x2="50" y2="150" class="diagram-ground-line"/> <line x1="120" y1="340" x2="440" y2="240" class="diagram-line"/> <line x1="420" y1="240" x2="440" y2="270" class="diagram-line"/> <line x1="50" y1="50" x2="440" y2="300" class="diagram-power-line"/> <line x1="580" y1="255" x2="620" y2="255" class="diagram-line"/> <line x1="120" y1="440" x2="440" y2="330" class="diagram-line"/> <line x1="420" y1="240" x2="440" y2="360" class="diagram-line"/> <line x1="420" y1="290" x2="470" y2="360" class="diagram-line"/> <line x1="580" y1="345" x2="620" y2="345" class="diagram-line"/> <circle cx="700" cy="220" r="15" class="diagram-led"/>
                    <text x="700" y="195" text-anchor="middle" class="diagram-label font-bold">LED A</text>
                    <line x1="120" y1="240" x2="685" y2="220" class="diagram-line"/> <rect x="690" y="245" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="700" y="270" text-anchor="middle" class="diagram-label text-xs">470Ω</text>
                    <line x1="700" y1="285" x2="700" y2="315" class="diagram-line"/>
                    <line x1="700" y1="315" x2="50" y2="150" class="diagram-ground-line"/> <circle cx="700" cy="320" r="15" class="diagram-led yellow"/>
                    <text x="700" y="295" text-anchor="middle" class="diagram-label font-bold">LED B</text>
                    <line x1="620" y1="255" x2="685" y2="320" class="diagram-line"/> <rect x="690" y="345" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="700" y="370" text-anchor="middle" class="diagram-label text-xs">470Ω</text>
                    <line x1="700" y1="385" x2="700" y2="415" class="diagram-line"/>
                    <line x1="700" y1="415" x2="50" y2="150" class="diagram-ground-line"/> <circle cx="700" cy="420" r="15" class="diagram-led green"/>
                    <text x="700" y="395" text-anchor="middle" class="diagram-label font-bold">LED C</text>
                    <line x1="620" y1="345" x2="685" y2="420" class="diagram-line"/> <rect x="690" y="445" width="20" height="40" rx="5" ry="5" class="diagram-resistor"/>
                    <text x="700" y="470" text-anchor="middle" class="diagram-label text-xs">470Ω</text>
                    <line x1="700" y1="485" x2="700" y2="515" class="diagram-line"/>
                    <line x1="700" y1="515" x2="50" y2="150" class="diagram-ground-line"/> <g id="path-a" class="hidden">
                        <line x1="120" y1="240" x2="685" y2="220" stroke="red" stroke-width="5" stroke-dasharray="5,5"/>
                        <circle cx="700" cy="220" r="18" fill="none" stroke="red" stroke-width="3"/>
                    </g>
                    <g id="path-b" class="hidden">
                        <line x1="120" y1="340" x2="440" y2="240" stroke="yellow" stroke-width="5" stroke-dasharray="5,5"/>
                        <line x1="420" y1="240" x2="440" y2="270" stroke="yellow" stroke-width="5" stroke-dasharray="5,5"/>
                        <line x1="620" y1="255" x2="685" y2="320" stroke="yellow" stroke-width="5" stroke-dasharray="5,5"/>
                        <circle cx="700" cy="320" r="18" fill="none" stroke="yellow" stroke-width="3"/>
                    </g>
                    <g id="path-c" class="hidden">
                        <line x1="120" y1="440" x2="440" y2="330" stroke="lime" stroke-width="5" stroke-dasharray="5,5"/>
                        <line x1="420" y1="240" x2="440" y2="360" stroke="lime" stroke-width="5" stroke-dasharray="5,5"/>
                        <line x1="420" y1="290" x2="470" y2="360" stroke="lime" stroke-width="5" stroke-dasharray="5,5"/>
                        <line x1="620" y1="345" x2="685" y2="420" stroke="lime" stroke-width="5" stroke-dasharray="5,5"/>
                        <circle cx="700" cy="420" r="18" fill="none" stroke="lime" stroke-width="3"/>
                    </g>

                    <rect x="100" y="200" width="40" height="40" rx="10" ry="10" class="diagram-switch cursor-pointer hover:fill-blue-400" onclick="simulateCall('A')"/>
                    <rect x="100" y="300" width="40" height="40" rx="10" ry="10" class="diagram-switch cursor-pointer hover:fill-blue-400" onclick="simulateCall('B')"/>
                    <rect x="100" y="400" width="40" height="40" rx="10" ry="10" class="diagram-switch cursor-pointer hover:fill-blue-400" onclick="simulateCall('C')"/>
                </svg>
            </div>
            <p class="text-sm text-gray-500 mt-2">انقر على المفاتيح (SW_A, SW_B, SW_C) لمحاكاة النداءات ومشاهدة مسار الإشارة.</p>
        </div>

        <div id="slide-9" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">مواصفات المكونات الرئيسية</h2>
            <div class="grid-container">
                <div class="grid-item animated-text" style="animation-delay: 0.2s;">
                    <span class="icon">🔠</span>
                    <h4 class="text-xl font-semibold">البوابات المنطقية</h4>
                    <ul class="list-disc list-inside text-right">
                        <li>عائلة المنطق (CMOS / TTL)</li>
                        <li>جهد التغذية</li>
                        <li>تيار الخرج</li>
                        <li>سرعة الانتشار</li>
                    </ul>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.4s;">
                    <span class="icon">〰️</span>
                    <h4 class="text-xl font-semibold">المقاومات</h4>
                    <ul class="list-disc list-inside text-right">
                        <li>القيمة الأومية (Pull-down, LED)</li>
                        <li>السماحية</li>
                        <li>القدرة المقننة</li>
                    </ul>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.6s;">
                    <span class="icon">💡</span>
                    <h4 class="text-xl font-semibold">المؤشرات الضوئية (LEDs)</h4>
                    <ul class="list-disc list-inside text-right">
                        <li>ألوان مميزة للأولوية</li>
                        <li>خصائص الجهد والتيار</li>
                    </ul>
                </div>
                <div class="grid-item animated-text" style="animation-delay: 0.8s;">
                    <span class="icon">🔘</span>
                    <h4 class="text-xl font-semibold">المفاتيح</h4>
                    <ul class="list-disc list-inside text-right">
                        <li>النوع (ضاغطة لحظية)</li>
                        <li>التحمل الكهربائي</li>
                        <li>العمر التشغيلي الميكانيكي</li>
                    </ul>
                </div>
            </div>
            <p class="text-lg text-gray-600 mt-8 animated-text" style="animation-delay: 1s;">اعتبارات الأجهزة الطبية: الموثوقية، التوفر، استهلاك الطاقة، المناعة ضد الضوضاء.</p>
        </div>

        <div id="slide-10" class="slide">
            <h2 class="text-4xl font-bold text-blue-700 mb-8 animated-text">الخلاصة والدروس المستفادة</h2>
            <p class="text-xl text-gray-700 mb-6 animated-text" style="animation-delay: 0.2s;">دائرة "أولوية نداء الممرضة" توضح كيفية استخدام البوابات المنطقية لتنفيذ وظائف قرار معقدة نسبياً.</p>

            <h3 class="text-2xl font-bold text-blue-600 mt-8 mb-4 animated-text" style="animation-delay: 0.4s;">خبرات عملية سابقة (انقر للمزيد)</h3>
            <div class="grid-container">
                <div class="lesson-card animated-text" style="animation-delay: 0.6s;" data-lesson="simultaneous_calls">
                    <h4>مشكلة النداءات المتزامنة غير المعالجة</h4>
                    <p>أهمية منطق الأولوية في الأنظمة التي تتعامل مع مدخلات متعددة وحرجة.</p>
                </div>
                <div class="lesson-card animated-text" style="animation-delay: 0.8s;" data-lesson="switch_bounce">
                    <h4>الارتداد في المفاتيح (Switch Bounce)</h4>
                    <p>الانتباه إلى الخصائص غير المثالية للمكونات الميكانيكية.</p>
                </div>
                <div class="lesson-card animated-text" style="animation-delay: 1s;" data-lesson="circuit_expansion">
                    <h4>توسيع نطاق الدائرة وتحدياته</h4>
                    <p>معرفة متى ننتقل من البوابات المنفصلة إلى الدوائر المتكاملة المتخصصة.</p>
                </div>
                <div class="lesson-card animated-text" style="animation-delay: 1.2s;" data-lesson="clear_indicators">
                    <h4>أهمية المؤشرات الواضحة</h4>
                    <p>واجهة المستخدم يجب أن تكون واضحة وبديهية قدر الإمكان.</p>
                </div>
            </div>

            <h3 class="text-2xl font-bold text-blue-600 mt-8 mb-4 animated-text" style="animation-delay: 1.4s;">نصيحة من خبير</h3>
            <p class="text-lg text-gray-700 animated-text" style="animation-delay: 1.6s;">
                عند التعامل مع البوابات المنطقية، ارسم دائماً جدول الحقيقة أولاً. ابدأ ببساطة ثم زد التعقيد تدريجياً. ولا تنسَ أن المدخلات غير المستخدمة في بوابات CMOS يجب ألا تُترك عائمة.
            </p>
        </div>

        <button id="prevBtn" class="nav-button left">❮</button>
        <button id="nextBtn" class="nav-button right">❯</button>
    </div>

    <div id="lessonModal" class="modal">
        <div class="modal-content">
            <button class="modal-close-button" onclick="closeModal()">✖</button>
            <h3 id="modalTitle"></h3>
            <p id="modalContent"></p>
        </div>
    </div>

    <script>
        const slides = document.querySelectorAll('.slide');
        let currentSlide = 0;
        const totalSlides = slides.length;

        function showSlide(index, direction) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'from-right', 'from-left');
                if (i === index) {
                    slide.classList.add('active');
                    if (direction === 'next') {
                        slide.classList.add('from-right');
                    } else if (direction === 'prev') {
                        slide.classList.add('from-left');
                    }
                    // Reset animation for animated-text elements on the active slide
                    slide.querySelectorAll('.animated-text').forEach(el => {
                        el.style.animation = 'none';
                        el.offsetHeight; /* trigger reflow */
                        el.style.animation = null;
                    });
                }
            });
            document.getElementById('prevBtn').style.display = (index === 0) ? 'none' : 'block';
            document.getElementById('nextBtn').style.display = (index === totalSlides - 1) ? 'none' : 'block';
        }

        function nextSlide() {
            if (currentSlide < totalSlides - 1) {
                currentSlide++;
                showSlide(currentSlide, 'next');
            }
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide, 'prev');
            }
        }

        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('prevBtn').addEventListener('click', prevSlide);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                prevSlide();
            }
        });

        // Initialize first slide
        showSlide(currentSlide, 'none');

        // Interactive Truth Table on Slide 4
        const truthTableRows = document.querySelectorAll('#slide-4 .interactive-table tbody tr');
        truthTableRows.forEach(row => {
            row.addEventListener('click', () => {
                truthTableRows.forEach(r => r.classList.remove('highlight-row'));
                row.classList.add('highlight-row');

                // Remove previous highlights
                row.querySelectorAll('td').forEach(td => td.classList.remove('highlight'));

                // Apply highlights based on logic
                const A = parseInt(row.dataset.a);
                const B = parseInt(row.dataset.b);
                const C = parseInt(row.dataset.c);

                if (A === 1) {
                    row.children[3].classList.add('highlight'); // Output A
                } else if (B === 1 && A === 0) {
                    row.children[4].classList.add('highlight'); // Output B
                } else if (C === 1 && A === 0 && B === 0) {
                    row.children[5].classList.add('highlight'); // Output C
                }
            });
        });

        // Circuit Diagram Interaction on Slide 8
        const pathA = document.getElementById('path-a');
        const pathB = document.getElementById('path-b');
        const pathC = document.getElementById('path-c');

        function simulateCall(input) {
            // Hide all paths first
            pathA.classList.add('hidden');
            pathB.classList.add('hidden');
            pathC.classList.add('hidden');

            if (input === 'A') {
                pathA.classList.remove('hidden');
            } else if (input === 'B') {
                // Check priority: A must be low
                // In a real circuit, pressing B when A is high would still result in A's output.
                // For simplicity in this visual, we just show the path if B is clicked.
                pathB.classList.remove('hidden');
            } else if (input === 'C') {
                // Check priority: A and B must be low
                pathC.classList.remove('hidden');
            }
        }

        // Modal for Lesson Details on Slide 10
        const lessonModal = document.getElementById('lessonModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalContent = document.getElementById('modalContent');
        const lessonCards = document.querySelectorAll('.lesson-card');

        const lessonDetails = {
            simultaneous_calls: {
                title: "مشكلة النداءات المتزامنة غير المعالجة",
                content: "في نظام نداء ممرضات قديم، لم تكن هناك آلية أولوية واضحة. إذا ضغط مريضان على زر النداء في نفس الوقت تقريباً، كان النظام أحياناً يعرض نداء واحداً فقط أو يتصرف بشكل غير متوقع. إعادة تصميم جزء من لوحة التحكم بإضافة منطق أولوية مشابه لما شرحناه (باستخدام ICs مخصصة نظراً لعدد المدخلات الكبير) حلت المشكلة وضمنت عدم إغفال أي نداء. الدرس المستفاد: منطق الأولوية ضروري في الأنظمة التي تتعامل مع مدخلات متعددة ومتزامنة قد تكون حرجة."
            },
            switch_bounce: {
                title: "الارتداد في المفاتيح (Switch Bounce)",
                content: "عند استخدام مفاتيح ميكانيكية كمدخلات لدوائر منطقية سريعة، يمكن للاهتزازات الميكانيكية الصغيرة عند ضغط المفتاح أو تحريره (الارتداد) أن تولد عدة نبضات HIGH/LOW سريعة بدلاً من نبضة واحدة نظيفة. هذا يمكن أن يربك الدوائر المنطقية، خاصة العدادات أو دوائر الذاكرة (Flip-flops). في دوائر الأولوية، قد لا يكون هذا حرجاً جداً إذا كانت الحالة مستقرة بعد الارتداد، ولكن في تطبيقات أخرى، يجب استخدام دوائر إزالة الارتداد (Debouncing circuits). الدرس المستفاد: يجب الانتباه إلى الخصائص غير المثالية للمكونات الميكانيكية عند ربطها بالدوائر الإلكترونية الحساسة."
            },
            circuit_expansion: {
                title: "توسيع نطاق الدائرة وتحدياته",
                content: "عندما حاولنا توسيع دائرة أولوية بسيطة مبنية على بوابات منفصلة لعدد كبير من المدخلات، أصبحت الدائرة معقدة جداً، واستهلكت مساحة كبيرة على اللوحة المطبوعة، وزادت احتمالية أخطاء التوصيل. التحول إلى استخدام مشفر أولوية مدمج (Priority Encoder IC) كان الحل الأمثل، حيث قلل بشكل كبير من عدد المكونات والتعقيد. الدرس المستفاد: لكل مستوى من التعقيد، هناك أدوات وحلول مناسبة. معرفة متى ننتقل من البوابات المنفصلة إلى الدوائر المتكاملة الأكثر تخصصاً أمر مهم."
            },
            clear_indicators: {
                title: "أهمية المؤشرات الواضحة",
                content: "في تصميم سابق، كانت جميع مؤشرات النداء بنفس اللون. كان من الصعب على الممرضات التمييز بسرعة بين نداء عادي ونداء طارئ. تغيير ألوان الـ LEDs (أحمر للطوارئ، أصفر للمساعدة، أخضر للطلبات الروتينية) مع إضافة نغمات صوتية مختلفة لكل مستوى أولوية، حسن بشكل كبير من كفاءة الاستجابة. الدرس المستفاد: واجهة المستخدم (User Interface)، حتى لو كانت مجرد LEDs، يجب أن تكون واضحة وبديهية قدر الإمكان، خاصة في البيئات المجهدة مثل المستشفيات."
            }
        };

        lessonCards.forEach(card => {
            card.addEventListener('click', () => {
                const lessonKey = card.dataset.lesson;
                const details = lessonDetails[lessonKey];
                if (details) {
                    modalTitle.textContent = details.title;
                    modalContent.textContent = details.content;
                    lessonModal.classList.add('show');
                }
            });
        });

        function closeModal() {
            lessonModal.classList.remove('show');
        }

        // Close modal on outside click
        lessonModal.addEventListener('click', (e) => {
            if (e.target === lessonModal) {
                closeModal();
            }
        });
    </script>
</body>
</html>
