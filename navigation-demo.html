<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Demo - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .demo-section {
            padding: 100px 20px 50px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .demo-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .demo-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 1rem;
        }
        
        .demo-btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-working { background: #4ade80; }
        .status-pending { background: #fbbf24; }
        .status-missing { background: #f87171; }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list i {
            color: #667eea;
            width: 20px;
        }
    </style>
</head>
<body>
    <!-- Enhanced Navigation Header -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-microchip"></i>
                    <span>Virtual Electronics Lab</span>
                </div>
                
                <div class="nav-menu">
                    <div class="nav-item">
                        <a href="#home" class="nav-link">
                            <i class="fas fa-home"></i>
                            Home
                        </a>
                    </div>
                    
                    <div class="nav-item mega-menu-item">
                        <a href="#courses" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            Courses
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-play-circle"></i> Fundamentals</h3>
                                    <ul>
                                        <li><a href="html/courses/fundamentals.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('basic-concepts')">Basic Concepts</a></li>
                                        <li><a href="#" onclick="openModule('ohms-law')">Ohm's Law</a></li>
                                        <li><a href="#" onclick="openModule('passive-components')">Passive Components</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-microchip"></i> Semiconductors</h3>
                                    <ul>
                                        <li><a href="#" onclick="openModule('diodes')">Diodes</a></li>
                                        <li><a href="#" onclick="openModule('bjt')">BJT Transistors</a></li>
                                        <li><a href="#" onclick="openModule('fet')">FET Transistors</a></li>
                                        <li><a href="#" onclick="openModule('opamps')">Op-Amps</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section featured">
                                    <h3><i class="fas fa-star"></i> Featured</h3>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-presentation"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Interactive Presentations</h4>
                                            <p>Animated learning slides</p>
                                            <a href="#" onclick="openPresentation()" class="featured-btn">Start</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="nav-item mega-menu-item">
                        <a href="#tools" class="nav-link">
                            <i class="fas fa-tools"></i>
                            Tools
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-calculator"></i> Calculators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('ohms-calculator')">Ohm's Law Calculator</a></li>
                                        <li><a href="#" onclick="openTool('power-calculator')">Power Calculator</a></li>
                                        <li><a href="#" onclick="openTool('resistor-calculator')">Resistor Color Code</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-flask"></i> Simulators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('circuit-simulator')">Circuit Simulator</a></li>
                                        <li><a href="#" onclick="openTool('logic-simulator')">Logic Simulator</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="nav-actions">
                    <button class="search-btn" onclick="toggleSearch()" data-tooltip="Search">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" onclick="toggleTheme()" data-tooltip="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="hamburger" onclick="toggleMobileMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Search Overlay -->
        <div class="search-overlay" id="searchOverlay">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search courses, tools, or topics..." id="searchInput">
                <button class="search-close" onclick="toggleSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="searchResults"></div>
        </div>
    </header>

    <!-- Demo Content -->
    <section class="demo-section">
        <h1 class="demo-title">🚀 Dynamic Navigation System Demo</h1>
        <p style="text-align: center; font-size: 1.2rem; color: #666; margin-bottom: 3rem;">
            Test all the navigation features and see how they work together seamlessly.
        </p>
        
        <div class="demo-grid">
            <!-- Navigation Features -->
            <div class="demo-card">
                <h3><i class="fas fa-bars"></i> Navigation Features</h3>
                <ul class="feature-list">
                    <li><span class="status-indicator status-working"></span><i class="fas fa-check"></i> Mega Menu System</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-check"></i> Mobile Responsive</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-check"></i> Search Functionality</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-check"></i> Theme Switching</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-check"></i> Smooth Animations</li>
                </ul>
            </div>
            
            <!-- Module Navigation -->
            <div class="demo-card">
                <h3><i class="fas fa-graduation-cap"></i> Test Module Navigation</h3>
                <p>Click these buttons to test module navigation:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="openModule('basic-concepts')">Basic Concepts</button>
                    <button class="demo-btn" onclick="openModule('ohms-law')">Ohm's Law</button>
                    <button class="demo-btn" onclick="openModule('bjt')">BJT Transistors</button>
                    <button class="demo-btn" onclick="openModule('solved-examples')">Solved Examples</button>
                </div>
            </div>
            
            <!-- Tool Navigation -->
            <div class="demo-card">
                <h3><i class="fas fa-tools"></i> Test Tool Navigation</h3>
                <p>Click these buttons to test tool navigation:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="openTool('ohms-calculator')">Ohm's Calculator</button>
                    <button class="demo-btn" onclick="openTool('circuit-simulator')">Circuit Simulator</button>
                    <button class="demo-btn" onclick="openTool('resistor-calculator')">Resistor Calculator</button>
                </div>
            </div>
            
            <!-- Interactive Features -->
            <div class="demo-card">
                <h3><i class="fas fa-mouse-pointer"></i> Interactive Features</h3>
                <p>Test these interactive navigation features:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="toggleSearch()">Open Search</button>
                    <button class="demo-btn" onclick="toggleTheme()">Toggle Theme</button>
                    <button class="demo-btn" onclick="toggleMobileMenu()">Mobile Menu</button>
                    <button class="demo-btn" onclick="navigateToPage('simulator.html')">Open Simulator</button>
                </div>
            </div>
            
            <!-- Page Navigation -->
            <div class="demo-card">
                <h3><i class="fas fa-link"></i> Page Navigation</h3>
                <p>Navigate to different sections:</p>
                <div class="demo-buttons">
                    <button class="demo-btn" onclick="navigateToPage('html/courses/fundamentals.html')">Fundamentals Course</button>
                    <button class="demo-btn" onclick="navigateToPage('html/level1/basic-concepts-new.html')">Basic Concepts</button>
                    <button class="demo-btn" onclick="navigateToPage('test-navigation.html')">Navigation Test</button>
                </div>
            </div>
            
            <!-- Status Overview -->
            <div class="demo-card">
                <h3><i class="fas fa-chart-line"></i> System Status</h3>
                <ul class="feature-list">
                    <li><span class="status-indicator status-working"></span><i class="fas fa-code"></i> JavaScript Navigation Manager</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-paint-brush"></i> Enhanced CSS Styling</li>
                    <li><span class="status-indicator status-working"></span><i class="fas fa-mobile-alt"></i> Mobile Optimization</li>
                    <li><span class="status-indicator status-pending"></span><i class="fas fa-file"></i> Some Module Pages</li>
                    <li><span class="status-indicator status-pending"></span><i class="fas fa-wrench"></i> Tool Pages</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: #f8f9fa; border-radius: 15px;">
            <h3 style="color: #667eea; margin-bottom: 1rem;">🎯 Navigation System Ready!</h3>
            <p style="color: #666; margin-bottom: 1.5rem;">
                The dynamic navigation system is fully implemented and ready to use across all pages. 
                Test the features above and explore the mega menus to see the system in action.
            </p>
            <button class="demo-btn" onclick="openPresentation()" style="background: #4ade80; font-size: 1.1rem; padding: 12px 24px;">
                🚀 Start Learning Journey
            </button>
        </div>
    </section>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    
    <script>
        // Additional demo functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 Navigation Demo Loaded Successfully!');
            console.log('📋 Available Functions:');
            console.log('  - openModule(moduleId)');
            console.log('  - openTool(toolId)');
            console.log('  - toggleSearch()');
            console.log('  - toggleTheme()');
            console.log('  - navigateToPage(url)');
        });
        
        // Override openPresentation for demo
        function openPresentation() {
            alert('🎓 Interactive Presentation System\n\nThis would open the comprehensive slide presentation with:\n• Animated circuit diagrams\n• Interactive calculators\n• Step-by-step tutorials\n• Medical device examples');
        }
    </script>
</body>
</html>
