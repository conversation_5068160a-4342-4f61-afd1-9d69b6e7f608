/* Solved Examples Module Styles */

/* Import modern color variables */
:root {
    /* Primary Gradients */
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
    --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 50%, #6366f1 100%);
    --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #ef4444 50%, #ec4899 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --dark-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    
    /* Solid Colors */
    --primary-color: #6366f1;
    --secondary-color: #06b6d4;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

/* Navigation */
.module-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.2rem 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: var(--radius-lg);
}

.back-btn:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
    transform: translateX(-2px);
}

.module-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.progress-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

/* Hero Section */
.examples-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 140px 40px 80px;
    background: var(--primary-gradient);
    color: white;
    position: relative;
    overflow: hidden;
}

.examples-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.hero-content {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text h1 {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.85);
}

.hero-objectives {
    background: rgba(255, 255, 255, 0.08);
    padding: 32px;
    border-radius: var(--radius-2xl);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: var(--shadow-xl);
    transition: all 0.3s ease;
}

.hero-objectives:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-4px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hero-objectives h3 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #f0f8ff;
    font-weight: 700;
}

.hero-objectives ul {
    list-style: none;
    padding: 0;
}

.hero-objectives li {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 1rem;
    font-weight: 500;
}

.hero-objectives li i {
    color: #4ade80;
    margin-right: 12px;
    font-size: 1.1rem;
}

/* Examples Showcase */
.examples-showcase {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.example-categories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.category-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 24px;
    border-radius: var(--radius-xl);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
}

.category-item .category-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 1.5rem;
}

.category-item.basic .category-icon {
    background: var(--success-gradient);
}

.category-item.intermediate .category-icon {
    background: var(--accent-gradient);
}

.category-item.advanced .category-icon {
    background: var(--secondary-gradient);
}

.category-item h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.category-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Featured Example */
.featured-example {
    background: rgba(255, 255, 255, 0.1);
    padding: 32px;
    border-radius: var(--radius-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.featured-example h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.example-preview {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.circuit-preview {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: var(--radius-lg);
}

.circuit-preview > div {
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Examples Categories Section */
.examples-categories {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.examples-categories::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.examples-categories h2 {
    text-align: center;
    font-size: 3rem;
    color: var(--text-primary);
    margin-bottom: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-weight: 800;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
}

.examples-categories h2 i {
    color: var(--primary-color);
    font-size: 2.5rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

/* Category Cards */
.category-card {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: 40px;
    box-shadow: var(--shadow-xl);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 90% 10%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 10% 90%, rgba(139, 92, 246, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
    position: relative;
    z-index: 1;
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.basic-icon {
    background: var(--success-gradient);
}

.medical-icon {
    background: var(--accent-gradient);
}

.signal-icon {
    background: var(--secondary-gradient);
}

.card-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    flex: 1;
}

.example-count {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 6px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    font-weight: 600;
}

.card-content {
    position: relative;
    z-index: 1;
}

.card-content p {
    color: var(--text-secondary);
    margin-bottom: 24px;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Example List */
.example-list {
    margin-bottom: 32px;
}

.example-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.example-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.example-number {
    width: 32px;
    height: 32px;
    background: var(--primary-gradient);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
}

.example-title {
    flex: 1;
    font-weight: 600;
    color: var(--text-primary);
}

.difficulty {
    padding: 4px 12px;
    border-radius: var(--radius-lg);
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.difficulty.easy {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.difficulty.medium {
    background: linear-gradient(135deg, #fed7aa, #fdba74);
    color: #9a3412;
}

.difficulty.hard {
    background: linear-gradient(135deg, #fce7f3, #fbcfe8);
    color: #be185d;
}

/* Explore Button */
.explore-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: var(--radius-xl);
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.explore-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Featured Example Detail */
.featured-example-detail {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

.featured-example-detail h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-weight: 800;
}

.featured-example-detail h2 i {
    color: var(--accent-color);
}

.example-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.problem-statement, .solution-approach {
    background: var(--bg-primary);
    padding: 32px;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: 32px;
}

.problem-statement h3, .solution-approach h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.requirements {
    margin-top: 24px;
}

.requirements h4 {
    color: var(--text-primary);
    margin-bottom: 12px;
    font-weight: 600;
}

.requirements ul {
    list-style: none;
    padding: 0;
}

.requirements li {
    padding: 8px 0;
    border-bottom: 1px solid var(--bg-tertiary);
    color: var(--text-secondary);
}

.requirements li:last-child {
    border-bottom: none;
}

.solution-approach ol {
    padding-left: 20px;
}

.solution-approach li {
    margin-bottom: 12px;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Circuit Diagram */
.circuit-diagram {
    background: var(--bg-primary);
    padding: 32px;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: 32px;
}

.circuit-diagram h4 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 24px;
    font-weight: 700;
}

.amplifier-stages {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

.stage1, .stage2, .filters {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: var(--radius-lg);
    text-align: center;
}

.stage1 h5, .stage2 h5, .filters h5 {
    color: var(--primary-color);
    margin-bottom: 12px;
    font-weight: 600;
}

.stage-details p, .filter-details p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

/* Performance Specs */
.performance-specs {
    background: var(--bg-primary);
    padding: 32px;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

.performance-specs h4 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: 24px;
    font-weight: 700;
}

.spec-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
}

.spec-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.spec-value {
    color: var(--primary-color);
    font-weight: 700;
}

/* Problem Solver */
.problem-solver {
    padding: 100px 0;
    background: var(--primary-gradient);
    color: white;
}

.problem-solver h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-weight: 800;
}

.solver-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 60px;
}

.problem-selector {
    background: rgba(255, 255, 255, 0.1);
    padding: 32px;
    border-radius: var(--radius-2xl);
    height: fit-content;
}

.problem-selector h3 {
    margin-bottom: 24px;
    font-weight: 700;
}

.problem-types {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.problem-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 16px;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
}

.problem-btn:hover, .problem-btn.active {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(4px);
}

.solver-interface {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.problem-generator, .solution-workspace {
    background: rgba(255, 255, 255, 0.1);
    padding: 32px;
    border-radius: var(--radius-2xl);
}

.problem-generator h3, .solution-workspace h3 {
    margin-bottom: 24px;
    font-weight: 700;
}

.problem-display {
    background: rgba(0, 0, 0, 0.2);
    padding: 24px;
    border-radius: var(--radius-lg);
    margin-bottom: 24px;
}

.problem-circuit {
    margin-top: 16px;
}

.circuit-elements {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 16px;
    border-radius: var(--radius-lg);
}

.circuit-element {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.9rem;
}

.generate-btn {
    background: var(--accent-gradient);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Solution Workspace */
.solution-steps {
    margin-bottom: 24px;
}

.step-input {
    margin-bottom: 20px;
}

.step-input label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.step-input textarea, .step-input input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px;
    border-radius: var(--radius-lg);
    resize: vertical;
    min-height: 80px;
}

.step-input input {
    min-height: auto;
    height: 44px;
}

.step-input textarea::placeholder, .step-input input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.solution-actions {
    display: flex;
    gap: 12px;
}

.check-btn, .hint-btn, .solution-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.check-btn {
    background: var(--success-gradient);
    color: white;
}

.hint-btn {
    background: var(--secondary-gradient);
    color: white;
}

.solution-btn {
    background: var(--accent-gradient);
    color: white;
}

.check-btn:hover, .hint-btn:hover, .solution-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Solution Feedback */
.solution-feedback {
    margin-top: 24px;
    padding: 16px;
    border-radius: var(--radius-lg);
    font-weight: 500;
}

.solution-feedback.success {
    background: rgba(16, 185, 129, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #6ee7b7;
}

.solution-feedback.error {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #fca5a5;
}

.solution-feedback.warning {
    background: rgba(245, 158, 11, 0.2);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #fcd34d;
}

.solution-feedback.info {
    background: rgba(6, 182, 212, 0.2);
    border: 1px solid rgba(6, 182, 212, 0.3);
    color: #67e8f9;
}

.solution-feedback.solution {
    background: rgba(139, 92, 246, 0.2);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: #c4b5fd;
}

.detailed-solution h4 {
    margin-bottom: 16px;
    color: #e0e7ff;
}

.detailed-solution ol {
    margin-bottom: 16px;
    padding-left: 20px;
}

.detailed-solution li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.final-answer {
    font-weight: 700;
    font-size: 1.1rem;
    color: #a5f3fc;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .solver-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .solver-interface {
        grid-template-columns: 1fr;
    }

    .example-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .example-categories {
        grid-template-columns: 1fr;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .container {
        padding: 0 20px;
    }

    .nav-container {
        padding: 1rem 20px;
        flex-direction: column;
        gap: 16px;
    }

    .amplifier-stages {
        grid-template-columns: 1fr;
    }

    .spec-grid {
        grid-template-columns: 1fr;
    }

    .solution-actions {
        flex-direction: column;
    }
}
