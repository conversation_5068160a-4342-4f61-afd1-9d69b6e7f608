<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electronics Fundamentals - Virtual Electronics Lab - دورة أساسيات الإلكترونيات</title>
    <link rel="stylesheet" href="../../css/course-page.css">
    <link rel="stylesheet" href="../../css/bilingual.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="course-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-home-btn">
                <i class="fas fa-home"></i>
                <span>Back to Home</span>
            </a>
            <div class="course-title" data-en="Electronics Fundamentals" data-ar="أساسيات الإلكترونيات">Electronics Fundamentals</div>
            <div class="nav-actions">
                <div class="language-toggle" id="languageToggle">
                    <button type="button" class="lang-btn active" data-lang="en">EN</button>
                    <button type="button" class="lang-btn" data-lang="ar">عر</button>
                </div>
                <button type="button" class="search-btn" onclick="toggleSearch()" title="Search">
                    <i class="fas fa-search"></i>
                </button>
                <button type="button" class="theme-toggle" onclick="toggleTheme()" title="Toggle Theme">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" id="courseProgress"></div>
                    </div>
                    <span class="progress-text">Progress: 0%</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="course-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Electronics Fundamentals</h1>
                <p>Master the foundation of electronic engineering with interactive lessons, simulations, and real-world applications in biomedical devices.</p>
                <div class="course-stats">
                    <div class="stat">
                        <i class="fas fa-play-circle"></i>
                        <span>5 Interactive Modules</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-clock"></i>
                        <span>3-4 Hours</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-certificate"></i>
                        <span>Certificate Available</span>
                    </div>
                </div>
                <div class="hero-buttons">
                    <button class="start-course-btn" onclick="startCourse()">
                        <i class="fas fa-play"></i>
                        Start Course
                    </button>
                    <button class="preview-btn" onclick="openPreview()">
                        <i class="fas fa-eye"></i>
                        Preview Course
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="course-preview">
                    <div class="preview-screen">
                        <div class="screen-header">
                            <div class="screen-controls">
                                <span class="control red"></span>
                                <span class="control yellow"></span>
                                <span class="control green"></span>
                            </div>
                            <span class="screen-title">Interactive Learning</span>
                        </div>
                        <div class="screen-content">
                            <div class="demo-circuit">
                                <div class="circuit-element battery">
                                    <span>V</span>
                                </div>
                                <div class="circuit-wire"></div>
                                <div class="circuit-element resistor">
                                    <span>R</span>
                                </div>
                                <div class="circuit-wire"></div>
                                <div class="current-flow">
                                    <div class="electron"></div>
                                    <div class="electron"></div>
                                    <div class="electron"></div>
                                </div>
                            </div>
                            <div class="demo-formula">
                                <span class="formula">V = I × R</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="course-content">
        <div class="container">
            <div class="content-grid">
                <!-- Course Modules -->
                <div class="modules-section">
                    <h2><i class="fas fa-list"></i> Course Modules</h2>
                    <div class="modules-list">
                        <!-- Module 1: Basic Concepts -->
                        <div class="module-card" data-module="1">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 1: Basic Concepts</h3>
                                    <p>Understanding Voltage, Current, Resistance, and Power</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 45 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Beginner</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator not-started" id="status-1">
                                        <i class="fas fa-play"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('basic-concepts')">
                                        <i class="fas fa-presentation"></i>
                                        <span>Interactive Presentation</span>
                                        <div class="topic-badge">New</div>
                                    </div>
                                    <div class="topic" onclick="openTopic('voltage-demo')">
                                        <i class="fas fa-bolt"></i>
                                        <span>Voltage Demonstration</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('current-flow')">
                                        <i class="fas fa-arrow-right"></i>
                                        <span>Current Flow Animation</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('power-calculator')">
                                        <i class="fas fa-calculator"></i>
                                        <span>Power Calculator</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn" onclick="startModule(1)">
                                        <i class="fas fa-play"></i>
                                        Start Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 2: Ohm's Law -->
                        <div class="module-card" data-module="2">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 2: Ohm's Law</h3>
                                    <p>Master the fundamental relationship V = I × R</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 50 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Beginner</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-2">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('ohms-law')">
                                        <i class="fas fa-presentation"></i>
                                        <span>Interactive Presentation</span>
                                        <div class="topic-badge">New</div>
                                    </div>
                                    <div class="topic" onclick="openTopic('magic-triangle')">
                                        <i class="fas fa-play"></i>
                                        <span>Magic Triangle Tool</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('ohms-calculator')">
                                        <i class="fas fa-calculator"></i>
                                        <span>Ohm's Law Calculator</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('practical-examples')">
                                        <i class="fas fa-lightbulb"></i>
                                        <span>Practical Examples</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(2)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 3: Passive Components -->
                        <div class="module-card" data-module="3">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 3: Passive Components</h3>
                                    <p>Explore Resistors, Capacitors, and Inductors</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 60 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-3">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('passive-components')">
                                        <i class="fas fa-presentation"></i>
                                        <span>Interactive Presentation</span>
                                        <div class="topic-badge">New</div>
                                    </div>
                                    <div class="topic" onclick="openTopic('resistor-color-code')">
                                        <i class="fas fa-palette"></i>
                                        <span>Resistor Color Code</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('capacitor-demo')">
                                        <i class="fas fa-battery-half"></i>
                                        <span>Capacitor Charging Demo</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('inductor-behavior')">
                                        <i class="fas fa-magnet"></i>
                                        <span>Inductor Behavior</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(3)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 4: Circuit Analysis -->
                        <div class="module-card" data-module="4">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 4: Circuit Analysis</h3>
                                    <p>Learn KVL, KCL, and circuit solving techniques</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 70 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-4">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('kvl-kcl')">
                                        <i class="fas fa-route"></i>
                                        <span>Kirchhoff's Laws</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('series-parallel')">
                                        <i class="fas fa-sitemap"></i>
                                        <span>Series & Parallel Circuits</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('circuit-simulator')">
                                        <i class="fas fa-microchip"></i>
                                        <span>Circuit Simulator</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('analysis-methods')">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Analysis Methods</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(4)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 5: Solved Examples -->
                        <div class="module-card" data-module="5">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-book-open"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 5: Biomedical Applications</h3>
                                    <p>Real-world examples in medical device engineering</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 55 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-5">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('pacemaker-circuit')">
                                        <i class="fas fa-heartbeat"></i>
                                        <span>Pacemaker Circuit Design</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('ecg-amplifier')">
                                        <i class="fas fa-chart-pulse"></i>
                                        <span>ECG Amplifier Analysis</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('sensor-circuits')">
                                        <i class="fas fa-thermometer-half"></i>
                                        <span>Medical Sensor Circuits</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('safety-considerations')">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Safety Considerations</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(5)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Sidebar -->
                <div class="course-sidebar">
                    <!-- Course Progress -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-chart-pie"></i> Your Progress</h3>
                        <div class="progress-circle">
                            <div class="circle">
                                <div class="progress-value">0%</div>
                            </div>
                        </div>
                        <div class="progress-stats">
                            <div class="stat">
                                <span class="label">Completed:</span>
                                <span class="value">0/5 modules</span>
                            </div>
                            <div class="stat">
                                <span class="label">Time Spent:</span>
                                <span class="value">0 hours</span>
                            </div>
                            <div class="stat">
                                <span class="label">Next Module:</span>
                                <span class="value">Basic Concepts</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Tools -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-tools"></i> Quick Tools</h3>
                        <div class="tools-list">
                            <button class="tool-btn" onclick="openTool('ohms-calculator')">
                                <i class="fas fa-calculator"></i>
                                <span>Ohm's Law Calculator</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('resistor-calculator')">
                                <i class="fas fa-palette"></i>
                                <span>Resistor Color Code</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('power-calculator')">
                                <i class="fas fa-fire"></i>
                                <span>Power Calculator</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('circuit-simulator')">
                                <i class="fas fa-microchip"></i>
                                <span>Circuit Simulator</span>
                            </button>
                        </div>
                    </div>

                    <!-- Related Courses -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-graduation-cap"></i> Related Courses</h3>
                        <div class="related-courses">
                            <div class="related-course" onclick="openCourse('digital-electronics')">
                                <div class="course-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Digital Electronics</h4>
                                    <p>Logic gates and digital systems</p>
                                </div>
                            </div>
                            <div class="related-course" onclick="openCourse('analog-electronics')">
                                <div class="course-icon">
                                    <i class="fas fa-wave-square"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Analog Electronics</h4>
                                    <p>Op-amps and analog circuits</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-container">
            <input type="text" class="search-input" placeholder="Search course content..." id="searchInput">
            <button class="search-close" onclick="toggleSearch()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="search-results" id="searchResults">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <script src="../../js/bilingual.js"></script>
    <script src="../../js/course-page.js"></script>
</body>
</html>
