
import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import Footer from './components/Footer';
import ModuleCard from './components/ModuleCard';
import ModuleView from './components/ModuleView';
import { MODULES_DATA } from './constants';
import { Module, ViewState } from './types';

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewState>('dashboard');
  const [selectedModuleId, setSelectedModuleId] = useState<string | null>(null);

  const handleSelectModule = (moduleId: string) => {
    setSelectedModuleId(moduleId);
    setCurrentView('module');
    window.scrollTo(0, 0); // Scroll to top when a module is selected
  };

  const handleBackToDashboard = () => {
    setSelectedModuleId(null);
    setCurrentView('dashboard');
    window.scrollTo(0, 0);
  };
  
  // Handle hash changes for direct navigation to modules (basic)
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace(/^#/, '');
      const moduleExists = MODULES_DATA.some(m => m.id === hash);
      if (hash && moduleExists) {
        handleSelectModule(hash);
      } else if (!hash && currentView === 'module') {
        // If hash is removed and we are in module view, go to dashboard
        // Or if hash is invalid
        handleBackToDashboard();
      } else if (hash && !moduleExists && currentView !== 'dashboard') {
        // If hash is invalid and not on dashboard, go to dashboard
        window.location.hash = ''; // Clear invalid hash
        handleBackToDashboard();
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Initial check

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []); // Run once on mount

  useEffect(() => {
    if (currentView === 'module' && selectedModuleId) {
      if(window.location.hash !== `#${selectedModuleId}`) {
        window.location.hash = selectedModuleId;
      }
    } else if (currentView === 'dashboard') {
      if(window.location.hash !== "") {
         window.location.hash = "";
      }
    }
  }, [currentView, selectedModuleId]);


  const selectedModule = selectedModuleId ? MODULES_DATA.find(m => m.id === selectedModuleId) : null;

  return (
    <div className="flex flex-col min-h-screen bg-slate-100">
      <Header />
      <main className="flex-grow">
        {currentView === 'dashboard' && (
          <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-sky-800 mb-4">Welcome to Your Learning Journey</h2>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto">
                Explore these modules to build a solid foundation in basic electronics concepts and circuit analysis, tailored for biomedical engineering students.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {MODULES_DATA.map(module => (
                <ModuleCard key={module.id} module={module} onSelectModule={handleSelectModule} />
              ))}
            </div>
          </div>
        )}
        {currentView === 'module' && selectedModule && (
          <ModuleView module={selectedModule} onBack={handleBackToDashboard} />
        )}
        {currentView === 'module' && !selectedModule && selectedModuleId && (
           // This case means selectedModuleId is set, but module not found
          <div className="container mx-auto px-4 py-8 text-center">
            <p className="text-xl text-red-500">Module "{selectedModuleId}" not found. Please return to the dashboard.</p>
            <button 
              onClick={handleBackToDashboard}
              className="mt-4 bg-sky-600 text-white py-2 px-4 rounded-lg hover:bg-sky-700"
            >
              Back to Dashboard
            </button>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
};

export default App;
