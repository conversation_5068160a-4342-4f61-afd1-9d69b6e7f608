<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التحكم الأساسي في درجة الحرارة في الأجهزة الطبية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden; /* Hide scrollbars */
        }
        .slide-container {
            background-color: #ffffff;
            border-radius: 1rem; /* rounded-xl */
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1); /* shadow-xl */
            width: 90vw;
            max-width: 1200px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Ensure content stays within bounds */
        }
        .slide {
            flex-grow: 1;
            padding: 2rem;
            display: none; /* Hidden by default */
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
            position: relative;
            overflow-y: auto; /* Allow scrolling for long content */
        }
        .slide.active {
            display: flex;
            opacity: 1;
            transform: translateY(0);
        }
        .slide-content {
            max-width: 90%;
            margin: auto;
        }
        .slide h1, .slide h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        .slide p, .slide ul {
            color: #34495e;
            font-size: 1.1rem;
            line-height: 1.6;
            text-align: justify;
        }
        .slide ul {
            list-style-type: disc;
            padding-right: 1.5rem;
            text-align: right;
            margin-bottom: 1rem;
        }
        .slide ul li {
            margin-bottom: 0.5rem;
        }
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            padding: 1rem 2rem;
            border-top: 1px solid #e2e8f0;
            background-color: #f8fafc;
        }
        .nav-button {
            background-color: #3498db;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .nav-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .nav-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }
        .icon-large {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 1rem;
        }
        .diagram-container {
            background-color: #ecf0f1;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1.5rem;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
            width: 100%;
            max-width: 800px;
            overflow-x: auto; /* Allow horizontal scrolling for wide diagrams */
            text-align: initial; /* Reset text alignment for diagram content */
        }
        .mermaid svg {
            max-width: 100%;
            height: auto;
        }

        /* Specific styles for circuit diagram explanation */
        .circuit-explanation-item {
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
            text-align: right;
        }
        .circuit-explanation-item:hover {
            background-color: #e0f2f7;
        }
        .circuit-explanation-item.highlighted {
            background-color: #d4edda; /* Light green for highlighted */
            font-weight: bold;
            border: 1px solid #28a745;
        }
        .circuit-diagram-wrapper {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }
        .circuit-diagram-wrapper .diagram-container {
            flex-shrink: 0; /* Prevent diagram from shrinking */
        }
        .circuit-explanation-list {
            flex-grow: 1;
            max-height: 400px; /* Limit height for scrollability */
            overflow-y: auto;
            padding-left: 1rem;
            padding-right: 1rem;
            width: 100%;
            max-width: 600px;
        }

        @media (max-width: 768px) {
            .slide-container {
                width: 95vw;
                height: 90vh;
            }
            .slide {
                padding: 1.5rem;
            }
            .slide h1 {
                font-size: 1.8rem;
            }
            .slide h2 {
                font-size: 1.4rem;
            }
            .slide p, .slide ul {
                font-size: 1rem;
            }
            .navigation-buttons {
                padding: 1rem;
            }
            .nav-button {
                padding: 0.6rem 1rem;
                font-size: 0.9rem;
            }
            .circuit-diagram-wrapper {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="slide active" id="slide-1">
            <div class="slide-content">
                <i class="fas fa-microchip icon-large animate-bounce"></i>
                <h1 class="text-4xl font-bold mb-4">أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</h1>
                <h2 class="text-2xl font-semibold text-gray-700 mb-2">المستوى الأول الحلقة 5: تحكم أساسي في درجة حرارة (Basic Temperature Control)</h2>
                <p class="text-xl text-gray-600">د. محمد يعقوب إسماعيل</p>
                <p class="text-lg text-gray-500">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
            </div>
        </div>

        <div class="slide" id="slide-2">
            <div class="slide-content">
                <i class="fas fa-thermometer-half icon-large animate-pulse"></i>
                <h2 class="text-3xl font-bold mb-4">مقدمة ومفاهيم أساسية</h2>
                <p class="mb-4">أهلاً بكم في الحلقة الخامسة من المستوى الأول. بعد أن تناولنا دوائر المؤشرات والإنذارات، ننتقل اليوم إلى تطبيق عملي آخر واسع الانتشار في الأجهزة الطبية: <strong>التحكم الأساسي في درجة الحرارة</strong>.</p>
                <p class="mb-4">العديد من العمليات الطبية والتشخيصية تتطلب الحفاظ على درجة حرارة معينة بدقة، سواء كان ذلك لعينات حيوية، سوائل، أو حتى لراحة المريض.</p>
                <p class="mb-4">سنركز على أبسط أشكال التحكم في درجة الحرارة، وهو التحكم من نوع "تشغيل/إيقاف" (ON/OFF Control) أو ما يعرف أحياناً بـ "التحكم بالثرموستات".</p>
                <h3 class="text-2xl font-semibold mb-2">المفاهيم الأساسية التي سنغطيها:</h3>
                <ul class="list-disc pr-8 text-right">
                    <li>مقدمة عن حساسات الحرارة الشائعة (الثرمستور NTC/PTC، الدايود/الترانزستور كحساس حرارة).</li>
                    <li>استخدام مجزئ الجهد مع حساس الحرارة.</li>
                    <li>استخدام المقارنات (Comparators) لاتخاذ قرار التشغيل/الإيقاف.</li>
                    <li>مفهوم التلاكؤ أو الهيستيريسيس (Hysteresis) لمنع التذبذب السريع.</li>
                    <li> قيادة عنصر تسخين (أو تبريد) بسيط باستخدام ريليه أو ترانزستور قدرة.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-3">
            <div class="slide-content">
                <i class="fas fa-heartbeat icon-large animate-bounce"></i>
                <h2 class="text-3xl font-bold mb-4">أهمية هذه الدائرة في سياق الأجهزة الطبية</h2>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li><strong>الحفاظ على العينات البيولوجية:</strong> حاضنات المختبر، أجهزة تحليل الدم تتطلب درجات حرارة مستقرة.</li>
                    <li><strong>تدفئة السوائل الوريدية:</strong> ضمان وصول السوائل للمريض بدرجة حرارة مناسبة.</li>
                    <li><strong>حاضنات الأطفال الخدج:</strong> توفير بيئة حرارية مثالية لنموهم.</li>
                    <li><strong>أجهزة العلاج الحراري/البرودة:</strong> تطبيق درجات حرارة محددة لأغراض علاجية.</li>
                    <li><strong>تبريد المكونات الإلكترونية الحساسة:</strong> داخل الأجهزة الطبية نفسها لمنع ارتفاع درجة حرارتها.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-4">
            <div class="slide-content">
                <h2 class="text-3xl font-bold mb-4">النظرية الرئيسية وآلية عمل النظام المقترح</h2>
                <p class="mb-4">يعتمد نظام التحكم الأساسي في درجة الحرارة على حلقة تغذية راجعة بسيطة:</p>
                <div class="diagram-container">
                    <div class="mermaid">
                        graph TD
                            Pwr[<i class='fas fa-power-off'></i> مصدر تغذية] --> SenseCircuit(دائرة استشعار وتحويل لجهد);
                            Pwr --> Setpoint[<i class='fas fa-sliders-h'></i> دائرة تحديد النقطة المرجعية];
                            Pwr --> Comparator{<i class='fas fa-compress-alt'></i> مقارن مع هيستيريسيس};
                            Pwr --> Driver(<i class='fas fa-cogs'></i> دائرة قيادة الحمل);
                            Pwr --> Load[<i class='fas fa-fire'></i> عنصر تسخين/تبريد];

                            Sensor[<i class='fas fa-thermometer-full'></i> حساس حرارة (e.g., NTC)] --> SenseCircuit;
                            SenseCircuit -- V_sense (يمثل الحرارة الحالية) --> Comparator;
                            Setpoint -- V_setpoint --> Comparator;
                            Comparator -- إشارة تحكم (ON/OFF) --> Driver;
                            Driver --> Load;
                            Load -- تأثير حراري --> MonitoredEnv(<i class='fas fa-cube'></i> البيئة المراقبة حرارياً);
                            MonitoredEnv -- درجة الحرارة الفعلية --> Sensor;
                    </div>
                </div>
                <p class="mt-4 text-gray-700">انقر على المكونات في المخطط لفهم دورها.</p>
            </div>
        </div>

        <div class="slide" id="slide-5">
            <div class="slide-content">
                <i class="fas fa-temperature-low icon-large animate-spin"></i>
                <h2 class="text-3xl font-bold mb-4">حساسات الحرارة (Temperature Sensors)</h2>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li><strong>الثرمستور (Thermistor):</strong> مقاومة تتغير قيمتها بشكل كبير مع تغير درجة الحرارة.
                        <ul class="list-circle pr-8 text-right text-lg">
                            <li><strong>NTC (Negative Temperature Coefficient):</strong> مقاومته تقل مع ارتفاع درجة الحرارة (الأكثر شيوعاً).</li>
                            <li><strong>PTC (Positive Temperature Coefficient):</strong> مقاومته تزداد مع ارتفاع درجة الحرارة.</li>
                        </ul>
                    </li>
                    <li><strong>الدايود أو الترانزستور كحساس حرارة:</strong> الجهد الأمامي للدايود (Vf) أو جهد القاعدة-باعث للترانزستور (Vbe) يتغير بشكل خطي تقريباً مع درجة الحرارة (عادة ينخفض بحوالي 2mV لكل درجة مئوية ارتفاع).</li>
                </ul>
                <img src="https://placehold.co/300x150/3498db/ffffff?text=Thermistor+Image" alt="Thermistor" class="rounded-lg shadow-md mt-4 mx-auto">
            </div>
        </div>

        <div class="slide" id="slide-6">
            <div class="slide-content">
                <i class="fas fa-exchange-alt icon-large animate-pulse"></i>
                <h2 class="text-3xl font-bold mb-4">تحويل تغير الحساس إلى إشارة جهد</h2>
                <p class="mb-4">عادةً ما يتم وضع الثرمستور (NTC كمثال) في دائرة <strong>مجزئ جهد</strong> مع مقاومة ثابتة.</p>
                <p class="mb-4">الجهد عند نقطة المنتصف في مجزئ الجهد سيتغير مع تغير مقاومة الثرمستور (وبالتالي مع تغير درجة الحرارة).</p>
                <div class="diagram-container text-left">
                    <p class="text-lg font-semibold mb-2">مثال لمجزئ الجهد مع NTC:</p>
                    <div class="mermaid">
                        graph TD
                            VCC[+Vcc] --> R_fixed(مقاومة ثابتة);
                            R_fixed --> V_sense(نقطة الإشارة);
                            V_sense --> NTC(NTC Thermistor);
                            NTC --> GND[GND];
                    </div>
                    <p class="mt-4">مع ارتفاع الحرارة، تقل مقاومة الثرمستور (R_NTC)، وبالتالي يتغير الجهد V_sense.</p>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-7">
            <div class="slide-content">
                <i class="fas fa-balance-scale icon-large animate-wobble"></i>
                <h2 class="text-3xl font-bold mb-4">المقارنة واتخاذ القرار (باستخدام مقارن Op-Amp)</h2>
                <p class="mb-4">يتم مقارنة <strong>V_sense</strong> (الذي يمثل درجة الحرارة الحالية) مع <strong>جهد مرجعي V_setpoint</strong> (الذي يمثل درجة الحرارة المطلوبة).</p>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li>إذا كان V_sense يشير إلى أن الحرارة منخفضة جداً، يقوم المقارن بتشغيل عنصر التسخين.</li>
                    <li>إذا كان V_sense يشير إلى أن الحرارة مرتفعة جداً، يقوم المقارن بإيقاف عنصر التسخين.</li>
                </ul>
                <img src="https://placehold.co/400x200/2ecc71/ffffff?text=Op-Amp+Comparator" alt="Op-Amp Comparator" class="rounded-lg shadow-md mt-4 mx-auto">
            </div>
        </div>

        <div class="slide" id="slide-8">
            <div class="slide-content">
                <i class="fas fa-arrows-alt-h icon-large animate-ping"></i>
                <h2 class="text-3xl font-bold mb-4">التلاكؤ أو الهيستيريسيس (Hysteresis)</h2>
                <h3 class="text-2xl font-semibold mb-2">المشكلة بدون هيستيريسيس:</h3>
                <p class="mb-4">إذا كانت نقطة التشغيل والإيقاف هي نفسها تماماً، فإن أي تغير طفيف في درجة الحرارة سيؤدي إلى تشغيل وإيقاف عنصر التسخين بشكل متكرر وسريع (Chattering)، مما يجهد المكونات ويقلل من كفاءة النظام.</p>
                <h3 class="text-2xl font-semibold mb-2">الحل:</h3>
                <p class="mb-4">يتم إدخال الهيستيريسيس عن طريق جعل نقطة التشغيل (Turn-ON point) مختلفة قليلاً عن نقطة الإيقاف (Turn-OFF point). هذا "النطاق الميت" (Deadband) يمنع التذبذب.</p>
                <p class="text-lg text-gray-700"><strong>تحقيق الهيستيريسيس:</strong> يتم ذلك عادةً عن طريق إضافة تغذية راجعة إيجابية (Positive Feedback) صغيرة من خرج المقارن إلى مدخله غير العاكس (أو العاكس) عبر مقاومة.</p>
                <img src="https://placehold.co/400x200/9b59b6/ffffff?text=Hysteresis+Graph" alt="Hysteresis+Graph" class="rounded-lg shadow-md mt-4 mx-auto">
            </div>
        </div>

        <div class="slide" id="slide-9">
            <div class="slide-content">
                <i class="fas fa-bolt icon-large animate-flash"></i>
                <h2 class="text-3xl font-bold mb-4">قيادة عنصر التسخين/التبريد</h2>
                <p class="mb-4">خرج المقارن عادة ما يكون إشارة منخفضة التيار، لذا نحتاج إلى دائرة قيادة.</p>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li><strong>الريليه (Relay):</strong> يمكن استخدام خرج المقارن (عبر ترانزستور) لتشغيل ريليه. تلامسات الريليه يمكنها توصيل وفصل التيار العالي اللازم لعنصر التسخين.</li>
                    <li><strong>ترانزستور قدرة (Power Transistor - BJT أو MOSFET):</strong> إذا كان عنصر التسخين يعمل على DC ومنخفض القدرة نسبياً، يمكن استخدام ترانزستور قدرة مباشرة كمفتاح.</li>
                </ul>
                <div class="flex justify-around items-center mt-6 w-full">
                    <img src="https://placehold.co/200x150/e67e22/ffffff?text=Relay+Image" alt="Relay" class="rounded-lg shadow-md">
                    <img src="https://placehold.co/200x150/f1c40f/ffffff?text=Transistor+Image" alt="Transistor" class="rounded-lg shadow-md">
                </div>
            </div>
        </div>

        <div class="slide" id="slide-10">
            <div class="slide-content flex flex-col lg:flex-row items-start justify-center gap-4 w-full h-full">
                <div class="circuit-diagram-wrapper flex flex-col items-center w-full lg:w-1/2">
                    <h2 class="text-3xl font-bold mb-4">المخطط الكهربائي: تحكم في درجة حرارة</h2>
                    <div class="diagram-container w-full h-auto">
                        <div class="mermaid" id="circuit-diagram-mermaid">
                            circuitDiagram
                            title تحكم أساسي في درجة حرارة (NTC, LM393, Relay)
                            subgraph "مصدر التغذية (e.g., +12V)"
                                battery(VCC, "+12V");
                                ground(GND);
                            end
                            subgraph "دائرة استشعار الحرارة وتحديد النقطة المرجعية"
                                resistor(R1, "10k Ω ");
                                thermistor(NTC1, "NTC 10k Ω  @ 25°C");
                                potentiometer(RV1, "10k Ω  Pot");
                            end
                            subgraph "مرحلة المقارنة مع هيستيريسيس (LM393)"
                                ic(U1, "LM393");
                                pin(U1_VCC, 8, VCC_IC, "+5V or +12V for LM393");
                                pin(U1_GND, 4, GND_IC);
                                pin(U1_IN_PLUS, 3, IN+);
                                pin(U1_IN_MINUS, 2, IN-);
                                pin(U1_OUT, 1, OUT);
                                resistor(R_hyst, "100k Ω ");
                                resistor(R_pullup_comp, "4.7k Ω ");
                            end
                            subgraph "دائرة قيادة الريليه"
                                transistor(Q1, "NPN (e.g., 2N2222A, BC337)");
                                resistor(R_base_Q1, "1k Ω ");
                                diode(D_flyback, "1N4007");
                                relay(RLY1, "Relay (12V Coil)");
                            end
                            subgraph "عنصر التسخين (يتم التحكم به بواسطة الريليه)"
                                # Placeholder for heater connections
                            end
                            VCC.p --- U1_VCC; GND.p --- U1_GND;
                            VCC.p --- R1.p1;
                            R1.p2 --- NTC1.p1;
                            R1.p2 --- U1_IN_PLUS; # V_sense (correct for heating logic)
                            NTC1.p2 --- GND.p;
                            VCC.p --- RV1.p1;
                            RV1.wiper --- U1_IN_MINUS; # V_setpoint (correct for heating logic)
                            RV1.p2 --- GND.p;
                            U1_OUT --- R_hyst.p1;
                            R_hyst.p2 --- U1_IN_PLUS;
                            VCC.p --- R_pullup_comp.p1;
                            R_pullup_comp.p2 --- U1_OUT;
                            U1_OUT --- R_base_Q1.p1;
                            R_base_Q1.p2 --- Q1.b;
                            Q1.e --- GND.p;
                            VCC.p --- RLY1.coil1;
                            RLY1.coil2 --- Q1.c;
                            D_flyback.A --- Q1.c;
                            D_flyback.K --- VCC.p;
                        </div>
                    </div>
                </div>
                <div class="circuit-explanation-list w-full lg:w-1/2 mt-4 lg:mt-0">
                    <h3 class="text-2xl font-semibold mb-2 text-right">شرح المخطط الكهربائي:</h3>
                    <div class="circuit-explanation-item" data-target="NTC1,R1,U1_IN_PLUS">
                        <p><strong>1. دائرة الاستشعار:</strong> R1 و NTC1 يشكلان مجزئ جهد. الجهد عند U1_IN_PLUS (V_sense) يزداد عندما تنخفض درجة الحرارة (لأن مقاومة NTC تزداد).</p>
                    </div>
                    <div class="circuit-explanation-item" data-target="RV1,U1_IN_MINUS">
                        <p><strong>2. دائرة النقطة المرجعية:</strong> المقاومة المتغيرة RV1 تسمح للمستخدم بضبط الجهد المرجعي V_setpoint عند U1_IN_MINUS، وبالتالي ضبط درجة الحرارة المطلوبة.</p>
                    </div>
                    <div class="circuit-explanation-item" data-target="U1,R_hyst">
                        <p><strong>3. المقارن مع هيستيريسيس:</strong> U1 (LM393) يقارن V_sense مع V_setpoint. R_hyst توفر تغذية راجعة إيجابية من خرج المقارن إلى مدخله غير العاكس (IN+)، مما يخلق نطاقي تشغيل وإيقاف مختلفين (هيستيريسيس).</p>
                    </div>
                    <div class="circuit-explanation-item" data-target="Q1,RLY1,D_flyback">
                        <p><strong>4. قيادة الريليه:</strong> Q1 يعمل كمفتاح لتشغيل ملف الريليه RLY1. D_flyback هو دايود حماية لحماية Q1 من الجهد العكسي العالي الذي يتولد عند إيقاف تشغيل ملف الريليه.</p>
                    </div>
                    <div class="circuit-explanation-item" data-target="RLY1">
                        <p><strong>5. عنصر التسخين:</strong> يتم توصيله عبر تلامسات الريليه (عادة التلامس المفتوح عادة - Normally Open).</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-11">
            <div class="slide-content">
                <i class="fas fa-cogs icon-large animate-spin"></i>
                <h2 class="text-3xl font-bold mb-4">مواصفات المكونات الرئيسية</h2>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li><strong>الثرمستور (NTC Thermistor):</strong> المقاومة عند 25°C، معامل بيتا (β)، نطاق درجة الحرارة التشغيلي، الدقة.</li>
                    <li><strong>المقارن (Comparator - e.g., LM393):</strong> استقرار جهد الإزاحة للمدخل.</li>
                    <li><strong>المقاومة المتغيرة (Potentiometer - RV1):</strong> القيمة الأومية، النوع (خطي)، القدرة المقننة، دقة الضبط.</li>
                    <li><strong>الريليه (Relay - RLY1):</strong> جهد الملف، مواصفات التلامسات (جهد وتيار الحمل)، نوع التلامسات (SPST-NO).</li>
                    <li><strong>الترانزستور (NPN BJT - Q1):</strong> يجب أن يتحمل تيار ملف الريليه.</li>
                    <li><strong>دايود الحماية (Flyback Diode - D_flyback):</strong> يتحمل تياراً عكسياً مساوياً لتيار الملف، وجهداً عكسياً أعلى من جهد المصدر.</li>
                    <li><strong>عنصر التسخين/التبريد:</strong> القدرة (بالواط)، جهد التشغيل، مقاومته.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-12">
            <div class="slide-content">
                <i class="fas fa-check-circle icon-large animate-fade-in"></i>
                <h2 class="text-3xl font-bold mb-4">الخلاصة والمهارات المكتسبة</h2>
                <p class="mb-4">دائرة التحكم الأساسي في درجة الحرارة هي تطبيق عملي يجمع بين استشعار متغير فيزيائي (الحرارة)، مقارنته بنقطة مرجعية، واتخاذ قرار للتحكم في حمل. إدخال الهيستيريسيس أمر بالغ الأهمية لضمان عمل النظام بشكل مستقر وتجنب التذبذب.</p>
                <h3 class="text-2xl font-semibold mb-2">المهارات المتوقع اكتسابها:</h3>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li>فهم مبدأ عمل الثرمستور NTC واستخدامه في مجزئ جهد.</li>
                    <li>تصميم دائرة مقارن مع هيستيريسيس.</li>
                    <li>ضبط نقطة مرجعية باستخدام مقاومة متغيرة.</li>
                    <li>قيادة ريليه باستخدام ترانزستور.</li>
                    <li>فهم أهمية دايود الحماية (Flyback diode).</li>
                    <li>القدرة على تحليل دائرة تحكم بسيطة في درجة الحرارة.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-13">
            <div class="slide-content">
                <i class="fas fa-exclamation-triangle icon-large animate-shake"></i>
                <h2 class="text-3xl font-bold mb-4">خبرات عملية سابقة ومشاكل مشابهة</h2>
                <ul class="list-disc pr-8 text-right text-xl">
                    <li><strong>مشكلة "التذبذب المزعج" (Chattering):</strong> حلها بزيادة قيمة مقاومة الهيستيريسيس.</li>
                    <li><strong>"الحرارة لا تصل أبداً للنقطة المطلوبة" أو "تتجاوزها بكثير":</strong>
                        <ul class="list-circle pr-8 text-right text-lg">
                            <li>عدم الوصول: قدرة عنصر التسخين غير كافية أو عزل حراري سيئ.</li>
                            <li>التجاوز الكبير (Overshoot): بسبب القصور الذاتي الحراري للنظام (Thermal Lag). يمكن تقليله بضبط نقاط الهيستيريسيس بعناية.</li>
                        </ul>
                    </li>
                    <li><strong>تلف ترانزستور قيادة الريليه:</strong> بسبب عدم وجود دايود حماية (Flyback diode) عبر ملف الريليه.</li>
                    <li><strong>وضع حساس الحرارة بشكل خاطئ:</strong> يؤدي إلى تحكم غير دقيق.</li>
                    <li><strong>المعايرة (Calibration):</strong> ضرورية للحصول على دقة جيدة.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-14">
            <div class="slide-content">
                <i class="fas fa-lightbulb icon-large animate-pulse"></i>
                <h2 class="text-3xl font-bold mb-4">نصيحة من خبير</h2>
                <p class="text-xl mb-4">عند تصميم دوائر التحكم في الحرارة، ابدأ دائماً بفهم جيد للمتطلبات الحرارية للنظام (النطاق المطلوب، الدقة، سرعة الاستجابة).</p>
                <p class="text-xl mb-4">اختر حساساً مناسباً وعنصر تسخين/تبريد ذا قدرة كافية.</p>
                <p class="text-xl">ولا تنسَ اعتبارات السلامة، خاصة عند التعامل مع عناصر تسخين قد تصل إلى درجات حرارة عالية أو تعمل على جهود خطيرة.</p>
                <img src="https://placehold.co/300x200/3498db/ffffff?text=Safety+First" alt="Safety+First" class="rounded-lg shadow-md mt-6 mx-auto">
            </div>
        </div>

        <div class="navigation-buttons">
            <button id="prevBtn" class="nav-button" disabled>
                <i class="fas fa-arrow-right"></i> السابق
            </button>
            <button id="nextBtn" class="nav-button">
                التالي <i class="fas fa-arrow-left"></i>
            </button>
        </div>
    </div>

    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.js';

        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // Initialize Mermaid
        mermaid.initialize({ startOnLoad: false });

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active');
                slide.style.display = 'none'; // Ensure it's truly hidden
                slide.style.opacity = '0';
                slide.style.transform = 'translateY(20px)';
            });

            if (slides[index]) {
                slides[index].style.display = 'flex';
                setTimeout(() => {
                    slides[index].classList.add('active');
                }, 50); // Small delay to allow display change before animation
            }

            prevBtn.disabled = (index === 0);
            nextBtn.disabled = (index === slides.length - 1);

            // Re-render Mermaid diagrams on slide change
            if (index === 3 || index === 5 || index === 9) { // Slides with diagrams
                renderMermaidDiagrams();
            }
        }

        function nextSlide() {
            if (currentSlide < slides.length - 1) {
                currentSlide++;
                showSlide(currentSlide);
            }
        }

        function prevSlide() {
            if (currentSlide > 0) {
                currentSlide--;
                showSlide(currentSlide);
            }
        }

        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);

        // Function to render all Mermaid diagrams
        async function renderMermaidDiagrams() {
            const diagramContainers = document.querySelectorAll('.mermaid');
            for (const container of diagramContainers) {
                const graphDefinition = container.textContent.trim();
                if (graphDefinition) {
                    try {
                        // Use the imported 'mermaid' object directly
                        const { svg, bindFunctions } = await mermaid.render('mermaid-diagram-' + Math.random().toString(36).substr(2, 9), graphDefinition);
                        container.innerHTML = svg;
                        if (bindFunctions) {
                            bindFunctions();
                        }
                    } catch (error) {
                        console.error('Error rendering Mermaid diagram:', error);
                        container.innerHTML = `<p style="color: red;">Error rendering diagram: ${error.message}</p>`;
                    }
                }
            }
        }

        // Initialize the first slide and render diagrams
        document.addEventListener('DOMContentLoaded', () => {
            showSlide(currentSlide);
            renderMermaidDiagrams(); // Render diagrams on initial load
        });

        // Circuit diagram highlighting
        const explanationItems = document.querySelectorAll('.circuit-explanation-item');
        explanationItems.forEach(item => {
            item.addEventListener('click', () => {
                // Remove highlight from all items
                explanationItems.forEach(exp => exp.classList.remove('highlighted'));
                // Add highlight to clicked item
                item.classList.add('highlighted');

                // Simulate highlighting in the diagram (this is a conceptual highlight)
                // In a real interactive circuit diagram, you'd manipulate SVG elements.
                // For Mermaid, we can't directly highlight nodes dynamically post-render.
                // This is a placeholder for visual feedback on the explanation.
                console.log('Highlighting:', item.dataset.target);
            });
        });

        // Simple animation for icons on slide load (re-trigger animation)
        slides.forEach(slide => {
            slide.addEventListener('transitionend', () => {
                if (slide.classList.contains('active')) {
                    const icons = slide.querySelectorAll('.icon-large');
                    icons.forEach(icon => {
                        // Remove and re-add animation classes to re-trigger
                        const classes = Array.from(icon.classList).filter(cls => cls.startsWith('animate-'));
                        classes.forEach(cls => icon.classList.remove(cls));
                        void icon.offsetWidth; // Trigger reflow
                        classes.forEach(cls => icon.classList.add(cls));
                    });
                }
            });
        });

    </script>
</body>
</html>
