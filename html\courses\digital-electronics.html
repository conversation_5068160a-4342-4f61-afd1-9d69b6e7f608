<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Electronics - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/course-page.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="course-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-home-btn">
                <i class="fas fa-home"></i>
                <span>Back to Home</span>
            </a>
            <div class="course-title">Digital Electronics</div>
            <div class="nav-actions">
                <button class="search-btn" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                </button>
                <button class="theme-toggle" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" id="courseProgress"></div>
                    </div>
                    <span class="progress-text">Progress: 0%</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="course-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Digital Electronics</h1>
                <p>Explore the world of digital systems, logic gates, and microprocessors with hands-on simulations and real-world medical device applications.</p>
                <div class="course-stats">
                    <div class="stat">
                        <i class="fas fa-microchip"></i>
                        <span>6 Advanced Modules</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-clock"></i>
                        <span>4-5 Hours</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-certificate"></i>
                        <span>Advanced Certificate</span>
                    </div>
                </div>
                <div class="hero-buttons">
                    <button class="start-course-btn" onclick="startCourse()">
                        <i class="fas fa-play"></i>
                        Start Course
                    </button>
                    <button class="preview-btn" onclick="openPreview()">
                        <i class="fas fa-eye"></i>
                        Preview Course
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="course-preview">
                    <div class="preview-screen">
                        <div class="screen-header">
                            <div class="screen-controls">
                                <span class="control red"></span>
                                <span class="control yellow"></span>
                                <span class="control green"></span>
                            </div>
                            <span class="screen-title">Logic Gate Simulator</span>
                        </div>
                        <div class="screen-content">
                            <div class="demo-circuit">
                                <div class="logic-gate and-gate">
                                    <div class="gate-inputs">
                                        <div class="input high">1</div>
                                        <div class="input low">0</div>
                                    </div>
                                    <div class="gate-symbol">AND</div>
                                    <div class="gate-output low">0</div>
                                </div>
                            </div>
                            <div class="demo-formula">
                                <span class="formula">A ∧ B = Y</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="course-content">
        <div class="container">
            <div class="content-grid">
                <!-- Course Modules -->
                <div class="modules-section">
                    <h2><i class="fas fa-microchip"></i> Course Modules</h2>
                    <div class="modules-list">
                        <!-- Module 1: Number Systems -->
                        <div class="module-card" data-module="1">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-calculator"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 1: Number Systems</h3>
                                    <p>Binary, Decimal, Hexadecimal, and BCD systems</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 40 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Beginner</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator not-started" id="status-1">
                                        <i class="fas fa-play"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('binary-basics')">
                                        <i class="fas fa-binary"></i>
                                        <span>Binary Number System</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('number-conversion')">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>Number Conversion</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('bcd-system')">
                                        <i class="fas fa-code"></i>
                                        <span>BCD & Gray Code</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('arithmetic-operations')">
                                        <i class="fas fa-plus"></i>
                                        <span>Binary Arithmetic</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn" onclick="startModule(1)">
                                        <i class="fas fa-play"></i>
                                        Start Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 2: Logic Gates -->
                        <div class="module-card" data-module="2">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 2: Logic Gates</h3>
                                    <p>Basic and universal logic gates with truth tables</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 50 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Beginner</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-2">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('basic-gates')">
                                        <i class="fas fa-project-diagram"></i>
                                        <span>Basic Logic Gates</span>
                                        <div class="topic-badge">Interactive</div>
                                    </div>
                                    <div class="topic" onclick="openTopic('universal-gates')">
                                        <i class="fas fa-star"></i>
                                        <span>Universal Gates</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('truth-tables')">
                                        <i class="fas fa-table"></i>
                                        <span>Truth Tables</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('gate-simulator')">
                                        <i class="fas fa-play-circle"></i>
                                        <span>Logic Gate Simulator</span>
                                        <div class="topic-badge">New</div>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(2)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 3: Boolean Algebra -->
                        <div class="module-card" data-module="3">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-function"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 3: Boolean Algebra</h3>
                                    <p>Laws, theorems, and circuit simplification</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 60 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-3">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('boolean-laws')">
                                        <i class="fas fa-balance-scale"></i>
                                        <span>Boolean Laws</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('karnaugh-maps')">
                                        <i class="fas fa-th"></i>
                                        <span>Karnaugh Maps</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('circuit-simplification')">
                                        <i class="fas fa-compress-alt"></i>
                                        <span>Circuit Simplification</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('sop-pos')">
                                        <i class="fas fa-layer-group"></i>
                                        <span>SOP & POS Forms</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(3)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 4: Combinational Circuits -->
                        <div class="module-card" data-module="4">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-puzzle-piece"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 4: Combinational Circuits</h3>
                                    <p>Encoders, decoders, multiplexers, and adders</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 70 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-4">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('adders-subtractors')">
                                        <i class="fas fa-plus-circle"></i>
                                        <span>Adders & Subtractors</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('encoders-decoders')">
                                        <i class="fas fa-code"></i>
                                        <span>Encoders & Decoders</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('multiplexers')">
                                        <i class="fas fa-random"></i>
                                        <span>Multiplexers & Demux</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('comparators')">
                                        <i class="fas fa-not-equal"></i>
                                        <span>Comparators</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(4)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 5: Sequential Circuits -->
                        <div class="module-card" data-module="5">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-history"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 5: Sequential Circuits</h3>
                                    <p>Flip-flops, counters, and state machines</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 80 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-5">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('flip-flops')">
                                        <i class="fas fa-toggle-on"></i>
                                        <span>Flip-Flops</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('counters')">
                                        <i class="fas fa-sort-numeric-up"></i>
                                        <span>Counters</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('shift-registers')">
                                        <i class="fas fa-arrows-alt-h"></i>
                                        <span>Shift Registers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('state-machines')">
                                        <i class="fas fa-state-machine"></i>
                                        <span>State Machines</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(5)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 6: Medical Applications -->
                        <div class="module-card" data-module="6">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 6: Medical Device Applications</h3>
                                    <p>Digital systems in medical equipment and devices</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 65 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-6">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('digital-ecg')">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Digital ECG Processing</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('pulse-oximeter')">
                                        <i class="fas fa-hand-holding-heart"></i>
                                        <span>Pulse Oximeter Logic</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('medical-alarms')">
                                        <i class="fas fa-bell"></i>
                                        <span>Medical Alarm Systems</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('data-acquisition')">
                                        <i class="fas fa-database"></i>
                                        <span>Data Acquisition Systems</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(6)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Sidebar -->
                <div class="course-sidebar">
                    <!-- Course Progress -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-chart-pie"></i> Your Progress</h3>
                        <div class="progress-circle">
                            <div class="circle">
                                <div class="progress-value">0%</div>
                            </div>
                        </div>
                        <div class="progress-stats">
                            <div class="stat">
                                <span class="label">Completed:</span>
                                <span class="value">0/6 modules</span>
                            </div>
                            <div class="stat">
                                <span class="label">Time Spent:</span>
                                <span class="value">0 hours</span>
                            </div>
                            <div class="stat">
                                <span class="label">Next Module:</span>
                                <span class="value">Number Systems</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Tools -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-tools"></i> Digital Tools</h3>
                        <div class="tools-list">
                            <button class="tool-btn" onclick="openTool('logic-simulator')">
                                <i class="fas fa-microchip"></i>
                                <span>Logic Gate Simulator</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('truth-table-generator')">
                                <i class="fas fa-table"></i>
                                <span>Truth Table Generator</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('karnaugh-solver')">
                                <i class="fas fa-th"></i>
                                <span>Karnaugh Map Solver</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('number-converter')">
                                <i class="fas fa-exchange-alt"></i>
                                <span>Number System Converter</span>
                            </button>
                        </div>
                    </div>

                    <!-- Prerequisites -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-graduation-cap"></i> Prerequisites</h3>
                        <div class="related-courses">
                            <div class="related-course" onclick="openCourse('fundamentals')">
                                <div class="course-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Electronics Fundamentals</h4>
                                    <p>Basic concepts and Ohm's law</p>
                                </div>
                            </div>
                            <div class="related-course" onclick="openCourse('analog-electronics')">
                                <div class="course-icon">
                                    <i class="fas fa-wave-square"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Analog Electronics</h4>
                                    <p>Recommended for comparison</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-container">
            <input type="text" class="search-input" placeholder="Search digital electronics content..." id="searchInput">
            <button class="search-close" onclick="toggleSearch()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="search-results" id="searchResults">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <script src="../../js/course-page.js"></script>
    <script>
        // Override course-specific settings
        document.addEventListener('DOMContentLoaded', function() {
            // Update course-specific variables
            window.courseModuleCount = 6;
            window.courseName = 'digital-electronics';
            
            // Update progress stats
            const nextModuleStat = document.querySelector('.progress-stats .stat:nth-child(3) .value');
            if (nextModuleStat) {
                const moduleNames = ['Number Systems', 'Logic Gates', 'Boolean Algebra', 'Combinational Circuits', 'Sequential Circuits', 'Medical Applications'];
                nextModuleStat.textContent = moduleNames[0];
            }
        });
    </script>
</body>
</html>
