// Module interaction utilities and common functions

// Initialize common module functionality
function initializeModuleCommon() {
    setupConceptToggles();
    setupProgressTracking();
    setupKeyboardNavigation();
}

// Setup concept card toggles
function setupConceptToggles() {
    const conceptCards = document.querySelectorAll('.concept-card');
    
    conceptCards.forEach(card => {
        const header = card.querySelector('.concept-header');
        const content = card.querySelector('.concept-content');
        const toggle = card.querySelector('.concept-toggle');
        
        if (header && content && toggle) {
            header.addEventListener('click', function() {
                const isExpanded = content.classList.contains('expanded');
                
                // Close all other cards
                conceptCards.forEach(otherCard => {
                    if (otherCard !== card) {
                        const otherContent = otherCard.querySelector('.concept-content');
                        const otherToggle = otherCard.querySelector('.concept-toggle i');
                        
                        otherContent.classList.remove('expanded');
                        otherToggle.classList.remove('fa-chevron-up');
                        otherToggle.classList.add('fa-chevron-down');
                    }
                });
                
                // Toggle current card
                content.classList.toggle('expanded');
                const icon = toggle.querySelector('i');
                
                if (isExpanded) {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                } else {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                }
                
                // Track interaction
                trackConceptInteraction(card.dataset.concept);
            });
        }
    });
}

// Track concept interactions for analytics
function trackConceptInteraction(conceptId) {
    const interactions = JSON.parse(localStorage.getItem('conceptInteractions') || '{}');
    const today = new Date().toDateString();
    
    if (!interactions[today]) {
        interactions[today] = {};
    }
    
    if (!interactions[today][conceptId]) {
        interactions[today][conceptId] = 0;
    }
    
    interactions[today][conceptId]++;
    localStorage.setItem('conceptInteractions', JSON.stringify(interactions));
}

// Setup progress tracking
function setupProgressTracking() {
    // Track time spent on page
    const startTime = Date.now();
    let timeSpent = 0;
    
    // Update time every 30 seconds
    const timeTracker = setInterval(() => {
        timeSpent = Math.floor((Date.now() - startTime) / 1000);
        updateTimeSpent(timeSpent);
    }, 30000);
    
    // Save progress when leaving page
    window.addEventListener('beforeunload', function() {
        clearInterval(timeTracker);
        saveTimeSpent(timeSpent);
    });
    
    // Track scroll progress
    let maxScroll = 0;
    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        maxScroll = Math.max(maxScroll, scrollPercent);
        updateScrollProgress(maxScroll);
    });
}

// Update time spent display
function updateTimeSpent(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    // Update any time display elements
    const timeDisplays = document.querySelectorAll('.time-spent');
    timeDisplays.forEach(display => {
        display.textContent = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    });
}

// Save time spent to localStorage
function saveTimeSpent(seconds) {
    const currentPage = window.location.pathname;
    const timeData = JSON.parse(localStorage.getItem('timeSpentData') || '{}');
    
    if (!timeData[currentPage]) {
        timeData[currentPage] = 0;
    }
    
    timeData[currentPage] += seconds;
    localStorage.setItem('timeSpentData', JSON.stringify(timeData));
}

// Update scroll progress
function updateScrollProgress(percent) {
    const progressBars = document.querySelectorAll('.scroll-progress');
    progressBars.forEach(bar => {
        bar.style.width = `${percent}%`;
    });
}

// Setup keyboard navigation
function setupKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // ESC to close modals or expanded concepts
        if (e.key === 'Escape') {
            closeAllModals();
            collapseAllConcepts();
        }
        
        // Arrow keys for quiz navigation
        if (e.key === 'ArrowLeft') {
            const prevBtn = document.getElementById('prevQuestion');
            if (prevBtn && !prevBtn.disabled) {
                prevBtn.click();
            }
        }
        
        if (e.key === 'ArrowRight') {
            const nextBtn = document.getElementById('nextQuestion');
            if (nextBtn && !nextBtn.disabled) {
                nextBtn.click();
            }
        }
        
        // Number keys for quiz options (1-4)
        if (e.key >= '1' && e.key <= '4') {
            const activeQuestion = document.querySelector('.quiz-question.active');
            if (activeQuestion) {
                const options = activeQuestion.querySelectorAll('.quiz-option');
                const optionIndex = parseInt(e.key) - 1;
                if (options[optionIndex] && !options[optionIndex].disabled) {
                    options[optionIndex].click();
                }
            }
        }
    });
}

// Close all modals
function closeAllModals() {
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => modal.remove());
}

// Collapse all concept cards
function collapseAllConcepts() {
    const conceptCards = document.querySelectorAll('.concept-card');
    conceptCards.forEach(card => {
        const content = card.querySelector('.concept-content');
        const toggle = card.querySelector('.concept-toggle i');
        
        content.classList.remove('expanded');
        toggle.classList.remove('fa-chevron-up');
        toggle.classList.add('fa-chevron-down');
    });
}

// Simulation utilities
class SimulationController {
    constructor() {
        this.simulations = new Map();
        this.isRunning = false;
    }
    
    register(id, simulation) {
        this.simulations.set(id, simulation);
    }
    
    start(id) {
        const simulation = this.simulations.get(id);
        if (simulation && typeof simulation.start === 'function') {
            simulation.start();
            this.isRunning = true;
        }
    }
    
    stop(id) {
        const simulation = this.simulations.get(id);
        if (simulation && typeof simulation.stop === 'function') {
            simulation.stop();
            this.isRunning = false;
        }
    }
    
    stopAll() {
        this.simulations.forEach((simulation, id) => {
            this.stop(id);
        });
    }
    
    reset(id) {
        const simulation = this.simulations.get(id);
        if (simulation && typeof simulation.reset === 'function') {
            simulation.reset();
        }
    }
    
    resetAll() {
        this.simulations.forEach((simulation, id) => {
            this.reset(id);
        });
    }
}

// Global simulation controller
const simulationController = new SimulationController();

// Circuit simulation base class
class CircuitSimulation {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.isRunning = false;
        this.animationId = null;
        this.parameters = {};
    }
    
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.animate();
        }
    }
    
    stop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    reset() {
        this.stop();
        this.parameters = {};
        this.render();
    }
    
    animate() {
        if (this.isRunning) {
            this.update();
            this.render();
            this.animationId = requestAnimationFrame(() => this.animate());
        }
    }
    
    update() {
        // Override in subclasses
    }
    
    render() {
        // Override in subclasses
    }
    
    setParameter(name, value) {
        this.parameters[name] = value;
        this.render();
    }
    
    getParameter(name) {
        return this.parameters[name];
    }
}

// Current flow simulation
class CurrentFlowSimulation extends CircuitSimulation {
    constructor(containerId) {
        super(containerId);
        this.electrons = [];
        this.parameters = {
            current: 1.0,
            speed: 1.0
        };
        this.initializeElectrons();
    }
    
    initializeElectrons() {
        const wire = this.container.querySelector('.wire');
        if (!wire) return;
        
        const electronFlow = wire.querySelector('.electron-flow');
        if (!electronFlow) return;
        
        this.electrons = Array.from(electronFlow.querySelectorAll('.electron'));
    }
    
    update() {
        const current = this.parameters.current;
        const speed = current * this.parameters.speed;
        
        this.electrons.forEach((electron, index) => {
            const currentPosition = parseFloat(electron.style.left) || (index * -25);
            let newPosition = currentPosition + speed;
            
            if (newPosition > 100) {
                newPosition = -10;
            }
            
            electron.style.left = `${newPosition}%`;
        });
    }
    
    render() {
        // Update electron visibility based on current
        const current = this.parameters.current;
        const opacity = Math.min(current / 5, 1); // Max opacity at 5A
        
        this.electrons.forEach(electron => {
            electron.style.opacity = opacity;
        });
    }
}

// Voltage simulation
class VoltageSimulation extends CircuitSimulation {
    constructor(containerId) {
        super(containerId);
        this.parameters = {
            voltage: 12
        };
    }
    
    render() {
        const voltage = this.parameters.voltage;
        const battery = this.container.querySelector('.battery-body');
        const indicator = this.container.querySelector('.voltage-indicator');
        
        if (battery) {
            const intensity = Math.min(voltage / 24, 1);
            battery.style.background = `linear-gradient(90deg, #ff6b6b ${intensity * 100}%, #ddd ${intensity * 100}%)`;
        }
        
        if (indicator) {
            indicator.textContent = `${voltage}V`;
        }
    }
}

// Power simulation
class PowerSimulation extends CircuitSimulation {
    constructor(containerId) {
        super(containerId);
        this.parameters = {
            power: 0,
            voltage: 12,
            current: 1
        };
    }
    
    update() {
        const voltage = this.parameters.voltage;
        const current = this.parameters.current;
        this.parameters.power = voltage * current;
    }
    
    render() {
        const power = this.parameters.power;
        const glow = this.container.querySelector('.glow');
        const powerDisplay = this.container.querySelector('#powerValue');
        
        if (glow) {
            const intensity = Math.min(power / 100, 1); // Max glow at 100W
            glow.style.opacity = intensity;
        }
        
        if (powerDisplay) {
            powerDisplay.textContent = Math.round(power);
        }
    }
}

// Quiz utilities
class QuizManager {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.questions = [];
        this.currentQuestion = 0;
        this.score = 0;
        this.answers = [];
    }
    
    addQuestion(question) {
        this.questions.push(question);
    }
    
    start() {
        this.currentQuestion = 0;
        this.score = 0;
        this.answers = [];
        this.showQuestion(0);
    }
    
    showQuestion(index) {
        const questions = this.container.querySelectorAll('.quiz-question');
        questions.forEach((q, i) => {
            q.classList.toggle('active', i === index);
        });
        
        this.updateNavigation();
    }
    
    answerQuestion(questionIndex, answerIndex, isCorrect) {
        this.answers[questionIndex] = {
            answerIndex,
            isCorrect,
            timestamp: Date.now()
        };
        
        if (isCorrect) {
            this.score++;
        }
    }
    
    nextQuestion() {
        if (this.currentQuestion < this.questions.length - 1) {
            this.currentQuestion++;
            this.showQuestion(this.currentQuestion);
        } else {
            this.showResults();
        }
    }
    
    previousQuestion() {
        if (this.currentQuestion > 0) {
            this.currentQuestion--;
            this.showQuestion(this.currentQuestion);
        }
    }
    
    showResults() {
        const resultsContainer = this.container.querySelector('.quiz-results');
        const scoreDisplay = this.container.querySelector('#finalScore');
        
        if (resultsContainer) {
            resultsContainer.style.display = 'block';
        }
        
        if (scoreDisplay) {
            scoreDisplay.textContent = this.score;
        }
        
        // Hide current question
        const activeQuestion = this.container.querySelector('.quiz-question.active');
        if (activeQuestion) {
            activeQuestion.classList.remove('active');
        }
        
        // Save quiz results
        this.saveResults();
    }
    
    updateNavigation() {
        const prevBtn = this.container.querySelector('#prevQuestion');
        const nextBtn = this.container.querySelector('#nextQuestion');
        const counter = this.container.querySelector('.question-counter');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentQuestion === 0;
        }
        
        if (nextBtn) {
            nextBtn.textContent = this.currentQuestion === this.questions.length - 1 ? 'Finish Quiz' : 'Next';
        }
        
        if (counter) {
            counter.textContent = `${this.currentQuestion + 1} / ${this.questions.length}`;
        }
    }
    
    saveResults() {
        const results = {
            score: this.score,
            totalQuestions: this.questions.length,
            answers: this.answers,
            completedAt: new Date().toISOString(),
            timeSpent: this.calculateTimeSpent()
        };
        
        const quizResults = JSON.parse(localStorage.getItem('quizResults') || '{}');
        const currentPage = window.location.pathname;
        
        if (!quizResults[currentPage]) {
            quizResults[currentPage] = [];
        }
        
        quizResults[currentPage].push(results);
        localStorage.setItem('quizResults', JSON.stringify(quizResults));
    }
    
    calculateTimeSpent() {
        if (this.answers.length === 0) return 0;
        
        const firstAnswer = Math.min(...this.answers.map(a => a.timestamp));
        const lastAnswer = Math.max(...this.answers.map(a => a.timestamp));
        
        return Math.round((lastAnswer - firstAnswer) / 1000); // seconds
    }
}

// Export utilities
window.SimulationController = SimulationController;
window.CircuitSimulation = CircuitSimulation;
window.CurrentFlowSimulation = CurrentFlowSimulation;
window.VoltageSimulation = VoltageSimulation;
window.PowerSimulation = PowerSimulation;
window.QuizManager = QuizManager;
window.simulationController = simulationController;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeModuleCommon();
});
