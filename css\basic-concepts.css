/* Basic Concepts Module Styles */

/* Hero Section */
.concept-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 20px 80px;
    margin-top: 80px;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.hero-text p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-objectives {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.hero-objectives h3 {
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.hero-objectives ul {
    list-style: none;
    padding: 0;
}

.hero-objectives li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.hero-objectives i {
    color: #4ecdc4;
}

/* Atom Visualization */
.hero-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.atom-visualization {
    width: 250px;
    height: 250px;
    position: relative;
}

.nucleus {
    width: 30px;
    height: 30px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    gap: 2px;
}

.proton, .neutron {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.proton {
    background: #ff6b6b;
    box-shadow: 0 0 10px #ff6b6b;
}

.neutron {
    background: #95a5a6;
    box-shadow: 0 0 10px #95a5a6;
}

.electron-shell {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.shell-1 {
    width: 80px;
    height: 80px;
    animation: rotate 3s linear infinite;
}

.shell-2 {
    width: 140px;
    height: 140px;
    animation: rotate 5s linear infinite reverse;
}

.electron {
    width: 10px;
    height: 10px;
    background: #4ecdc4;
    border-radius: 50%;
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 8px #4ecdc4;
}

.shell-2 .electron:nth-child(2) {
    top: auto;
    bottom: -5px;
}

@keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Slide Presentation */
.slide-presentation {
    padding: 60px 20px;
    background: #f8f9fa;
}

.presentation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.presentation-header h2 {
    font-size: 2.5rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 15px;
}

.slide-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.slide-btn {
    padding: 10px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.slide-btn:hover:not(:disabled) {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.slide-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.slide-counter {
    font-weight: 600;
    color: #666;
    font-size: 1.1rem;
}

/* Slides Container */
.slides-container {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    min-height: 600px;
}

.slide {
    display: none;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideIn 0.5s ease-in-out;
}

.slide.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-content {
    padding: 3rem;
}

.slide-content h3 {
    font-size: 2.2rem;
    margin-bottom: 2rem;
    color: #333;
    display: flex;
    align-items: center;
    gap: 15px;
    border-bottom: 3px solid #667eea;
    padding-bottom: 1rem;
}

.slide-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.slide-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #555;
}

.slide-text p {
    margin-bottom: 1.5rem;
}

/* Key Points and Formula Boxes */
.key-points, .formula-box, .material-types {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 15px;
    margin-top: 1.5rem;
    border-left: 4px solid #667eea;
}

.key-points h4, .formula-box h4, .material-types h4 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.2rem;
}

.key-points ul, .formula-explanation ul {
    list-style: none;
    padding: 0;
}

.key-points li, .formula-explanation li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    color: #555;
}

.key-points i {
    color: #4ecdc4;
    font-size: 0.9rem;
}

.formula {
    font-size: 1.8rem;
    font-weight: bold;
    color: #667eea;
    text-align: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    margin: 1rem 0;
    font-family: 'Courier New', monospace;
}

.formula-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.formula-grid .formula {
    font-size: 1.3rem;
    padding: 0.8rem;
}

/* Material Types Grid */
.material-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.material-item {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.material-item:hover {
    transform: translateY(-3px);
}

.material-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.material-item.conductor i { color: #feca57; }
.material-item.semiconductor i { color: #667eea; }
.material-item.insulator i { color: #ff6b6b; }

.material-item span {
    display: block;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.material-item small {
    color: #666;
    font-size: 0.8rem;
}

/* Interactive Simulations */
.slide-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    min-height: 300px;
}

/* Electricity Demo */
.electricity-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.conductor-wire {
    width: 250px;
    height: 20px;
    background: linear-gradient(90deg, #ddd, #bbb);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.electron-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.moving-electron {
    width: 12px;
    height: 12px;
    background: #feca57;
    border-radius: 50%;
    position: absolute;
    top: 4px;
    animation: electronMove 3s linear infinite;
    box-shadow: 0 0 8px #feca57;
}

.moving-electron:nth-child(1) { animation-delay: 0s; }
.moving-electron:nth-child(2) { animation-delay: 1s; }
.moving-electron:nth-child(3) { animation-delay: 2s; }

@keyframes electronMove {
    0% { left: -15px; }
    100% { left: 250px; }
}

.demo-labels {
    display: flex;
    gap: 2rem;
}

.label {
    background: #667eea;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Current Simulator */
.current-simulator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.pipe-analogy {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.water-pipe {
    width: 200px;
    height: 30px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    border: 3px solid #2c3e50;
}

.water-flow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.water-particle {
    width: 10px;
    height: 10px;
    background: #ecf0f1;
    border-radius: 50%;
    position: absolute;
    top: 10px;
    animation: waterFlow 2s linear infinite;
}

.water-particle:nth-child(1) { animation-delay: 0s; }
.water-particle:nth-child(2) { animation-delay: 0.7s; }
.water-particle:nth-child(3) { animation-delay: 1.4s; }

@keyframes waterFlow {
    0% { left: -15px; }
    100% { left: 200px; }
}

.flow-meter {
    background: #2c3e50;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.2rem;
}

.current-control, .voltage-control, .resistance-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.current-control label, .voltage-control label, .resistance-control label {
    font-weight: 600;
    color: #333;
}

.current-control input, .voltage-control input, .resistance-control input {
    width: 200px;
    height: 8px;
    border-radius: 4px;
    background: #ddd;
    outline: none;
    cursor: pointer;
}

/* Voltage Simulator */
.voltage-simulator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.battery-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.battery-cell {
    display: flex;
    align-items: center;
    gap: 5px;
}

.positive-terminal, .negative-terminal {
    width: 25px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.5rem;
}

.positive-terminal {
    background: #ff6b6b;
    border-radius: 5px 0 0 5px;
}

.negative-terminal {
    background: #333;
    border-radius: 0 5px 5px 0;
}

.battery-body {
    width: 100px;
    height: 60px;
    background: #ddd;
    position: relative;
    border-radius: 5px;
    overflow: hidden;
}

.voltage-level {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(90deg, #ff6b6b, #e74c3c);
    transition: height 0.3s ease;
    height: 50%;
}

.voltage-display {
    background: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Resistance Simulator */
.resistance-simulator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.resistor-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.resistor-symbol {
    display: flex;
    align-items: center;
    gap: 10px;
}

.resistor-body {
    width: 80px;
    height: 30px;
    background: #f4e4bc;
    border-radius: 5px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    border: 2px solid #ddd;
}

.color-bands {
    display: flex;
    gap: 8px;
    height: 100%;
    align-items: center;
}

.band {
    width: 6px;
    height: 100%;
    border-radius: 2px;
}

.band1 { background: #8b4513; }
.band2 { background: #ff0000; }
.band3 { background: #ffa500; }
.band4 { background: #ffd700; }

.resistor-leads {
    width: 20px;
    height: 2px;
    background: #666;
}

.resistance-display {
    background: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.2rem;
}

/* Power Simulator */
.power-simulator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.light-bulb-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.bulb-container {
    position: relative;
}

.light-bulb {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bulb-glass {
    width: 60px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    border: 2px solid #ddd;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filament {
    width: 30px;
    height: 30px;
    border: 2px solid #666;
    border-radius: 50%;
    position: relative;
}

.filament::before, .filament::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #666;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.filament::after {
    transform: translate(-50%, -50%) rotate(90deg);
}

.glow-effect {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 255, 0, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bulb-base {
    width: 30px;
    height: 20px;
    background: #666;
    border-radius: 0 0 5px 5px;
}

.power-display {
    background: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 10px;
    font-weight: bold;
    font-size: 1.2rem;
}

.power-controls {
    display: flex;
    gap: 2rem;
}

.control-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #333;
}

.control-group input {
    width: 150px;
}

/* Circuit Builder */
.circuit-builder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.simple-circuit {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #ddd;
}

.current-flow {
    stroke-dasharray: 10;
    animation: dash 2s linear infinite;
}

@keyframes dash {
    to { stroke-dashoffset: -20; }
}

.current-arrow {
    animation: pulse 1s infinite;
}

.circuit-explanation {
    max-width: 300px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Symbol Legend */
.symbol-legend {
    margin-top: 1.5rem;
}

.symbols-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.symbol-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.symbol {
    width: 30px;
    height: 20px;
    position: relative;
}

.battery-symbol {
    background: linear-gradient(90deg, #ff6b6b 50%, #333 50%);
    border-radius: 3px;
}

.resistor-symbol {
    background: #f4e4bc;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.wire-symbol {
    background: #333;
    height: 3px;
    border-radius: 2px;
}

.switch-symbol {
    background: #ddd;
    border-radius: 3px;
    position: relative;
}

.switch-symbol::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 5px;
    right: 5px;
    height: 2px;
    background: #333;
    transform: rotate(15deg);
}

/* Block Diagram */
.block-diagram-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.medical-device-blocks {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.block-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.system-block {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #ddd;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    min-width: 100px;
    transition: transform 0.3s ease;
}

.system-block:hover {
    transform: scale(1.05);
}

.system-block.input {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.1);
}

.system-block.processing {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.system-block.output {
    border-color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.system-block i {
    font-size: 2rem;
}

.system-block.input i { color: #4ecdc4; }
.system-block.processing i { color: #667eea; }
.system-block.output i { color: #ff6b6b; }

.arrow {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
}

.block-description {
    text-align: center;
    color: #666;
    font-weight: 600;
}

/* Summary Section */
.summary-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.concepts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.concept-summary {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border-left: 4px solid #667eea;
}

.concept-summary i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.concept-summary h5 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.concept-summary p {
    color: #666;
    line-height: 1.6;
}

.next-steps {
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 3rem;
    border-radius: 20px;
}

.next-steps h4 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.next-steps p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .slide-layout {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .presentation-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .concepts-grid {
        grid-template-columns: 1fr;
    }
    
    .material-grid {
        grid-template-columns: 1fr;
    }
    
    .power-controls {
        flex-direction: column;
        gap: 1rem;
    }
    
    .block-row {
        flex-direction: column;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
}
