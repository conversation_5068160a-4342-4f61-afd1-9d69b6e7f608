// Digital Electronics Module JavaScript

// Global variables
let currentGate = 'and';
let inputA = 0;
let inputB = 0;

// Gate truth tables
const gateTruthTables = {
    and: [
        [0, 0, 0],
        [0, 1, 0],
        [1, 0, 0],
        [1, 1, 1]
    ],
    or: [
        [0, 0, 0],
        [0, 1, 1],
        [1, 0, 1],
        [1, 1, 1]
    ],
    not: [
        [0, 1],
        [1, 0]
    ],
    nand: [
        [0, 0, 1],
        [0, 1, 1],
        [1, 0, 1],
        [1, 1, 0]
    ],
    nor: [
        [0, 0, 1],
        [0, 1, 0],
        [1, 0, 0],
        [1, 1, 0]
    ],
    xor: [
        [0, 0, 0],
        [0, 1, 1],
        [1, 0, 1],
        [1, 1, 0]
    ],
    xnor: [
        [0, 0, 1],
        [0, 1, 0],
        [1, 0, 0],
        [1, 1, 1]
    ]
};

// Initialize the module
document.addEventListener('DOMContentLoaded', function() {
    initializeGateSimulator();
    initializeNumberConverter();
    setupEventListeners();
    animateHeroElements();
});

function initializeGateSimulator() {
    // Setup gate selection buttons
    const gateButtons = document.querySelectorAll('.gate-btn');
    gateButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            gateButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentGate = this.dataset.gate;
            updateGateDisplay();
            updateTruthTable();
        });
    });

    // Setup input toggles
    const inputABtn = document.getElementById('inputA');
    const inputBBtn = document.getElementById('inputB');

    if (inputABtn) {
        inputABtn.addEventListener('click', function() {
            inputA = inputA === 0 ? 1 : 0;
            this.textContent = inputA;
            this.dataset.state = inputA;
            updateGateOutput();
            highlightCurrentRow();
        });
    }

    if (inputBBtn) {
        inputBBtn.addEventListener('click', function() {
            inputB = inputB === 0 ? 1 : 0;
            this.textContent = inputB;
            this.dataset.state = inputB;
            updateGateOutput();
            highlightCurrentRow();
        });
    }

    // Initialize display
    updateGateDisplay();
    updateTruthTable();
    updateGateOutput();
}

function updateGateDisplay() {
    const gateSymbol = document.getElementById('gateSymbol');
    const inputBControl = document.getElementById('inputBControl');
    
    if (gateSymbol) {
        const gateShape = gateSymbol.querySelector('.gate-shape');
        const gateLabel = gateShape.querySelector('.gate-label');
        
        // Update gate label
        gateLabel.textContent = currentGate.toUpperCase();
        
        // Update gate shape class
        gateShape.className = `gate-shape ${currentGate}-shape`;
        
        // Show/hide second input for NOT gate
        if (inputBControl) {
            inputBControl.style.display = currentGate === 'not' ? 'none' : 'flex';
        }
    }
}

function updateTruthTable() {
    const truthTable = document.getElementById('truthTable');
    if (!truthTable) return;
    
    const tbody = truthTable.querySelector('tbody');
    tbody.innerHTML = '';
    
    const table = gateTruthTables[currentGate];
    
    // Update table header for NOT gate
    const thead = truthTable.querySelector('thead tr');
    if (currentGate === 'not') {
        thead.innerHTML = '<th>A</th><th>Output</th>';
    } else {
        thead.innerHTML = '<th>A</th><th>B</th><th>Output</th>';
    }
    
    // Populate table rows
    table.forEach(row => {
        const tr = document.createElement('tr');
        row.forEach(value => {
            const td = document.createElement('td');
            td.textContent = value;
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
    
    highlightCurrentRow();
}

function updateGateOutput() {
    const output = calculateGateOutput(currentGate, inputA, inputB);
    const outputElement = document.getElementById('gateOutput');
    
    if (outputElement) {
        outputElement.textContent = output;
        outputElement.dataset.state = output;
    }
}

function calculateGateOutput(gate, a, b) {
    switch(gate) {
        case 'and': return a && b ? 1 : 0;
        case 'or': return a || b ? 1 : 0;
        case 'not': return a ? 0 : 1;
        case 'nand': return a && b ? 0 : 1;
        case 'nor': return a || b ? 0 : 1;
        case 'xor': return a !== b ? 1 : 0;
        case 'xnor': return a === b ? 1 : 0;
        default: return 0;
    }
}

function highlightCurrentRow() {
    const truthTable = document.getElementById('truthTable');
    if (!truthTable) return;
    
    const rows = truthTable.querySelectorAll('tbody tr');
    rows.forEach(row => row.classList.remove('current-row'));
    
    let currentRowIndex;
    if (currentGate === 'not') {
        currentRowIndex = inputA;
    } else {
        currentRowIndex = inputA * 2 + inputB;
    }
    
    if (rows[currentRowIndex]) {
        rows[currentRowIndex].classList.add('current-row');
    }
}

function initializeNumberConverter() {
    const decimalInput = document.getElementById('decimalInput');
    const binaryInput = document.getElementById('binaryInput');
    const hexInput = document.getElementById('hexInput');
    const octalInput = document.getElementById('octalInput');

    if (decimalInput) {
        decimalInput.addEventListener('input', function() {
            const decimal = parseInt(this.value) || 0;
            updateAllConversions(decimal);
            showBinaryBreakdown(decimal);
        });
    }

    if (binaryInput) {
        binaryInput.addEventListener('input', function() {
            const binary = this.value.replace(/[^01]/g, '');
            this.value = binary;
            const decimal = parseInt(binary, 2) || 0;
            updateAllConversions(decimal);
            showBinaryBreakdown(decimal);
        });
    }

    if (hexInput) {
        hexInput.addEventListener('input', function() {
            const hex = this.value.replace(/[^0-9A-Fa-f]/g, '');
            this.value = hex;
            const decimal = parseInt(hex, 16) || 0;
            updateAllConversions(decimal);
            showBinaryBreakdown(decimal);
        });
    }

    if (octalInput) {
        octalInput.addEventListener('input', function() {
            const octal = this.value.replace(/[^0-7]/g, '');
            this.value = octal;
            const decimal = parseInt(octal, 8) || 0;
            updateAllConversions(decimal);
            showBinaryBreakdown(decimal);
        });
    }

    // Initialize with 0
    updateAllConversions(0);
    showBinaryBreakdown(0);
}

function updateAllConversions(decimal) {
    const decimalResult = document.getElementById('decimalResult');
    const binaryResult = document.getElementById('binaryResult');
    const hexResult = document.getElementById('hexResult');
    const octalResult = document.getElementById('octalResult');

    if (decimalResult) decimalResult.textContent = decimal.toString();
    if (binaryResult) binaryResult.textContent = decimal.toString(2);
    if (hexResult) hexResult.textContent = decimal.toString(16).toUpperCase();
    if (octalResult) octalResult.textContent = decimal.toString(8);
}

function showBinaryBreakdown(decimal) {
    const breakdownElement = document.getElementById('binaryBreakdown');
    if (!breakdownElement) return;

    const binary = decimal.toString(2);
    const breakdown = [];

    for (let i = 0; i < binary.length; i++) {
        const bit = binary[binary.length - 1 - i];
        const power = i;
        const value = bit === '1' ? Math.pow(2, power) : 0;
        
        breakdown.unshift({
            bit: bit,
            power: power,
            value: value
        });
    }

    breakdownElement.innerHTML = `
        <h4>Binary Breakdown:</h4>
        <div class="breakdown-grid">
            ${breakdown.map(item => `
                <div class="breakdown-item ${item.bit === '1' ? 'active' : ''}">
                    <div class="bit-value">${item.bit}</div>
                    <div class="power-label">2<sup>${item.power}</sup></div>
                    <div class="decimal-value">${item.value}</div>
                </div>
            `).join('')}
        </div>
        <div class="breakdown-sum">
            Sum: ${breakdown.filter(item => item.bit === '1').map(item => item.value).join(' + ')} = ${decimal}
        </div>
    `;
}

function setupEventListeners() {
    // Module navigation
    const moduleCards = document.querySelectorAll('.module-card');
    moduleCards.forEach(card => {
        card.addEventListener('click', function() {
            const module = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            openModule(module);
        });
    });
}

function openModule(moduleName) {
    // This would navigate to specific module pages
    console.log('Opening module:', moduleName);
    // For now, just show an alert
    alert(`Opening ${moduleName} module. This would navigate to a detailed page for this topic.`);
}

function animateHeroElements() {
    // Animate binary bits
    const bits = document.querySelectorAll('.bit');
    bits.forEach((bit, index) => {
        setTimeout(() => {
            bit.style.animation = 'bitPulse 2s ease-in-out infinite';
            bit.style.animationDelay = `${index * 0.2}s`;
        }, index * 100);
    });

    // Animate gate items
    const gateItems = document.querySelectorAll('.gate-item');
    gateItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + index * 200);
    });
}

// Progress tracking
function updateProgress(percentage) {
    const progressFill = document.getElementById('moduleProgress');
    const progressText = document.querySelector('.progress-text');
    
    if (progressFill) {
        progressFill.style.width = `${percentage}%`;
    }
    
    if (progressText) {
        progressText.textContent = `Progress: ${percentage}%`;
    }
}

// Utility functions
function formatBinary(decimal, bits = 8) {
    return decimal.toString(2).padStart(bits, '0');
}

function formatHex(decimal) {
    return decimal.toString(16).toUpperCase();
}

function validateBinary(binary) {
    return /^[01]+$/.test(binary);
}

function validateHex(hex) {
    return /^[0-9A-Fa-f]+$/.test(hex);
}

function validateOctal(octal) {
    return /^[0-7]+$/.test(octal);
}

// Export functions for global access
window.openModule = openModule;
window.updateProgress = updateProgress;

// Initialize progress tracking
document.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const rate = scrolled / (document.body.scrollHeight - window.innerHeight);
    const progress = Math.round(rate * 100);
    updateProgress(Math.min(progress, 100));
});

console.log('Digital Electronics module loaded successfully');
