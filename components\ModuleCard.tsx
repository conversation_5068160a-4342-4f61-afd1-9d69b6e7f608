
import React from 'react';
import { Module } from '../types';
import { Icons } from './icons/IconComponents'; // Corrected import path

interface ModuleCardProps {
  module: Module;
  onSelectModule: (moduleId: string) => void;
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onSelectModule }) => {
  const IconComponent = module.icon ? Icons[module.icon] : null;

  return (
    <div 
      className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 cursor-pointer flex flex-col"
      onClick={() => onSelectModule(module.id)}
      aria-label={`Select module: ${module.title}`}
    >
      <div className="bg-sky-600 p-6 flex items-center justify-center h-32">
        {IconComponent ? <IconComponent className="w-16 h-16 text-white" /> : <div className="w-16 h-16 bg-white opacity-20 rounded-full" aria-hidden="true" />}
      </div>
      <div className="p-6 flex-grow flex flex-col">
        <h3 className="text-xl font-semibold text-sky-800 mb-2">{module.title}</h3>
        <p className="text-slate-600 text-sm mb-4 flex-grow">{module.description}</p>
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent card click when button is clicked
            onSelectModule(module.id);
          }}
          className="mt-auto w-full bg-emerald-500 text-white py-2 px-4 rounded-lg hover:bg-emerald-600 transition-colors duration-200 text-sm font-medium"
        >
          Start Learning
        </button>
      </div>
    </div>
  );
};

export default ModuleCard;
