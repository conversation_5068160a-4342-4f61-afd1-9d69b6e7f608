// Course Page JavaScript

// Global variables
let courseProgress = 0;
let completedModules = [];
let currentModule = null;

// Initialize course page
document.addEventListener('DOMContentLoaded', function() {
    initializeCourse();
    setupEventListeners();
    loadProgress();
});

function initializeCourse() {
    // Initialize module cards
    const moduleCards = document.querySelectorAll('.module-card');
    moduleCards.forEach((card, index) => {
        const moduleHeader = card.querySelector('.module-header');
        moduleHeader.addEventListener('click', () => toggleModule(index + 1));
    });
    
    // Initialize progress circle
    updateProgressDisplay();
    
    // Initialize theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        document.querySelector('.theme-toggle')?.classList.add('dark-mode');
    }
}

function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            performSearch(e.target.value);
        });
        
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                toggleSearch();
            }
        });
    }
    
    // Close search overlay when clicking outside
    document.addEventListener('click', (e) => {
        const searchOverlay = document.getElementById('searchOverlay');
        if (searchOverlay?.classList.contains('active') && 
            e.target === searchOverlay) {
            toggleSearch();
        }
    });
}

function toggleModule(moduleNumber) {
    const moduleCard = document.querySelector(`[data-module="${moduleNumber}"]`);
    if (!moduleCard) return;
    
    // Close other modules
    document.querySelectorAll('.module-card').forEach(card => {
        if (card !== moduleCard) {
            card.classList.remove('expanded');
        }
    });
    
    // Toggle current module
    moduleCard.classList.toggle('expanded');
    
    // Smooth scroll to module if expanding
    if (moduleCard.classList.contains('expanded')) {
        setTimeout(() => {
            moduleCard.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }, 100);
    }
}

function startCourse() {
    // Start with the first module
    startModule(1);
}

function startModule(moduleNumber) {
    // Check if module is unlocked
    if (moduleNumber > 1 && !completedModules.includes(moduleNumber - 1)) {
        showNotification('Please complete the previous module first!', 'warning');
        return;
    }
    
    currentModule = moduleNumber;
    
    // Update module status
    updateModuleStatus(moduleNumber, 'in-progress');
    
    // Open the first topic of the module
    const moduleCard = document.querySelector(`[data-module="${moduleNumber}"]`);
    const firstTopic = moduleCard?.querySelector('.topic');
    if (firstTopic) {
        firstTopic.click();
    }
    
    // Save progress
    saveProgress();
}

function openTopic(topicName) {
    // Map topic names to their respective URLs
    const topicUrls = {
        'basic-concepts': '../presentations/basic-concepts-presentation.html',
        'voltage-demo': '../tools/voltage-demo.html',
        'current-flow': '../tools/current-flow.html',
        'power-calculator': '../tools/power-calculator.html',
        'ohms-law': '../presentations/ohms-law-presentation.html',
        'magic-triangle': '../tools/magic-triangle.html',
        'ohms-calculator': '../tools/ohms-calculator.html',
        'practical-examples': '../tools/practical-examples.html',
        'passive-components': '../presentations/passive-components-presentation.html',
        'resistor-color-code': '../tools/resistor-color-code.html',
        'capacitor-demo': '../tools/capacitor-demo.html',
        'inductor-behavior': '../tools/inductor-behavior.html',
        'kvl-kcl': '../tools/kvl-kcl.html',
        'series-parallel': '../tools/series-parallel.html',
        'circuit-simulator': '../simulator/circuit-simulator.html',
        'analysis-methods': '../tools/analysis-methods.html',
        'pacemaker-circuit': '../examples/pacemaker-circuit.html',
        'ecg-amplifier': '../examples/ecg-amplifier.html',
        'sensor-circuits': '../examples/sensor-circuits.html',
        'safety-considerations': '../examples/safety-considerations.html'
    };
    
    const url = topicUrls[topicName];
    if (url) {
        window.open(url, '_blank');
        
        // Mark topic as viewed
        markTopicAsViewed(topicName);
    } else {
        showNotification(`${topicName} is coming soon!`, 'info');
    }
}

function markTopicAsViewed(topicName) {
    // Add visual indicator that topic has been viewed
    const topicElement = document.querySelector(`[onclick="openTopic('${topicName}')"]`);
    if (topicElement && !topicElement.classList.contains('viewed')) {
        topicElement.classList.add('viewed');
        
        // Check if all topics in current module are viewed
        checkModuleCompletion();
    }
}

function checkModuleCompletion() {
    if (!currentModule) return;
    
    const moduleCard = document.querySelector(`[data-module="${currentModule}"]`);
    const topics = moduleCard?.querySelectorAll('.topic');
    const viewedTopics = moduleCard?.querySelectorAll('.topic.viewed');
    
    if (topics && viewedTopics && topics.length === viewedTopics.length) {
        completeModule(currentModule);
    }
}

function completeModule(moduleNumber) {
    if (completedModules.includes(moduleNumber)) return;
    
    completedModules.push(moduleNumber);
    updateModuleStatus(moduleNumber, 'completed');
    
    // Unlock next module
    if (moduleNumber < 5) {
        unlockModule(moduleNumber + 1);
    }
    
    // Update progress
    courseProgress = (completedModules.length / 5) * 100;
    updateProgressDisplay();
    
    // Save progress
    saveProgress();
    
    // Show completion notification
    showNotification(`Module ${moduleNumber} completed! 🎉`, 'success');
    
    // Check if course is completed
    if (completedModules.length === 5) {
        showCourseCompletion();
    }
}

function updateModuleStatus(moduleNumber, status) {
    const statusIndicator = document.getElementById(`status-${moduleNumber}`);
    if (!statusIndicator) return;
    
    // Remove all status classes
    statusIndicator.classList.remove('not-started', 'in-progress', 'completed', 'locked');
    
    // Add new status class
    statusIndicator.classList.add(status);
    
    // Update icon
    const icon = statusIndicator.querySelector('i');
    if (icon) {
        icon.className = getStatusIcon(status);
    }
    
    // Update button
    const button = document.querySelector(`[data-module="${moduleNumber}"] .start-module-btn`);
    if (button) {
        updateModuleButton(button, status, moduleNumber);
    }
}

function getStatusIcon(status) {
    const icons = {
        'not-started': 'fas fa-play',
        'in-progress': 'fas fa-clock',
        'completed': 'fas fa-check',
        'locked': 'fas fa-lock'
    };
    return icons[status] || 'fas fa-play';
}

function updateModuleButton(button, status, moduleNumber) {
    const icon = button.querySelector('i');
    const text = button.childNodes[button.childNodes.length - 1];
    
    switch (status) {
        case 'not-started':
            button.classList.remove('locked');
            button.disabled = false;
            icon.className = 'fas fa-play';
            text.textContent = ' Start Module';
            break;
        case 'in-progress':
            button.classList.remove('locked');
            button.disabled = false;
            icon.className = 'fas fa-arrow-right';
            text.textContent = ' Continue Module';
            break;
        case 'completed':
            button.classList.remove('locked');
            button.disabled = false;
            icon.className = 'fas fa-redo';
            text.textContent = ' Review Module';
            break;
        case 'locked':
            button.classList.add('locked');
            button.disabled = true;
            icon.className = 'fas fa-lock';
            text.textContent = ' Complete Previous Module';
            break;
    }
}

function unlockModule(moduleNumber) {
    updateModuleStatus(moduleNumber, 'not-started');
}

function updateProgressDisplay() {
    // Update progress bar
    const progressFill = document.getElementById('courseProgress');
    if (progressFill) {
        progressFill.style.width = `${courseProgress}%`;
    }
    
    // Update progress text
    const progressText = document.querySelector('.progress-text');
    if (progressText) {
        progressText.textContent = `Progress: ${Math.round(courseProgress)}%`;
    }
    
    // Update progress circle
    const progressValue = document.querySelector('.progress-value');
    if (progressValue) {
        progressValue.textContent = `${Math.round(courseProgress)}%`;
    }
    
    const circle = document.querySelector('.circle');
    if (circle) {
        const degrees = (courseProgress / 100) * 360;
        circle.style.background = `conic-gradient(#667eea ${degrees}deg, #e2e8f0 ${degrees}deg)`;
    }
    
    // Update progress stats
    const completedStat = document.querySelector('.progress-stats .stat:nth-child(1) .value');
    if (completedStat) {
        completedStat.textContent = `${completedModules.length}/5 modules`;
    }
    
    const nextModuleStat = document.querySelector('.progress-stats .stat:nth-child(3) .value');
    if (nextModuleStat) {
        const nextModule = completedModules.length < 5 ? completedModules.length + 1 : 'Course Complete';
        const moduleNames = ['Basic Concepts', 'Ohm\'s Law', 'Passive Components', 'Circuit Analysis', 'Biomedical Applications'];
        nextModuleStat.textContent = nextModule === 'Course Complete' ? nextModule : moduleNames[nextModule - 1];
    }
}

function saveProgress() {
    const progressData = {
        courseProgress,
        completedModules,
        currentModule,
        timestamp: Date.now()
    };
    localStorage.setItem('fundamentals-progress', JSON.stringify(progressData));
}

function loadProgress() {
    const savedProgress = localStorage.getItem('fundamentals-progress');
    if (savedProgress) {
        try {
            const data = JSON.parse(savedProgress);
            courseProgress = data.courseProgress || 0;
            completedModules = data.completedModules || [];
            currentModule = data.currentModule || null;
            
            // Update UI based on loaded progress
            completedModules.forEach(moduleNumber => {
                updateModuleStatus(moduleNumber, 'completed');
            });
            
            // Unlock next module if needed
            if (completedModules.length < 5) {
                updateModuleStatus(completedModules.length + 1, 'not-started');
            }
            
            updateProgressDisplay();
        } catch (error) {
            console.error('Error loading progress:', error);
        }
    }
}

function openPreview() {
    // Open a preview of the course content
    window.open('../presentations/basic-concepts-presentation.html', '_blank');
}

function openTool(toolName) {
    // Open specific tools
    const toolUrls = {
        'ohms-calculator': '../tools/ohms-calculator.html',
        'resistor-calculator': '../tools/resistor-calculator.html',
        'power-calculator': '../tools/power-calculator.html',
        'circuit-simulator': '../simulator/circuit-simulator.html'
    };
    
    const url = toolUrls[toolName];
    if (url) {
        window.open(url, '_blank');
    } else {
        showNotification(`${toolName} is coming soon!`, 'info');
    }
}

function openCourse(courseName) {
    // Open related courses
    const courseUrls = {
        'digital-electronics': '../level2/digital-electronics.html',
        'analog-electronics': '../level2/analog-electronics.html'
    };
    
    const url = courseUrls[courseName];
    if (url) {
        window.open(url, '_blank');
    } else {
        showNotification(`${courseName} course is coming soon!`, 'info');
    }
}

function toggleSearch() {
    const searchOverlay = document.getElementById('searchOverlay');
    const searchInput = document.getElementById('searchInput');
    
    if (searchOverlay) {
        searchOverlay.classList.toggle('active');
        if (searchOverlay.classList.contains('active')) {
            setTimeout(() => searchInput?.focus(), 300);
        }
    }
}

function performSearch(query) {
    const searchResults = document.getElementById('searchResults');
    if (!searchResults || !query.trim()) {
        searchResults.innerHTML = '';
        return;
    }
    
    // Sample search data for the fundamentals course
    const searchData = [
        { title: 'Basic Concepts', type: 'Module', description: 'Voltage, Current, Resistance, Power' },
        { title: 'Ohm\'s Law', type: 'Module', description: 'V = I × R relationship' },
        { title: 'Passive Components', type: 'Module', description: 'Resistors, Capacitors, Inductors' },
        { title: 'Circuit Analysis', type: 'Module', description: 'KVL, KCL, Series/Parallel' },
        { title: 'Biomedical Applications', type: 'Module', description: 'Medical device examples' },
        { title: 'Power Calculator', type: 'Tool', description: 'Calculate electrical power' },
        { title: 'Resistor Color Code', type: 'Tool', description: 'Decode resistor values' },
        { title: 'Magic Triangle', type: 'Tool', description: 'Ohm\'s Law visual aid' }
    ];
    
    const filteredResults = searchData.filter(item => 
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase())
    );
    
    if (filteredResults.length === 0) {
        searchResults.innerHTML = '<div class="no-results">No results found</div>';
        return;
    }
    
    searchResults.innerHTML = filteredResults.map(item => `
        <div class="search-result-item" onclick="searchResultClick('${item.title}')">
            <div class="result-title">${item.title}</div>
            <div class="result-type">${item.type}</div>
            <div class="result-description">${item.description}</div>
        </div>
    `).join('');
}

function searchResultClick(title) {
    // Handle search result clicks
    const moduleMap = {
        'Basic Concepts': 1,
        'Ohm\'s Law': 2,
        'Passive Components': 3,
        'Circuit Analysis': 4,
        'Biomedical Applications': 5
    };
    
    if (moduleMap[title]) {
        toggleSearch();
        setTimeout(() => {
            toggleModule(moduleMap[title]);
        }, 300);
    }
}

function toggleTheme() {
    const body = document.body;
    const themeToggle = document.querySelector('.theme-toggle');
    
    body.classList.toggle('dark-theme');
    
    if (themeToggle) {
        themeToggle.classList.toggle('dark-mode');
    }
    
    // Save theme preference
    localStorage.setItem('theme', body.classList.contains('dark-theme') ? 'dark' : 'light');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle',
        'info': 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

function showCourseCompletion() {
    // Show course completion modal or redirect
    showNotification('Congratulations! You have completed the Electronics Fundamentals course! 🎉🎓', 'success');
    
    // Could redirect to certificate page or next course
    setTimeout(() => {
        if (confirm('Would you like to continue to the next course: Digital Electronics?')) {
            window.open('../level2/digital-electronics.html', '_blank');
        }
    }, 2000);
}

// Export functions for global access
window.startCourse = startCourse;
window.startModule = startModule;
window.openTopic = openTopic;
window.openPreview = openPreview;
window.openTool = openTool;
window.openCourse = openCourse;
window.toggleSearch = toggleSearch;
window.toggleTheme = toggleTheme;
