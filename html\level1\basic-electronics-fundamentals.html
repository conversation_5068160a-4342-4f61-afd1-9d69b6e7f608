<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Electronics Fundamentals - Virtual Lab</title>
    <link rel="stylesheet" href="../../css/main.css">
    <link rel="stylesheet" href="../../css/module.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
            <h1>Basic Electronics Fundamentals</h1>
            <div class="progress-indicator">
                <span class="progress-text">Progress: 0%</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="module-content">
        <div class="container">
            <!-- Introduction Section -->
            <section class="intro-section">
                <div class="intro-content">
                    <h2><i class="fas fa-bolt"></i> Welcome to Electronics Fundamentals</h2>
                    <p>Master the basic concepts that form the foundation of all electronic systems. This interactive module will guide you through essential principles with hands-on simulations.</p>
                    
                    <div class="learning-objectives">
                        <h3>Learning Objectives</h3>
                        <ul>
                            <li><i class="fas fa-check"></i> Understand electric current, voltage, and resistance</li>
                            <li><i class="fas fa-check"></i> Learn about power and energy in electrical circuits</li>
                            <li><i class="fas fa-check"></i> Explore different types of electrical components</li>
                            <li><i class="fas fa-check"></i> Apply basic circuit analysis techniques</li>
                        </ul>
                    </div>
                </div>
                <div class="intro-animation">
                    <div class="atom-model">
                        <div class="nucleus"></div>
                        <div class="electron-orbit orbit-1">
                            <div class="electron"></div>
                        </div>
                        <div class="electron-orbit orbit-2">
                            <div class="electron"></div>
                        </div>
                        <div class="electron-orbit orbit-3">
                            <div class="electron"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Interactive Concepts -->
            <section class="concepts-section">
                <h2>Interactive Concepts</h2>
                
                <!-- Concept 1: Electric Current -->
                <div class="concept-card" data-concept="current">
                    <div class="concept-header">
                        <h3><i class="fas fa-water"></i> Electric Current</h3>
                        <button class="concept-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="concept-content">
                        <div class="concept-explanation">
                            <p>Electric current is the flow of electric charge through a conductor. It's measured in Amperes (A).</p>
                            <div class="formula-box">
                                <strong>I = Q / t</strong>
                                <span>Where I = Current, Q = Charge, t = Time</span>
                            </div>
                        </div>
                        <div class="concept-simulation">
                            <div class="current-demo">
                                <div class="wire">
                                    <div class="electron-flow">
                                        <div class="electron"></div>
                                        <div class="electron"></div>
                                        <div class="electron"></div>
                                        <div class="electron"></div>
                                    </div>
                                </div>
                                <div class="current-controls">
                                    <label>Current: <span id="currentValue">1.0</span> A</label>
                                    <input type="range" id="currentSlider" min="0" max="5" step="0.1" value="1.0">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Concept 2: Voltage -->
                <div class="concept-card" data-concept="voltage">
                    <div class="concept-header">
                        <h3><i class="fas fa-battery-full"></i> Voltage</h3>
                        <button class="concept-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="concept-content">
                        <div class="concept-explanation">
                            <p>Voltage is the electric potential difference between two points. It's the "pressure" that pushes current through a circuit.</p>
                            <div class="formula-box">
                                <strong>V = W / Q</strong>
                                <span>Where V = Voltage, W = Work, Q = Charge</span>
                            </div>
                        </div>
                        <div class="concept-simulation">
                            <div class="voltage-demo">
                                <div class="battery">
                                    <div class="battery-terminal positive">+</div>
                                    <div class="battery-body">
                                        <div class="voltage-indicator" id="voltageIndicator">12V</div>
                                    </div>
                                    <div class="battery-terminal negative">-</div>
                                </div>
                                <div class="voltage-controls">
                                    <label>Voltage: <span id="voltageValue">12</span> V</label>
                                    <input type="range" id="voltageSlider" min="0" max="24" step="1" value="12">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Concept 3: Resistance -->
                <div class="concept-card" data-concept="resistance">
                    <div class="concept-header">
                        <h3><i class="fas fa-cog"></i> Resistance</h3>
                        <button class="concept-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="concept-content">
                        <div class="concept-explanation">
                            <p>Resistance opposes the flow of electric current. It's measured in Ohms (Ω).</p>
                            <div class="formula-box">
                                <strong>R = V / I</strong>
                                <span>Ohm's Law: Resistance = Voltage / Current</span>
                            </div>
                        </div>
                        <div class="concept-simulation">
                            <div class="resistance-demo">
                                <div class="resistor">
                                    <div class="resistor-body">
                                        <div class="color-band band1"></div>
                                        <div class="color-band band2"></div>
                                        <div class="color-band band3"></div>
                                        <div class="color-band band4"></div>
                                    </div>
                                    <div class="resistor-leads"></div>
                                </div>
                                <div class="resistance-controls">
                                    <label>Resistance: <span id="resistanceValue">1000</span> Ω</label>
                                    <input type="range" id="resistanceSlider" min="100" max="10000" step="100" value="1000">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Concept 4: Power -->
                <div class="concept-card" data-concept="power">
                    <div class="concept-header">
                        <h3><i class="fas fa-fire"></i> Electrical Power</h3>
                        <button class="concept-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="concept-content">
                        <div class="concept-explanation">
                            <p>Electrical power is the rate at which electrical energy is consumed or produced. It's measured in Watts (W).</p>
                            <div class="formula-box">
                                <strong>P = V × I = I²R = V²/R</strong>
                                <span>Power formulas for different scenarios</span>
                            </div>
                        </div>
                        <div class="concept-simulation">
                            <div class="power-demo">
                                <div class="light-bulb">
                                    <div class="bulb-glass">
                                        <div class="filament"></div>
                                        <div class="glow" id="bulbGlow"></div>
                                    </div>
                                    <div class="bulb-base"></div>
                                </div>
                                <div class="power-display">
                                    <div class="power-meter">
                                        <span>Power: <span id="powerValue">12</span> W</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Interactive Quiz -->
            <section class="quiz-section">
                <h2><i class="fas fa-question-circle"></i> Knowledge Check</h2>
                <div class="quiz-container">
                    <div class="quiz-question active" data-question="1">
                        <h3>Question 1: What is the unit of electric current?</h3>
                        <div class="quiz-options">
                            <button class="quiz-option" data-answer="wrong">Volts (V)</button>
                            <button class="quiz-option" data-answer="correct">Amperes (A)</button>
                            <button class="quiz-option" data-answer="wrong">Ohms (Ω)</button>
                            <button class="quiz-option" data-answer="wrong">Watts (W)</button>
                        </div>
                        <div class="quiz-feedback"></div>
                    </div>

                    <div class="quiz-question" data-question="2">
                        <h3>Question 2: According to Ohm's Law, if voltage increases and resistance stays constant, current will:</h3>
                        <div class="quiz-options">
                            <button class="quiz-option" data-answer="correct">Increase</button>
                            <button class="quiz-option" data-answer="wrong">Decrease</button>
                            <button class="quiz-option" data-answer="wrong">Stay the same</button>
                            <button class="quiz-option" data-answer="wrong">Become zero</button>
                        </div>
                        <div class="quiz-feedback"></div>
                    </div>

                    <div class="quiz-question" data-question="3">
                        <h3>Question 3: What happens to power consumption when resistance increases (voltage constant)?</h3>
                        <div class="quiz-options">
                            <button class="quiz-option" data-answer="wrong">Power increases</button>
                            <button class="quiz-option" data-answer="correct">Power decreases</button>
                            <button class="quiz-option" data-answer="wrong">Power stays the same</button>
                            <button class="quiz-option" data-answer="wrong">Power becomes infinite</button>
                        </div>
                        <div class="quiz-feedback"></div>
                    </div>

                    <div class="quiz-navigation">
                        <button id="prevQuestion" class="btn btn-secondary" disabled>Previous</button>
                        <span class="question-counter">1 / 3</span>
                        <button id="nextQuestion" class="btn btn-primary">Next</button>
                    </div>

                    <div class="quiz-results" style="display: none;">
                        <h3>Quiz Complete!</h3>
                        <div class="score-display">
                            <span class="score">Score: <span id="finalScore">0</span>/3</span>
                        </div>
                        <button id="retakeQuiz" class="btn btn-secondary">Retake Quiz</button>
                        <button id="continueToNext" class="btn btn-primary">Continue to Next Module</button>
                    </div>
                </div>
            </section>

            <!-- Next Steps -->
            <section class="next-steps">
                <h2>What's Next?</h2>
                <div class="next-modules">
                    <div class="next-module-card">
                        <h3><i class="fas fa-calculator"></i> Ohm's Law & Circuit Analysis</h3>
                        <p>Dive deeper into circuit analysis using Ohm's Law and learn to solve complex circuits.</p>
                        <button class="btn btn-primary" onclick="openModule('ohms-law')">Start Module</button>
                    </div>
                    <div class="next-module-card">
                        <h3><i class="fas fa-cog"></i> Passive Components</h3>
                        <p>Explore resistors, capacitors, and inductors in detail with interactive simulations.</p>
                        <button class="btn btn-primary" onclick="openModule('passive-components')">Start Module</button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../../js/main.js"></script>
    <script src="../../js/module-interactions.js"></script>
    <script>
        // Module-specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            initializeModule();
            setupInteractiveElements();
            setupQuiz();
        });

        function initializeModule() {
            // Mark module as started
            updateProgress(1, 'basic-concepts', false);
        }

        function setupInteractiveElements() {
            // Current simulation
            const currentSlider = document.getElementById('currentSlider');
            const currentValue = document.getElementById('currentValue');
            
            if (currentSlider) {
                currentSlider.addEventListener('input', function() {
                    currentValue.textContent = this.value;
                    updateElectronFlow(this.value);
                });
            }

            // Voltage simulation
            const voltageSlider = document.getElementById('voltageSlider');
            const voltageValue = document.getElementById('voltageValue');
            const voltageIndicator = document.getElementById('voltageIndicator');
            
            if (voltageSlider) {
                voltageSlider.addEventListener('input', function() {
                    voltageValue.textContent = this.value;
                    voltageIndicator.textContent = this.value + 'V';
                    updateBatteryDisplay(this.value);
                });
            }

            // Resistance simulation
            const resistanceSlider = document.getElementById('resistanceSlider');
            const resistanceValue = document.getElementById('resistanceValue');
            
            if (resistanceSlider) {
                resistanceSlider.addEventListener('input', function() {
                    resistanceValue.textContent = this.value;
                    updateResistorColors(this.value);
                });
            }

            // Concept toggles
            const conceptToggles = document.querySelectorAll('.concept-toggle');
            conceptToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const card = this.closest('.concept-card');
                    const content = card.querySelector('.concept-content');
                    const icon = this.querySelector('i');
                    
                    content.classList.toggle('expanded');
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');
                });
            });
        }

        function updateElectronFlow(current) {
            const electrons = document.querySelectorAll('.electron-flow .electron');
            const speed = current * 0.5; // Adjust speed based on current
            
            electrons.forEach((electron, index) => {
                electron.style.animationDuration = `${2 / speed}s`;
                electron.style.animationDelay = `${index * 0.2}s`;
            });
        }

        function updateBatteryDisplay(voltage) {
            const battery = document.querySelector('.battery-body');
            const intensity = voltage / 24; // Normalize to 0-1
            battery.style.background = `linear-gradient(90deg, #ff6b6b ${intensity * 100}%, #ddd ${intensity * 100}%)`;
        }

        function updateResistorColors(resistance) {
            // Simplified color coding for demonstration
            const bands = document.querySelectorAll('.color-band');
            const colors = ['#8B4513', '#FF0000', '#FFA500', '#FFD700']; // Brown, Red, Orange, Gold
            
            bands.forEach((band, index) => {
                band.style.backgroundColor = colors[index];
            });
        }

        function setupQuiz() {
            let currentQuestion = 1;
            let score = 0;
            const totalQuestions = 3;

            const prevBtn = document.getElementById('prevQuestion');
            const nextBtn = document.getElementById('nextQuestion');
            const questionCounter = document.querySelector('.question-counter');

            // Quiz option handlers
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.addEventListener('click', function() {
                    const question = this.closest('.quiz-question');
                    const options = question.querySelectorAll('.quiz-option');
                    const feedback = question.querySelector('.quiz-feedback');
                    
                    // Remove previous selections
                    options.forEach(opt => opt.classList.remove('selected', 'correct', 'wrong'));
                    
                    // Mark this option as selected
                    this.classList.add('selected');
                    
                    // Show feedback
                    if (this.dataset.answer === 'correct') {
                        this.classList.add('correct');
                        feedback.innerHTML = '<i class="fas fa-check"></i> Correct! Well done.';
                        feedback.className = 'quiz-feedback correct';
                        score++;
                    } else {
                        this.classList.add('wrong');
                        feedback.innerHTML = '<i class="fas fa-times"></i> Incorrect. Try reviewing the concept above.';
                        feedback.className = 'quiz-feedback wrong';
                        
                        // Show correct answer
                        const correctOption = question.querySelector('[data-answer="correct"]');
                        correctOption.classList.add('correct');
                    }
                    
                    // Disable all options
                    options.forEach(opt => opt.disabled = true);
                });
            });

            // Navigation handlers
            nextBtn.addEventListener('click', function() {
                if (currentQuestion < totalQuestions) {
                    document.querySelector(`[data-question="${currentQuestion}"]`).classList.remove('active');
                    currentQuestion++;
                    document.querySelector(`[data-question="${currentQuestion}"]`).classList.add('active');
                    
                    questionCounter.textContent = `${currentQuestion} / ${totalQuestions}`;
                    prevBtn.disabled = false;
                    
                    if (currentQuestion === totalQuestions) {
                        nextBtn.textContent = 'Finish Quiz';
                    }
                } else {
                    // Show results
                    document.querySelector('.quiz-container .quiz-question.active').classList.remove('active');
                    document.querySelector('.quiz-results').style.display = 'block';
                    document.getElementById('finalScore').textContent = score;
                    
                    // Update progress if passed
                    if (score >= 2) {
                        updateProgress(1, 'basic-concepts', true);
                        updateModuleProgress(100);
                    }
                }
            });

            prevBtn.addEventListener('click', function() {
                if (currentQuestion > 1) {
                    document.querySelector(`[data-question="${currentQuestion}"]`).classList.remove('active');
                    currentQuestion--;
                    document.querySelector(`[data-question="${currentQuestion}"]`).classList.add('active');
                    
                    questionCounter.textContent = `${currentQuestion} / ${totalQuestions}`;
                    nextBtn.textContent = 'Next';
                    
                    if (currentQuestion === 1) {
                        prevBtn.disabled = true;
                    }
                }
            });
        }

        function updateModuleProgress(percentage) {
            const progressFill = document.getElementById('moduleProgress');
            const progressText = document.querySelector('.progress-text');
            
            if (progressFill) {
                progressFill.style.width = `${percentage}%`;
                progressText.textContent = `Progress: ${percentage}%`;
            }
        }
    </script>
</body>
</html>
