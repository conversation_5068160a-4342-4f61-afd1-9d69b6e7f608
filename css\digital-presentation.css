/* Digital Electronics Presentation Styles */

/* Digital Showcase */
.digital-showcase {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    align-items: center;
}

.logic-gates-demo {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.gate-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.gate-inputs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-pin, .gate-output {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.input-pin.high, .gate-output.high {
    background: #4facfe;
    color: white;
    box-shadow: 0 0 15px rgba(79, 172, 254, 0.6);
}

.input-pin.low, .gate-output.low {
    background: #6c757d;
    color: white;
}

.gate-symbol {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    font-weight: bold;
    font-size: 0.8rem;
}

/* Binary Counter */
.binary-counter {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.bit-display {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.bit {
    width: 40px;
    height: 40px;
    background: #1a1a2e;
    color: #4facfe;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    border: 2px solid #4facfe;
    transition: all 0.3s ease;
}

.counter-label {
    text-align: center;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Number Systems */
.number-systems {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.system-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 1.5rem;
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    text-align: center;
    transition: all 0.3s ease;
}

.system-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.system-card h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.system-card .example {
    background: #667eea;
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    font-weight: bold;
    margin-top: 1rem;
}

/* Number Converter */
.number-converter {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.converter-input {
    margin-bottom: 1.5rem;
}

.converter-input label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
}

.converter-input input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.converter-input input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.conversion-results {
    display: grid;
    gap: 1rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.result-item .label {
    font-weight: 600;
    color: #4a5568;
}

.result-item .value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #667eea;
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

/* Binary Visualization */
.binary-visualization {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.place-values {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.place-value {
    text-align: center;
}

.bit-position {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.bit-value {
    width: 40px;
    height: 40px;
    background: #667eea;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bit-value:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.place-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.calculation {
    text-align: center;
    font-weight: 600;
    color: #2d3748;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Logic Gates Grid */
.gates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.gate-demo {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.gate-demo:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.gate-header h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.gate-header p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.gate-symbol-large {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.gate-inputs-large {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.input-terminal {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.input-terminal:hover {
    transform: scale(1.05);
}

.input-label, .output-label {
    font-weight: bold;
    color: #4a5568;
}

.input-value, .output-value {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    transition: all 0.3s ease;
}

.input-terminal[data-state="0"] .input-value,
.output-terminal .output-value[data-output="0"] {
    background: #6c757d;
    color: white;
}

.input-terminal[data-state="1"] .input-value,
.output-terminal .output-value[data-output="1"] {
    background: #4facfe;
    color: white;
    box-shadow: 0 0 15px rgba(79, 172, 254, 0.6);
}

.gate-body {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    font-weight: bold;
    text-align: center;
}

.output-terminal {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Truth Tables */
.truth-table {
    overflow-x: auto;
}

.truth-table table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.truth-table th,
.truth-table td {
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.truth-table th {
    background: #667eea;
    color: white;
    font-weight: bold;
}

.truth-table td {
    background: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

/* Boolean Algebra */
.boolean-content {
    display: grid;
    gap: 3rem;
}

.laws-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.law-card {
    background: white;
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.law-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
}

.law-card h4 {
    color: #667eea;
    margin-bottom: 1rem;
    font-weight: 700;
}

.law-equations {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.equation {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    text-align: center;
    color: #2d3748;
}

/* Simplification Demo */
.simplification-demo {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.simplification-example {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
}

.original-circuit,
.simplified-circuit {
    text-align: center;
}

.original-circuit h4,
.simplified-circuit h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
}

.expression {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.1rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.circuit-diagram {
    background: #1a1a2e;
    padding: 1.5rem;
    border-radius: 10px;
    color: white;
}

.complex-circuit {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.gate-level {
    display: flex;
    gap: 1rem;
}

.and-gate-small {
    background: #4facfe;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: bold;
}

.or-gate-final,
.or-gate-simple {
    background: #fa709a;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: bold;
}

.simplification-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
}

.simplification-arrow i {
    font-size: 2rem;
}

.simplification-arrow span {
    font-weight: 600;
}

/* Medical Applications */
.medical-applications {
    display: grid;
    gap: 2rem;
}

.application-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.application-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.app-header h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.app-header h3 i {
    color: #667eea;
}

.app-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

/* Signal Flow */
.signal-flow {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.stage {
    text-align: center;
    flex: 1;
}

.stage-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 0.5rem;
}

.stage-label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
}

.arrow {
    color: #667eea;
    font-size: 1.5rem;
    font-weight: bold;
}

/* ECG Waveform */
.ecg-waveform {
    background: #1a1a2e;
    padding: 1rem;
    border-radius: 10px;
    display: flex;
    justify-content: center;
}

/* Pulse Oximeter */
.oximeter-demo {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.sensor-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.led-array {
    display: flex;
    gap: 0.5rem;
}

.led {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
}

.red-led {
    background: #ff6b6b;
}

.ir-led {
    background: #6c757d;
}

.finger {
    font-size: 3rem;
    color: #fdbcb4;
}

.photodiode {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    color: #4a5568;
}

.photodiode i {
    font-size: 1.5rem;
    color: #667eea;
}

.digital-processor {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
}

.digital-processor h4 {
    color: #2d3748;
    margin-bottom: 1rem;
    text-align: center;
}

.processing-steps {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.step {
    background: white;
    padding: 0.5rem;
    border-radius: 5px;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
}

.spo2-display {
    display: flex;
    justify-content: center;
}

.display-screen {
    background: #1a1a2e;
    color: #4facfe;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #4facfe;
}

.reading {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.reading .unit {
    font-size: 1.5rem;
}

.label {
    font-size: 1rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .logic-gates-demo {
        flex-direction: column;
        align-items: center;
    }
    
    .gates-grid {
        grid-template-columns: 1fr;
    }
    
    .simplification-example {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .simplification-arrow {
        transform: rotate(90deg);
    }
    
    .app-content {
        grid-template-columns: 1fr;
    }
    
    .oximeter-demo {
        grid-template-columns: 1fr;
    }
    
    .signal-flow {
        flex-direction: column;
    }
    
    .arrow {
        transform: rotate(90deg);
    }
}
