<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المكونات الإلكترونية الأساسية (الملفات والدايودات)</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            overflow: hidden;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            overflow: hidden;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 1.5s ease-in-out;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.9; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); opacity: 0.9; }
        }
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.4);
        }
        .magnetic-field {
            animation: magneticPulse 3s ease-in-out infinite;
        }
        @keyframes magneticPulse {
            0% { transform: scale(0.8); opacity: 0.2; }
            50% { transform: scale(1.2); opacity: 0.8; }
            100% { transform: scale(0.8); opacity: 0.2; }
        }
        .circuit-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: dash 6s linear forwards infinite;
        }
        @keyframes dash {
            to { stroke-dashoffset: 0; }
        }
        .electron-flow {
            animation: electronFlow 3s linear infinite;
        }
        @keyframes electronFlow {
            0% { transform: translateX(0); }
            100% { transform: translateX(100px); }
        }
        .led-glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { filter: drop-shadow(0 0 2px rgba(255, 0, 0, 0.7)); }
            to { filter: drop-shadow(0 0 10px rgba(255, 0, 0, 0.9)); }
        }
        .current-flow {
            stroke-dasharray: 20;
            animation: flow 2s linear infinite;
        }
        @keyframes flow {
            to { stroke-dashoffset: -100; }
        }
    </style>
</head>
<body>
    <div class="slide bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex flex-col p-10 text-white">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg width="1280" height="720" viewBox="0 0 1280 720">
                <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4fd1c5" stroke-width="1" opacity="0.3"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                <path class="circuit-line" d="M50,200 C150,250 250,150 350,200 L600,200" fill="none" stroke="#4fd1c5" stroke-width="2"/>
                <path class="circuit-line" d="M1200,500 C1100,450 1000,550 900,500 L650,500" fill="none" stroke="#4fd1c5" stroke-width="2"/>
            </svg>
        </div>
        
        <!-- Header with animated icon -->
        <div class="flex items-center mb-6 fade-in">
            <div class="pulse bg-teal-600 bg-opacity-30 rounded-full p-3 ml-4">
                <i class="fas fa-bolt text-teal-300 text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-teal-300">المكونات الإلكترونية الأساسية (الملفات والدايودات)</h1>
        </div>
        
        <!-- Main content in two columns -->
        <div class="flex gap-8">
            <!-- Left column - Inductors -->
            <div class="w-1/2 fade-in" style="animation-delay: 0.3s">
                <div class="bg-black bg-opacity-30 rounded-lg p-5 shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-700 bg-opacity-50 rounded-full p-2 ml-3">
                            <i class="fas fa-sync-alt text-indigo-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-300">الملفات (Inductors)</h2>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">النظرية والوظيفة:</h3>
                        <p class="text-gray-300">
                            مكون يخزن الطاقة في مجال مغناطيسي عندما يمر به تيار كهربائي. يقاوم التغيرات المفاجئة في التيار، ويمكن تشبيهه بالقصور الذاتي الكهربائي.
                        </p>
                    </div>
                    
                    <!-- Inductor animation -->
                    <div class="mb-4">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4 flex flex-col items-center">
                            <svg width="220" height="120" viewBox="0 0 220 120">
                                <!-- Core -->
                                <rect x="60" y="50" width="100" height="20" fill="#696969" rx="2" opacity="0.5"/>
                                
                                <!-- Coil -->
                                <path d="M40,60 C50,40 60,80 70,60 C80,40 90,80 100,60 C110,40 120,80 130,60 C140,40 150,80 160,60 C170,40 180,80 190,60" 
                                      fill="none" stroke="#C0C0C0" stroke-width="3" />
                                
                                <!-- Magnetic field lines -->
                                <ellipse cx="110" cy="60" rx="60" ry="20" fill="none" stroke="#6366F1" stroke-width="1" class="magnetic-field" opacity="0.6" />
                                <ellipse cx="110" cy="60" rx="70" ry="30" fill="none" stroke="#6366F1" stroke-width="1" class="magnetic-field" opacity="0.5" style="animation-delay: 0.5s" />
                                <ellipse cx="110" cy="60" rx="80" ry="40" fill="none" stroke="#6366F1" stroke-width="1" class="magnetic-field" opacity="0.3" style="animation-delay: 1s" />
                                
                                <!-- Current flow arrow -->
                                <line x1="25" y1="60" x2="40" y2="60" stroke="#FBBF24" stroke-width="2" class="current-flow" />
                                <polygon points="40,60 35,57 35,63" fill="#FBBF24" />
                                
                                <line x1="190" y1="60" x2="205" y2="60" stroke="#FBBF24" stroke-width="2" class="current-flow" />
                                <polygon points="205,60 200,57 200,63" fill="#FBBF24" />
                            </svg>
                            <p class="text-sm text-center text-gray-300 mt-2">
                                ملف يولد مجال مغناطيسي عند مرور تيار به
                            </p>
                            <p class="text-xs text-teal-300 mt-1">
                                L = حثية الملف (تقاس بالهنري - H)
                            </p>
                        </div>
                    </div>
                    
                    <!-- Inductor types -->
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">أنواع الملفات:</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-gray-300 to-gray-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">ملفات هوائية</h4>
                                </div>
                                <p class="text-xs text-gray-300">بدون قلب، للترددات العالية</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-gray-600 to-gray-800 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">ملفات بقلب حديدي</h4>
                                </div>
                                <p class="text-xs text-gray-300">حثية عالية، للترددات المنخفضة</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-teal-300 to-teal-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">محولات</h4>
                                </div>
                                <p class="text-xs text-gray-300">لرفع أو خفض الجهد</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-green-300 to-green-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">ملفات SMD</h4>
                                </div>
                                <p class="text-xs text-gray-300">صغيرة الحجم، للدوائر المدمجة</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Applications in medical devices -->
                    <div>
                        <h3 class="text-lg font-bold mb-2 text-teal-300">التطبيقات في الأجهزة الطبية:</h3>
                        <div class="bg-teal-900 bg-opacity-20 p-3 rounded-lg">
                            <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                <li>وحدات التغذية التحويلية (SMPS) للأجهزة الطبية</li>
                                <li>محولات العزل الكهربائي للسلامة</li>
                                <li>فلاتر التداخل الكهرومغناطيسي (EMI) لحماية الإشارات الحيوية</li>
                                <li>دوائر الرنين في أجهزة التصوير بالرنين المغناطيسي</li>
                                <li>محولات الرفع في مولدات الجهد العالي (أجهزة الأشعة السينية)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right column - Diodes -->
            <div class="w-1/2">
                <div class="bg-black bg-opacity-30 rounded-lg p-5 shadow-lg fade-in" style="animation-delay: 0.6s">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-700 bg-opacity-50 rounded-full p-2 ml-3">
                            <i class="fas fa-arrow-right text-red-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-300">الدايودات (Diodes)</h2>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">النظرية والوظيفة:</h3>
                        <p class="text-gray-300">
                            مكون شبه موصل يسمح بمرور التيار في اتجاه واحد فقط ويمنع مروره في الاتجاه المعاكس. يمكن تشبيهه بصمام اتجاه واحد للتيار الكهربائي.
                        </p>
                    </div>
                    
                    <!-- Diode animation -->
                    <div class="mb-4">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4 flex flex-col items-center">
                            <div class="flex justify-center space-x-8">
                                <!-- Forward bias -->
                                <div class="flex flex-col items-center ml-4">
                                    <svg width="160" height="80" viewBox="0 0 160 80">
                                        <!-- Diode symbol -->
                                        <line x1="40" y1="40" x2="80" y2="40" stroke="#4FD1C5" stroke-width="2" />
                                        <polygon points="80,30 80,50 100,40" fill="#4FD1C5" stroke="#4FD1C5" stroke-width="1" />
                                        <line x1="100" y1="40" x2="140" y2="40" stroke="#4FD1C5" stroke-width="2" />
                                        <line x1="100" y1="30" x2="100" y2="50" stroke="#4FD1C5" stroke-width="2" />
                                        
                                        <!-- Electrons flow -->
                                        <circle cx="50" cy="40" r="3" fill="#FBBF24" class="electron-flow" />
                                        <circle cx="60" cy="40" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.2s" />
                                        <circle cx="70" cy="40" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.4s" />
                                        <circle cx="110" cy="40" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.6s" />
                                        <circle cx="120" cy="40" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.8s" />
                                        
                                        <!-- Voltage -->
                                        <text x="30" y="30" text-anchor="end" fill="#ffffff" font-size="12">+</text>
                                        <text x="150" y="30" text-anchor="start" fill="#ffffff" font-size="12">−</text>
                                    </svg>
                                    <p class="text-xs text-center text-gray-300">انحياز أمامي</p>
                                    <p class="text-xs text-center text-teal-300">(يمر التيار)</p>
                                </div>
                                
                                <!-- Reverse bias -->
                                <div class="flex flex-col items-center mr-4">
                                    <svg width="160" height="80" viewBox="0 0 160 80">
                                        <!-- Diode symbol -->
                                        <line x1="40" y1="40" x2="80" y2="40" stroke="#4FD1C5" stroke-width="2" />
                                        <polygon points="80,30 80,50 100,40" fill="#4FD1C5" stroke="#4FD1C5" stroke-width="1" />
                                        <line x1="100" y1="40" x2="140" y2="40" stroke="#4FD1C5" stroke-width="2" />
                                        <line x1="100" y1="30" x2="100" y2="50" stroke="#4FD1C5" stroke-width="2" />
                                        
                                        <!-- Red X for no current -->
                                        <path d="M60,30 L80,50 M60,50 L80,30" stroke="#FF5555" stroke-width="2" />
                                        
                                        <!-- Voltage -->
                                        <text x="30" y="30" text-anchor="end" fill="#ffffff" font-size="12">−</text>
                                        <text x="150" y="30" text-anchor="start" fill="#ffffff" font-size="12">+</text>
                                    </svg>
                                    <p class="text-xs text-center text-gray-300">انحياز عكسي</p>
                                    <p class="text-xs text-center text-red-400">(لا يمر التيار)</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Diode types -->
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">أنواع الدايودات:</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-gray-300 to-gray-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">دايود عادي</h4>
                                </div>
                                <p class="text-xs text-gray-300">لتقويم التيار المتردد</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-green-400 to-green-600 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">دايود زينر</h4>
                                </div>
                                <p class="text-xs text-gray-300">لتثبيت الجهد</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-red-400 to-red-600 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">LED</h4>
                                </div>
                                <p class="text-xs text-gray-300">باعث للضوء عند مرور التيار</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-blue-400 to-blue-600 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">فوتودايود</h4>
                                </div>
                                <p class="text-xs text-gray-300">حساس للضوء</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Applications in medical devices -->
                    <div>
                        <h3 class="text-lg font-bold mb-2 text-teal-300">التطبيقات في الأجهزة الطبية:</h3>
                        <div class="bg-teal-900 bg-opacity-20 p-3 rounded-lg">
                            <ul class="list-disc list-inside text-sm text-gray-300 space-y-1">
                                <li>تقويم التيار المتردد في وحدات التغذية الخاصة بالأجهزة الطبية</li>
                                <li>الحماية من عكس القطبية في الأجهزة المحمولة</li>
                                <li>LEDs كمؤشرات ضوئية للحالة والإنذارات</li>
                                <li>دايودات زينر لتثبيت الجهد في دوائر الحساسات</li>
                                <li>فوتودايودات في حساسات قياس النبض ومستوى الأكسجين (Pulse Oximeter)</li>
                                <li>حماية دوائر الإشارات الحيوية من الجهود العكسية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom section - Practical circuit example -->
        <div class="mt-4 fade-in" style="animation-delay: 0.9s">
            <div class="bg-black bg-opacity-30 rounded-lg p-4 shadow-lg">
                <h3 class="text-xl font-bold mb-3 text-teal-300 text-center">
                    <i class="fas fa-heartbeat ml-2"></i>
                    تطبيق عملي: دائرة الحماية من عكس القطبية في جهاز طبي محمول
                </h3>
                <div class="flex gap-6 items-center">
                    <!-- Circuit diagram -->
                    <div class="flex-1 flex justify-center">
                        <svg width="300" height="120" viewBox="0 0 300 120">
                            <!-- Battery -->
                            <rect x="20" y="45" width="30" height="40" rx="2" fill="#3182CE" opacity="0.7" />
                            <text x="35" y="70" text-anchor="middle" fill="#ffffff" font-size="10">بطارية</text>
                            <text x="15" y="55" text-anchor="end" fill="#ffffff" font-size="12">+</text>
                            <text x="15" y="75" text-anchor="end" fill="#ffffff" font-size="12">−</text>
                            
                            <!-- Diode -->
                            <line x1="50" y1="50" x2="80" y2="50" stroke="#4FD1C5" stroke-width="2" class="circuit-line" />
                            <polygon points="80,40 80,60 100,50" fill="#4FD1C5" stroke="#4FD1C5" stroke-width="1" />
                            <line x1="100" y1="50" x2="130" y2="50" stroke="#4FD1C5" stroke-width="2" class="circuit-line" />
                            <line x1="100" y1="40" x2="100" y2="60" stroke="#4FD1C5" stroke-width="2" />
                            
                            <!-- Medical device -->
                            <rect x="130" y="30" width="80" height="40" rx="5" fill="#4FD1C5" opacity="0.2" stroke="#4FD1C5" stroke-width="1" />
                            <text x="170" y="55" text-anchor="middle" fill="#ffffff" font-size="10">جهاز طبي</text>
                            
                            <!-- Ground -->
                            <line x1="50" y1="80" x2="170" y2="80" stroke="#4FD1C5" stroke-width="2" class="circuit-line" />
                            <line x1="170" y1="70" x2="170" y2="80" stroke="#4FD1C5" stroke-width="2" />
                            <line x1="160" y1="80" x2="180" y2="80" stroke="#4FD1C5" stroke-width="2" />
                            <line x1="163" y1="85" x2="177" y2="85" stroke="#4FD1C5" stroke-width="1.5" />
                            <line x1="166" y1="90" x2="174" y2="90" stroke="#4FD1C5" stroke-width="1" />
                            
                            <!-- Current flow animation -->
                            <circle cx="60" cy="50" r="3" fill="#FBBF24" class="electron-flow" />
                            <circle cx="70" cy="50" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.2s" />
                            <circle cx="110" cy="50" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.4s" />
                            <circle cx="120" cy="50" r="3" fill="#FBBF24" class="electron-flow" style="animation-delay: 0.6s" />
                            
                            <!-- LED indicator light -->
                            <circle cx="230" cy="50" r="10" fill="#FF0000" opacity="0.3" class="led-glow" />
                            <circle cx="230" cy="50" r="6" fill="#FF0000" />
                            <text x="230" y="80" text-anchor="middle" fill="#ffffff" font-size="10">LED مؤشر</text>
                            <line x1="210" y1="50" x2="220" y2="50" stroke="#4FD1C5" stroke-width="2" />
                            <line x1="240" y1="50" x2="250" y2="50" stroke="#4FD1C5" stroke-width="2" />
                            <line x1="250" y1="50" x2="250" y2="80" stroke="#4FD1C5" stroke-width="2" />
                            <line x1="250" y1="80" x2="170" y2="80" stroke="#4FD1C5" stroke-width="2" />
                        </svg>
                    </div>
                    
                    <!-- Example explanation -->
                    <div class="flex-1">
                        <p class="text-sm text-gray-300 mb-2">
                            <span class="text-teal-300 font-bold">الوظيفة:</span> 
                            حماية الدوائر الإلكترونية في الجهاز الطبي من الأضرار الناتجة عن توصيل البطارية بالقطبية المعكوسة.
                        </p>
                        <p class="text-sm text-gray-300 mb-2">
                            <span class="text-teal-300 font-bold">المبدأ:</span>
                            عند توصيل البطارية بشكل صحيح، يمر التيار عبر الدايود إلى الجهاز. وفي حالة عكس القطبية، يمنع الدايود مرور التيار، مما يحمي الدائرة الإلكترونية.
                        </p>
                        <p class="text-sm text-gray-300">
                            <span class