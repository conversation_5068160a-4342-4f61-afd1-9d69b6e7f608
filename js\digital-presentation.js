// Digital Electronics Presentation JavaScript

// Initialize presentation
document.addEventListener('DOMContentLoaded', function() {
    initializeDigitalPresentation();
    startAnimations();
});

function initializeDigitalPresentation() {
    // Initialize number converter
    initializeNumberConverter();
    
    // Initialize binary visualization
    initializeBinaryVisualization();
    
    // Initialize logic gates
    initializeLogicGates();
    
    // Initialize binary counter
    initializeBinaryCounter();
    
    // Initialize SpO2 animation
    initializeSpO2Animation();
}

// Number Converter Functions
function initializeNumberConverter() {
    const decimalInput = document.getElementById('decimalInput');
    if (decimalInput) {
        decimalInput.addEventListener('input', updateConversions);
        updateConversions(); // Initial conversion
    }
}

function updateConversions() {
    const decimal = parseInt(document.getElementById('decimalInput').value) || 0;
    
    // Update binary result
    const binary = decimal.toString(2);
    document.getElementById('binaryResult').textContent = binary;
    
    // Update hexadecimal result
    const hex = decimal.toString(16).toUpperCase();
    document.getElementById('hexResult').textContent = hex;
    
    // Update BCD result
    const bcd = decimalToBCD(decimal);
    document.getElementById('bcdResult').textContent = bcd;
    
    // Update binary visualization
    updateBinaryVisualization(decimal);
}

function decimalToBCD(decimal) {
    const digits = decimal.toString().split('');
    return digits.map(digit => {
        const binary = parseInt(digit).toString(2).padStart(4, '0');
        return binary;
    }).join(' ');
}

// Binary Visualization Functions
function initializeBinaryVisualization() {
    const bitValues = document.querySelectorAll('.bit-value');
    bitValues.forEach(bit => {
        bit.addEventListener('click', toggleBit);
    });
}

function updateBinaryVisualization(decimal) {
    const binary = decimal.toString(2).padStart(4, '0');
    const bits = binary.split('').reverse();
    
    // Update bit displays
    document.getElementById('bit-8').textContent = bits[3] || '0';
    document.getElementById('bit-4').textContent = bits[2] || '0';
    document.getElementById('bit-2').textContent = bits[1] || '0';
    document.getElementById('bit-1').textContent = bits[0] || '0';
    
    // Update bit colors
    updateBitColors();
    
    // Update calculation display
    updateCalculationDisplay();
}

function toggleBit(event) {
    const bit = event.target;
    const currentValue = parseInt(bit.textContent);
    const newValue = currentValue === 0 ? 1 : 0;
    bit.textContent = newValue;
    
    updateBitColors();
    updateCalculationDisplay();
    
    // Update decimal input
    const decimal = calculateDecimalFromBits();
    document.getElementById('decimalInput').value = decimal;
    updateConversions();
}

function updateBitColors() {
    const bitElements = [
        { element: document.getElementById('bit-8'), value: 8 },
        { element: document.getElementById('bit-4'), value: 4 },
        { element: document.getElementById('bit-2'), value: 2 },
        { element: document.getElementById('bit-1'), value: 1 }
    ];
    
    bitElements.forEach(({ element, value }) => {
        if (element) {
            if (element.textContent === '1') {
                element.style.background = '#4facfe';
                element.style.boxShadow = '0 0 15px rgba(79, 172, 254, 0.6)';
            } else {
                element.style.background = '#6c757d';
                element.style.boxShadow = 'none';
            }
        }
    });
}

function calculateDecimalFromBits() {
    const bit8 = parseInt(document.getElementById('bit-8').textContent) * 8;
    const bit4 = parseInt(document.getElementById('bit-4').textContent) * 4;
    const bit2 = parseInt(document.getElementById('bit-2').textContent) * 2;
    const bit1 = parseInt(document.getElementById('bit-1').textContent) * 1;
    
    return bit8 + bit4 + bit2 + bit1;
}

function updateCalculationDisplay() {
    const bit8 = document.getElementById('bit-8').textContent;
    const bit4 = document.getElementById('bit-4').textContent;
    const bit2 = document.getElementById('bit-2').textContent;
    const bit1 = document.getElementById('bit-1').textContent;
    
    const calculation = `${bit8}×8 + ${bit4}×4 + ${bit2}×2 + ${bit1}×1 = ${calculateDecimalFromBits()}`;
    document.getElementById('calculation-display').textContent = calculation;
}

// Logic Gates Functions
function initializeLogicGates() {
    const inputTerminals = document.querySelectorAll('.input-terminal');
    inputTerminals.forEach(terminal => {
        terminal.addEventListener('click', () => toggleInput(terminal));
    });
    
    // Initial gate calculations
    updateAllGateOutputs();
}

function toggleInput(terminal) {
    const currentState = parseInt(terminal.dataset.state);
    const newState = currentState === 0 ? 1 : 0;
    
    terminal.dataset.state = newState;
    terminal.querySelector('.input-value').textContent = newState;
    
    // Update visual state
    updateInputVisual(terminal, newState);
    
    // Update gate output
    updateGateOutput(terminal);
}

function updateInputVisual(terminal, state) {
    const inputValue = terminal.querySelector('.input-value');
    if (state === 1) {
        inputValue.style.background = '#4facfe';
        inputValue.style.boxShadow = '0 0 15px rgba(79, 172, 254, 0.6)';
    } else {
        inputValue.style.background = '#6c757d';
        inputValue.style.boxShadow = 'none';
    }
}

function updateGateOutput(terminal) {
    const gateDemo = terminal.closest('.gate-demo');
    const gateType = getGateType(gateDemo);
    const inputs = getGateInputs(gateDemo);
    const output = calculateGateOutput(gateType, inputs);
    
    updateOutputDisplay(gateDemo, output);
}

function getGateType(gateDemo) {
    if (gateDemo.classList.contains('and-demo')) return 'AND';
    if (gateDemo.classList.contains('or-demo')) return 'OR';
    if (gateDemo.classList.contains('not-demo')) return 'NOT';
    if (gateDemo.classList.contains('xor-demo')) return 'XOR';
    return 'AND';
}

function getGateInputs(gateDemo) {
    const inputTerminals = gateDemo.querySelectorAll('.input-terminal');
    return Array.from(inputTerminals).map(terminal => parseInt(terminal.dataset.state));
}

function calculateGateOutput(gateType, inputs) {
    switch (gateType) {
        case 'AND':
            return inputs.every(input => input === 1) ? 1 : 0;
        case 'OR':
            return inputs.some(input => input === 1) ? 1 : 0;
        case 'NOT':
            return inputs[0] === 1 ? 0 : 1;
        case 'XOR':
            return inputs.filter(input => input === 1).length === 1 ? 1 : 0;
        default:
            return 0;
    }
}

function updateOutputDisplay(gateDemo, output) {
    const outputValue = gateDemo.querySelector('.output-value');
    if (outputValue) {
        outputValue.textContent = output;
        outputValue.dataset.output = output;
        
        if (output === 1) {
            outputValue.style.background = '#4facfe';
            outputValue.style.boxShadow = '0 0 15px rgba(79, 172, 254, 0.6)';
        } else {
            outputValue.style.background = '#6c757d';
            outputValue.style.boxShadow = 'none';
        }
    }
}

function updateAllGateOutputs() {
    const gateDemons = document.querySelectorAll('.gate-demo');
    gateDemons.forEach(gateDemo => {
        const firstInput = gateDemo.querySelector('.input-terminal');
        if (firstInput) {
            updateGateOutput(firstInput);
        }
    });
}

// Binary Counter Functions
function initializeBinaryCounter() {
    let counter = 0;
    const maxCount = 15; // 4-bit counter (0-15)
    
    setInterval(() => {
        updateCounterDisplay(counter);
        counter = (counter + 1) % (maxCount + 1);
    }, 1000);
}

function updateCounterDisplay(value) {
    const binary = value.toString(2).padStart(4, '0');
    const bits = binary.split('');
    
    document.getElementById('bit3').textContent = bits[0];
    document.getElementById('bit2').textContent = bits[1];
    document.getElementById('bit1').textContent = bits[2];
    document.getElementById('bit0').textContent = bits[3];
    
    // Animate bit changes
    animateBitChange('bit3', bits[0]);
    animateBitChange('bit2', bits[1]);
    animateBitChange('bit1', bits[2]);
    animateBitChange('bit0', bits[3]);
}

function animateBitChange(bitId, value) {
    const bitElement = document.getElementById(bitId);
    if (bitElement) {
        // Add animation class
        bitElement.classList.add('bit-changing');
        
        // Update color based on value
        if (value === '1') {
            bitElement.style.background = '#4facfe';
            bitElement.style.boxShadow = '0 0 15px rgba(79, 172, 254, 0.6)';
        } else {
            bitElement.style.background = '#6c757d';
            bitElement.style.boxShadow = 'none';
        }
        
        // Remove animation class after animation
        setTimeout(() => {
            bitElement.classList.remove('bit-changing');
        }, 300);
    }
}

// SpO2 Animation Functions
function initializeSpO2Animation() {
    let spo2Value = 95;
    const spo2Display = document.getElementById('spo2-value');
    
    if (spo2Display) {
        setInterval(() => {
            // Simulate realistic SpO2 values (95-100%)
            spo2Value = 95 + Math.floor(Math.random() * 6);
            spo2Display.textContent = spo2Value;
            
            // Add pulse animation
            spo2Display.style.transform = 'scale(1.1)';
            setTimeout(() => {
                spo2Display.style.transform = 'scale(1)';
            }, 200);
        }, 2000);
    }
}

// Animation Functions
function startAnimations() {
    // Animate logic gates on title slide
    animateLogicGates();
    
    // Animate ECG waveform
    animateECGWaveform();
    
    // Add CSS animations
    addCSSAnimations();
}

function animateLogicGates() {
    const gateItems = document.querySelectorAll('.gate-item');
    gateItems.forEach((gate, index) => {
        gsap.fromTo(gate, 
            { opacity: 0, y: 50 },
            { 
                opacity: 1, 
                y: 0, 
                duration: 0.8, 
                delay: index * 0.2,
                ease: "back.out(1.7)"
            }
        );
    });
}

function animateECGWaveform() {
    // ECG animation is handled by CSS animations in the SVG
    const ecgPaths = document.querySelectorAll('.ecg-waveform path');
    ecgPaths.forEach(path => {
        // Add heartbeat effect to the container
        const container = path.closest('.ecg-waveform');
        if (container) {
            setInterval(() => {
                container.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    container.style.transform = 'scale(1)';
                }, 100);
            }, 1000);
        }
    });
}

function addCSSAnimations() {
    // Add CSS for bit changing animation
    const style = document.createElement('style');
    style.textContent = `
        .bit-changing {
            animation: bitPulse 0.3s ease-in-out;
        }
        
        @keyframes bitPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .gate-item {
            animation: gateGlow 3s ease-in-out infinite alternate;
        }
        
        @keyframes gateGlow {
            0% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.2); }
            100% { box-shadow: 0 0 20px rgba(79, 172, 254, 0.4); }
        }
        
        .led {
            animation: ledBlink 2s ease-in-out infinite;
        }
        
        @keyframes ledBlink {
            0%, 50%, 100% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }
    `;
    document.head.appendChild(style);
}

// Utility Functions
function openCombinationalCircuits() {
    window.open('combinational-circuits-presentation.html', '_blank');
}

function openSequentialCircuits() {
    window.open('sequential-circuits-presentation.html', '_blank');
}

// Export functions for global access
window.toggleInput = toggleInput;
window.openCombinationalCircuits = openCombinationalCircuits;
window.openSequentialCircuits = openSequentialCircuits;
