// Electronics Fundamentals Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeFundamentals();
    setupModuleInteractions();
    loadProgress();
    setupAchievements();
});

// Initialize fundamentals page
function initializeFundamentals() {
    console.log('Electronics Fundamentals page loaded');
    
    // Initialize progress circles
    updateProgressCircles();
    
    // Setup timeline interactions
    setupTimelineInteractions();
    
    // Initialize component previews
    initializeComponentPreviews();
    
    // Setup resource cards
    setupResourceCards();
}

// Setup module interactions
function setupModuleInteractions() {
    const moduleCards = document.querySelectorAll('.module-card');
    
    moduleCards.forEach(card => {
        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.classList.add('hovered');
            animateModulePreview(this);
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('hovered');
        });
    });
}

// Setup timeline interactions
function setupTimelineInteractions() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    timelineItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const moduleNumber = index + 1;
            highlightTimelineItem(this);
            showModuleInfo(moduleNumber);
        });
    });
}

// Highlight timeline item
function highlightTimelineItem(item) {
    // Remove highlight from all items
    document.querySelectorAll('.timeline-item').forEach(i => {
        i.classList.remove('active');
    });
    
    // Add highlight to clicked item
    item.classList.add('active');
}

// Show module information
function showModuleInfo(moduleNumber) {
    const moduleInfo = {
        1: {
            title: "Basic Concepts",
            description: "Learn fundamental electrical quantities: current, voltage, resistance, and power.",
            duration: "45 minutes",
            topics: ["Electric Current", "Voltage", "Resistance", "Power & Energy"]
        },
        2: {
            title: "Ohm's Law",
            description: "Master the fundamental relationship between voltage, current, and resistance.",
            duration: "50 minutes",
            topics: ["Ohm's Law Formula", "V-I-R Relationships", "Power Calculations", "Problem Solving"]
        },
        3: {
            title: "Passive Components",
            description: "Explore resistors, capacitors, and inductors in detail.",
            duration: "60 minutes",
            topics: ["Resistor Types", "Capacitor Behavior", "Inductor Properties", "Component Selection"]
        },
        4: {
            title: "Circuit Analysis",
            description: "Analyze series, parallel, and complex circuit configurations.",
            duration: "70 minutes",
            topics: ["Series Circuits", "Parallel Circuits", "Kirchhoff's Laws", "Complex Analysis"]
        }
    };
    
    const info = moduleInfo[moduleNumber];
    if (info) {
        showModuleModal(info);
    }
}

// Show module modal
function showModuleModal(info) {
    const modal = document.createElement('div');
    modal.className = 'module-info-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${info.title}</h3>
                <button class="modal-close" onclick="closeModuleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p>${info.description}</p>
                <div class="modal-meta">
                    <span><i class="fas fa-clock"></i> ${info.duration}</span>
                </div>
                <h4>Topics Covered:</h4>
                <ul>
                    ${info.topics.map(topic => `<li><i class="fas fa-check"></i> ${topic}</li>`).join('')}
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="startModuleFromModal('${info.title.toLowerCase().replace(/[^a-z0-9]/g, '-')}')">
                    Start Module
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add modal styles if not present
    if (!document.querySelector('#modal-styles-fundamentals')) {
        const styles = document.createElement('style');
        styles.id = 'modal-styles-fundamentals';
        styles.textContent = `
            .module-info-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }
            
            .module-info-modal .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            }
            
            .module-info-modal .modal-header {
                padding: 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .module-info-modal .modal-body {
                padding: 20px;
            }
            
            .module-info-modal .modal-footer {
                padding: 20px;
                border-top: 1px solid #eee;
                text-align: center;
            }
            
            .modal-meta {
                margin: 15px 0;
                color: #667eea;
                font-weight: 600;
            }
            
            .module-info-modal ul {
                list-style: none;
                padding: 0;
            }
            
            .module-info-modal li {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 8px;
            }
            
            .module-info-modal i.fa-check {
                color: #4ecdc4;
            }
        `;
        document.head.appendChild(styles);
    }
}

// Close module modal
function closeModuleModal() {
    const modal = document.querySelector('.module-info-modal');
    if (modal) {
        modal.remove();
    }
}

// Start module from modal
function startModuleFromModal(moduleId) {
    closeModuleModal();
    startModule(moduleId);
}

// Animate module preview
function animateModulePreview(card) {
    const preview = card.querySelector('.preview-simulation');
    if (!preview) return;
    
    const moduleType = card.dataset.module;
    
    switch(moduleType) {
        case 'basic-concepts':
            animateCurrentFlow(preview);
            break;
        case 'ohms-law':
            animateOhmsLaw(preview);
            break;
        case 'passive-components':
            animateComponents(preview);
            break;
        case 'circuit-analysis':
            animateCircuit(preview);
            break;
    }
}

// Animate current flow
function animateCurrentFlow(container) {
    const electrons = container.querySelectorAll('.electron-particle');
    electrons.forEach((electron, index) => {
        electron.style.animationDuration = '1.5s';
        electron.style.animationDelay = `${index * 0.5}s`;
    });
}

// Animate Ohm's law triangle
function animateOhmsLaw(container) {
    const triangle = container.querySelector('.formula-triangle');
    if (triangle) {
        triangle.style.animation = 'pulse 2s infinite';
    }
}

// Animate components
function animateComponents(container) {
    const components = container.querySelectorAll('.component-item');
    components.forEach((component, index) => {
        setTimeout(() => {
            component.style.transform = 'scale(1.1)';
            setTimeout(() => {
                component.style.transform = 'scale(1)';
            }, 300);
        }, index * 200);
    });
}

// Animate circuit
function animateCircuit(container) {
    const indicator = container.querySelector('.current-indicator');
    if (indicator) {
        indicator.style.animation = 'pulse 0.5s infinite';
    }
}

// Initialize component previews
function initializeComponentPreviews() {
    // Resistor color code animation
    const resistorBodies = document.querySelectorAll('.resistor-body');
    resistorBodies.forEach(resistor => {
        resistor.addEventListener('click', function() {
            animateColorBands(this);
        });
    });
    
    // Capacitor charging animation
    const capacitors = document.querySelectorAll('.capacitor-plates');
    capacitors.forEach(capacitor => {
        capacitor.addEventListener('click', function() {
            animateCapacitorCharging(this);
        });
    });
    
    // Inductor magnetic field animation
    const inductors = document.querySelectorAll('.inductor-coil');
    inductors.forEach(inductor => {
        inductor.addEventListener('click', function() {
            animateInductorField(this);
        });
    });
}

// Animate resistor color bands
function animateColorBands(resistor) {
    const bands = resistor.querySelectorAll('.color-band');
    bands.forEach((band, index) => {
        setTimeout(() => {
            band.style.transform = 'scaleY(1.2)';
            setTimeout(() => {
                band.style.transform = 'scaleY(1)';
            }, 200);
        }, index * 100);
    });
}

// Animate capacitor charging
function animateCapacitorCharging(capacitor) {
    const plates = capacitor.querySelectorAll('.plate');
    plates.forEach(plate => {
        plate.style.background = '#4ecdc4';
        setTimeout(() => {
            plate.style.background = '#333';
        }, 1000);
    });
}

// Animate inductor magnetic field
function animateInductorField(inductor) {
    const turns = inductor.querySelectorAll('.coil-turn');
    turns.forEach((turn, index) => {
        setTimeout(() => {
            turn.style.borderColor = '#667eea';
            turn.style.boxShadow = '0 0 10px #667eea';
            setTimeout(() => {
                turn.style.borderColor = '#333';
                turn.style.boxShadow = 'none';
            }, 500);
        }, index * 100);
    });
}

// Setup resource cards
function setupResourceCards() {
    const resourceCards = document.querySelectorAll('.resource-card');
    
    resourceCards.forEach(card => {
        const button = card.querySelector('.btn');
        button.addEventListener('click', function() {
            const cardTitle = card.querySelector('h3').textContent;
            handleResourceClick(cardTitle);
        });
    });
}

// Handle resource card clicks
function handleResourceClick(resourceType) {
    switch(resourceType) {
        case 'Reference Materials':
            openReferenceLibrary();
            break;
        case 'Circuit Calculator':
            openCircuitCalculator();
            break;
        case 'Virtual Lab':
            openVirtualLab();
            break;
        case 'Practice Problems':
            openPracticeProblems();
            break;
    }
}

// Open reference library
function openReferenceLibrary() {
    showComingSoonModal('Reference Library', 'Comprehensive reference materials and formula sheets will be available soon.');
}

// Open circuit calculator
function openCircuitCalculator() {
    showComingSoonModal('Circuit Calculator', 'Interactive circuit calculation tools are being developed.');
}

// Open virtual lab
function openVirtualLab() {
    window.open('../../simulator.html', '_blank');
}

// Open practice problems
function openPracticeProblems() {
    showComingSoonModal('Practice Problems', 'Additional practice exercises will be available soon.');
}

// Module navigation functions
function startModule(moduleId) {
    const moduleUrls = {
        'basic-concepts': 'basic-concepts-new.html',
        'basic-concepts-new': 'basic-concepts-new.html',
        'ohms-law': 'ohms-law.html',
        'passive-components': 'passive-components.html',
        'circuit-analysis': 'circuit-analysis.html'
    };

    const url = moduleUrls[moduleId];
    if (url) {
        // For Ohm's Law, we know it exists
        if (moduleId === 'ohms-law') {
            window.location.href = url;
            return;
        }

        // Check if file exists for other modules
        fetch(url, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    window.location.href = url;
                } else {
                    showComingSoonModal('Module', `The ${moduleId} module is currently under development.`);
                }
            })
            .catch(() => {
                showComingSoonModal('Module', `The ${moduleId} module is currently under development.`);
            });
    } else {
        showComingSoonModal('Module', `The ${moduleId} module is currently under development.`);
    }
}

function previewModule(moduleId) {
    const moduleNumber = {
        'basic-concepts': 1,
        'ohms-law': 2,
        'passive-components': 3,
        'circuit-analysis': 4
    }[moduleId];
    
    if (moduleNumber) {
        showModuleInfo(moduleNumber);
    }
}

// Progress management
function loadProgress() {
    const savedProgress = localStorage.getItem('fundamentalsProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {
        'basic-concepts': 0,
        'ohms-law': 0,
        'passive-components': 0,
        'circuit-analysis': 0
    };
    
    updateProgressDisplay(progress);
}

function updateProgressDisplay(progress) {
    // Update individual module progress
    Object.keys(progress).forEach(moduleId => {
        const moduleCard = document.querySelector(`[data-module="${moduleId}"]`);
        if (moduleCard) {
            const progressCircle = moduleCard.querySelector('.progress-circle');
            const progressText = moduleCard.querySelector('.progress-circle span');
            
            if (progressCircle && progressText) {
                const percentage = progress[moduleId];
                updateProgressCircle(progressCircle, percentage);
                progressText.textContent = `${percentage}%`;
            }
        }
    });
    
    // Update overall progress
    const overallProgress = Math.round(Object.values(progress).reduce((a, b) => a + b, 0) / 4);
    const overallProgressBar = document.getElementById('overallProgress');
    const overallProgressText = document.querySelector('.progress-text');
    
    if (overallProgressBar) {
        overallProgressBar.style.width = `${overallProgress}%`;
    }
    
    if (overallProgressText) {
        overallProgressText.textContent = `Overall Progress: ${overallProgress}%`;
    }
}

function updateProgressCircle(circle, percentage) {
    const degrees = (percentage / 100) * 360;
    circle.style.background = `conic-gradient(#667eea ${degrees}deg, #e9ecef ${degrees}deg)`;
}

function updateProgressCircles() {
    const circles = document.querySelectorAll('.progress-circle');
    circles.forEach(circle => {
        const progress = parseInt(circle.dataset.progress) || 0;
        updateProgressCircle(circle, progress);
    });
}

// Achievement system
function setupAchievements() {
    checkAchievements();
}

function checkAchievements() {
    const savedProgress = localStorage.getItem('fundamentalsProgress');
    const progress = savedProgress ? JSON.parse(savedProgress) : {};
    
    // Check for achievements
    const achievements = {
        'first-module': Object.values(progress).some(p => p > 0),
        'ohms-master': progress['ohms-law'] >= 80,
        'component-expert': progress['passive-components'] >= 80,
        'circuit-analyst': progress['circuit-analysis'] >= 80,
        'fundamentals-graduate': Object.values(progress).every(p => p >= 80)
    };
    
    // Update achievement badges
    Object.keys(achievements).forEach(achievementId => {
        const badge = document.querySelector(`[data-achievement="${achievementId}"]`);
        if (badge && achievements[achievementId]) {
            badge.classList.remove('locked');
            badge.classList.add('unlocked');
        }
    });
}

// Utility functions
function showComingSoonModal(title, message) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-construction"></i> ${title} Coming Soon</h3>
                <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
            </div>
            <div class="modal-body">
                <p>${message}</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="this.closest('.modal-overlay').remove()">OK</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Export functions for global access
window.startModule = startModule;
window.previewModule = previewModule;
window.closeModuleModal = closeModuleModal;
