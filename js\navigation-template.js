// Universal Navigation Template for Virtual Electronics Lab
// This file provides a consistent navigation header that can be included in any page

class UniversalNavigation {
    constructor(options = {}) {
        this.currentPage = options.currentPage || this.getCurrentPage();
        this.showBreadcrumbs = options.showBreadcrumbs !== false;
        this.showBackButton = options.showBackButton !== false;
        this.customTitle = options.customTitle || null;
        this.init();
    }

    getCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return filename.replace('.html', '');
    }

    init() {
        this.injectNavigationHTML();
        this.setupEventListeners();
        this.updateBreadcrumbs();
    }

    injectNavigationHTML() {
        const navHTML = `
            <header class="universal-header">
                <nav class="universal-navbar">
                    <div class="nav-container">
                        <div class="nav-left">
                            ${this.showBackButton ? `
                                <button class="back-btn" onclick="navigateBack()">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back</span>
                                </button>
                            ` : ''}
                            <div class="nav-logo">
                                <a href="../../index.html">
                                    <i class="fas fa-microchip"></i>
                                    <span>Virtual Electronics Lab</span>
                                </a>
                            </div>
                        </div>
                        
                        <div class="nav-center">
                            ${this.customTitle ? `<h1 class="page-title">${this.customTitle}</h1>` : ''}
                            ${this.showBreadcrumbs ? '<div class="breadcrumbs"></div>' : ''}
                        </div>
                        
                        <div class="nav-right">
                            <div class="nav-actions">
                                <button class="search-btn" onclick="toggleSearch()" data-tooltip="Search">
                                    <i class="fas fa-search"></i>
                                </button>
                                <button class="home-btn" onclick="navigateToPage('../../index.html')" data-tooltip="Home">
                                    <i class="fas fa-home"></i>
                                </button>
                                <button class="simulator-btn" onclick="navigateToPage('../../simulator.html')" data-tooltip="Simulator">
                                    <i class="fas fa-flask"></i>
                                </button>
                                <button class="theme-toggle" onclick="toggleTheme()" data-tooltip="Toggle Theme">
                                    <i class="fas fa-moon"></i>
                                </button>
                                <div class="hamburger" onclick="toggleMobileMenu()">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Mobile Menu -->
                <div class="mobile-menu">
                    <div class="mobile-menu-content">
                        <a href="../../index.html" class="mobile-menu-item">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <a href="../../simulator.html" class="mobile-menu-item">
                            <i class="fas fa-flask"></i>
                            <span>Simulator</span>
                        </a>
                        <div class="mobile-menu-item" onclick="toggleSearch()">
                            <i class="fas fa-search"></i>
                            <span>Search</span>
                        </div>
                        <div class="mobile-menu-item" onclick="toggleTheme()">
                            <i class="fas fa-moon"></i>
                            <span>Toggle Theme</span>
                        </div>
                    </div>
                </div>
                
                <!-- Search Overlay -->
                <div class="search-overlay" id="searchOverlay">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="Search courses, tools, or topics..." id="searchInput">
                        <button class="search-close" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="search-results" id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </header>
        `;

        // Insert navigation at the beginning of body
        document.body.insertAdjacentHTML('afterbegin', navHTML);
        
        // Add necessary styles
        this.injectStyles();
    }

    injectStyles() {
        const styles = `
            <style id="universal-nav-styles">
                .universal-header {
                    position: fixed;
                    top: 0;
                    width: 100%;
                    background: rgba(255, 255, 255, 0.95);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                    z-index: 1000;
                    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                }
                
                .universal-navbar {
                    padding: 15px 0;
                }
                
                .nav-container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 0 20px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                .nav-left {
                    display: flex;
                    align-items: center;
                    gap: 20px;
                }
                
                .back-btn {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    background: rgba(102, 126, 234, 0.1);
                    border: none;
                    padding: 8px 16px;
                    border-radius: 25px;
                    color: #667eea;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                
                .back-btn:hover {
                    background: rgba(102, 126, 234, 0.2);
                    transform: translateX(-2px);
                }
                
                .nav-logo a {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    text-decoration: none;
                    color: #667eea;
                    font-size: 1.2rem;
                    font-weight: 700;
                }
                
                .nav-logo i {
                    font-size: 1.5rem;
                }
                
                .nav-center {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 10px;
                }
                
                .page-title {
                    margin: 0;
                    font-size: 1.5rem;
                    color: #333;
                    font-weight: 600;
                }
                
                .breadcrumbs {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-size: 0.9rem;
                    color: #666;
                }
                
                .breadcrumb-item {
                    cursor: pointer;
                    transition: color 0.3s ease;
                }
                
                .breadcrumb-item:hover:not(.active) {
                    color: #667eea;
                }
                
                .breadcrumb-item.active {
                    color: #333;
                    font-weight: 600;
                }
                
                .breadcrumb-separator {
                    font-size: 0.7rem;
                    color: #999;
                }
                
                .nav-actions {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                
                .nav-actions button {
                    width: 40px;
                    height: 40px;
                    border: none;
                    border-radius: 50%;
                    background: rgba(102, 126, 234, 0.1);
                    color: #667eea;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;
                    position: relative;
                }
                
                .nav-actions button:hover {
                    background: rgba(102, 126, 234, 0.2);
                    transform: scale(1.1);
                }
                
                .mobile-menu {
                    display: none;
                    position: absolute;
                    top: 100%;
                    left: 0;
                    width: 100%;
                    background: white;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                    opacity: 0;
                    visibility: hidden;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                }
                
                .mobile-menu.active {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(0);
                }
                
                .mobile-menu-content {
                    padding: 20px;
                    display: flex;
                    flex-direction: column;
                    gap: 15px;
                }
                
                .mobile-menu-item {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 12px;
                    border-radius: 10px;
                    text-decoration: none;
                    color: #333;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                
                .mobile-menu-item:hover {
                    background: #f8f9fa;
                    color: #667eea;
                }
                
                .hamburger {
                    display: none;
                    flex-direction: column;
                    cursor: pointer;
                    gap: 3px;
                }
                
                .hamburger span {
                    width: 25px;
                    height: 3px;
                    background: #667eea;
                    transition: 0.3s;
                    border-radius: 2px;
                }
                
                .hamburger.active span:nth-child(1) {
                    transform: rotate(45deg) translate(6px, 6px);
                }
                
                .hamburger.active span:nth-child(2) {
                    opacity: 0;
                }
                
                .hamburger.active span:nth-child(3) {
                    transform: rotate(-45deg) translate(6px, -6px);
                }
                
                /* Responsive */
                @media (max-width: 768px) {
                    .hamburger {
                        display: flex;
                    }
                    
                    .nav-center {
                        display: none;
                    }
                    
                    .nav-actions button:not(.hamburger) {
                        display: none;
                    }
                    
                    .mobile-menu {
                        display: block;
                    }
                    
                    .page-title {
                        font-size: 1.2rem;
                    }
                }
                
                /* Add top padding to body to account for fixed header */
                body {
                    padding-top: 80px;
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }

    setupEventListeners() {
        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (hamburger && mobileMenu) {
            hamburger.addEventListener('click', () => {
                hamburger.classList.toggle('active');
                mobileMenu.classList.toggle('active');
            });
        }
        
        // Initialize tooltips
        this.initializeTooltips();
    }

    updateBreadcrumbs() {
        if (!this.showBreadcrumbs) return;
        
        const breadcrumbContainer = document.querySelector('.breadcrumbs');
        if (!breadcrumbContainer) return;

        const pathSegments = window.location.pathname.split('/').filter(segment => segment);
        const breadcrumbs = [{ name: 'Home', url: '../../index.html' }];
        
        // Build breadcrumb path
        let currentPath = '';
        pathSegments.forEach((segment, index) => {
            currentPath += '/' + segment;
            if (segment !== 'index.html' && !segment.includes('.html')) {
                const name = this.formatBreadcrumbName(segment);
                const isLast = index === pathSegments.length - 1;
                breadcrumbs.push({ 
                    name, 
                    url: isLast ? null : currentPath,
                    active: isLast 
                });
            }
        });

        this.renderBreadcrumbs(breadcrumbs, breadcrumbContainer);
    }

    formatBreadcrumbName(segment) {
        const nameMap = {
            'html': 'Content',
            'level1': 'Fundamentals',
            'level2': 'Semiconductors', 
            'level3': 'Medical Electronics',
            'level4': 'Advanced',
            'courses': 'Courses',
            'presentations': 'Presentations'
        };
        
        return nameMap[segment] || segment.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    renderBreadcrumbs(breadcrumbs, container) {
        const breadcrumbHTML = breadcrumbs.map((crumb, index) => {
            const isLast = index === breadcrumbs.length - 1;
            return `
                <span class="breadcrumb-item ${isLast ? 'active' : ''}" 
                      ${!isLast && crumb.url ? `onclick="navigateToPage('${crumb.url}')"` : ''}>
                    ${crumb.name}
                </span>
                ${!isLast ? '<i class="fas fa-chevron-right breadcrumb-separator"></i>' : ''}
            `;
        }).join('');
        
        container.innerHTML = breadcrumbHTML;
    }

    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function(e) {
                const tooltip = document.createElement('div');
                tooltip.className = 'nav-tooltip';
                tooltip.textContent = e.target.getAttribute('data-tooltip');
                tooltip.style.cssText = `
                    position: absolute;
                    background: #333;
                    color: white;
                    padding: 6px 10px;
                    border-radius: 4px;
                    font-size: 12px;
                    z-index: 10001;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    white-space: nowrap;
                `;
                
                document.body.appendChild(tooltip);
                
                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = rect.bottom + 10 + 'px';
                
                setTimeout(() => tooltip.style.opacity = '1', 10);
                
                e.target._tooltip = tooltip;
            });
            
            element.addEventListener('mouseleave', function(e) {
                if (e.target._tooltip) {
                    e.target._tooltip.style.opacity = '0';
                    setTimeout(() => {
                        if (e.target._tooltip && e.target._tooltip.parentNode) {
                            e.target._tooltip.parentNode.removeChild(e.target._tooltip);
                        }
                        e.target._tooltip = null;
                    }, 300);
                }
            });
        });
    }
}

// Global function to initialize universal navigation
function initializeUniversalNavigation(options = {}) {
    return new UniversalNavigation(options);
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UniversalNavigation, initializeUniversalNavigation };
}
