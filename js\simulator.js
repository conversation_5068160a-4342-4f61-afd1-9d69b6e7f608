// Virtual Circuit Simulator - Core Functionality
// معمل الإلكترونيات الافتراضي - الوظائف الأساسية

class CircuitSimulator {
    constructor() {
        this.components = new Map();
        this.connections = [];
        this.isRunning = false;
        this.simulationTime = 0;
        this.currentTool = 'select';
        this.zoomLevel = 1;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupCanvas();
        this.loadSavedCircuits();
    }

    setupEventListeners() {
        // Save/Load/Clear buttons
        document.getElementById('saveCircuit')?.addEventListener('click', () => this.saveCircuit());
        document.getElementById('loadCircuit')?.addEventListener('click', () => this.loadCircuit());
        document.getElementById('clearCircuit')?.addEventListener('click', () => this.clearCircuit());
        
        // Simulation controls
        document.getElementById('runSimulation')?.addEventListener('click', () => this.startSimulation());
        document.getElementById('pauseSimulation')?.addEventListener('click', () => this.pauseSimulation());
        document.getElementById('stopSimulation')?.addEventListener('click', () => this.stopSimulation());
    }

    setupCanvas() {
        const canvas = document.getElementById('circuitCanvas');
        if (!canvas) return;

        // Grid snapping
        this.gridSize = 20;
        
        // Canvas interactions
        canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showContextMenu(e);
        });
    }

    // Component Management
    addComponent(type, x, y) {
        const id = this.generateId();
        const component = {
            id,
            type,
            x: this.snapToGrid(x),
            y: this.snapToGrid(y),
            properties: this.getDefaultProperties(type),
            connections: []
        };
        
        this.components.set(id, component);
        this.renderComponent(component);
        this.updateComponentCount();
        
        return component;
    }

    getDefaultProperties(type) {
        const defaults = {
            'battery': { voltage: 12, internal_resistance: 0.1 },
            'resistor': { resistance: 1000, tolerance: 5 },
            'capacitor': { capacitance: 100e-6, voltage_rating: 25 },
            'inductor': { inductance: 1e-3, resistance: 0.1 },
            'led': { forward_voltage: 2.1, forward_current: 20e-3 },
            'diode': { forward_voltage: 0.7, reverse_voltage: 50 },
            'transistor': { beta: 100, vbe: 0.7, type: 'npn' },
            'voltmeter': { range: 20, impedance: 1e6 },
            'ammeter': { range: 1, resistance: 0.01 },
            'oscilloscope': { channels: 2, bandwidth: 100e6 }
        };
        
        return defaults[type] || {};
    }

    renderComponent(component) {
        const canvas = document.getElementById('circuitCanvas');
        const element = document.createElement('div');
        element.className = `circuit-component ${component.type}`;
        element.style.left = `${component.x}px`;
        element.style.top = `${component.y}px`;
        element.dataset.id = component.id;
        element.innerHTML = this.getComponentSVG(component.type);
        
        // Add interaction handlers
        this.makeComponentInteractive(element, component);
        
        canvas.appendChild(element);
    }

    getComponentSVG(type) {
        const svgMap = {
            'battery': `
                <svg width="50" height="30" viewBox="0 0 50 30">
                    <line x1="5" y1="15" x2="15" y2="15" stroke="#333" stroke-width="2"/>
                    <line x1="15" y1="8" x2="15" y2="22" stroke="#333" stroke-width="3"/>
                    <line x1="20" y1="5" x2="20" y2="25" stroke="#333" stroke-width="2"/>
                    <line x1="35" y1="15" x2="45" y2="15" stroke="#333" stroke-width="2"/>
                    <text x="25" y="12" font-size="8" fill="#666">+</text>
                </svg>
            `,
            'resistor': `
                <svg width="50" height="20" viewBox="0 0 50 20">
                    <path d="M5,10 L10,10 L12,5 L16,15 L20,5 L24,15 L28,5 L32,15 L36,5 L40,10 L45,10" 
                          stroke="#8B4513" stroke-width="2" fill="none"/>
                    <rect x="10" y="7" width="30" height="6" fill="#D2691E" stroke="#8B4513"/>
                </svg>
            `,
            'capacitor': `
                <svg width="40" height="30" viewBox="0 0 40 30">
                    <line x1="5" y1="15" x2="17" y2="15" stroke="#333" stroke-width="2"/>
                    <line x1="17" y1="8" x2="17" y2="22" stroke="#333" stroke-width="3"/>
                    <line x1="23" y1="8" x2="23" y2="22" stroke="#333" stroke-width="3"/>
                    <line x1="23" y1="15" x2="35" y2="15" stroke="#333" stroke-width="2"/>
                </svg>
            `,
            'led': `
                <svg width="40" height="30" viewBox="0 0 40 30">
                    <polygon points="15,8 15,22 25,15" fill="#FF4444" stroke="#333" stroke-width="1"/>
                    <line x1="25" y1="8" x2="25" y2="22" stroke="#333" stroke-width="2"/>
                    <line x1="5" y1="15" x2="15" y2="15" stroke="#333" stroke-width="2"/>
                    <line x1="25" y1="15" x2="35" y2="15" stroke="#333" stroke-width="2"/>
                    <path d="M28,8 L32,4 M30,8 L34,4" stroke="#FFD700" stroke-width="1"/>
                </svg>
            `,
            'voltmeter': `
                <svg width="40" height="40" viewBox="0 0 40 40">
                    <circle cx="20" cy="20" r="15" fill="#F0F0F0" stroke="#333" stroke-width="2"/>
                    <text x="20" y="25" text-anchor="middle" font-size="12" fill="#333">V</text>
                    <line x1="5" y1="20" x2="35" y2="20" stroke="#333" stroke-width="1"/>
                </svg>
            `
        };
        
        return svgMap[type] || `<div class="generic-component">${type}</div>`;
    }

    makeComponentInteractive(element, component) {
        // Double-click for properties
        element.addEventListener('dblclick', () => {
            this.openComponentProperties(component);
        });
        
        // Drag functionality
        let isDragging = false;
        let startPos = { x: 0, y: 0 };
        
        element.addEventListener('mousedown', (e) => {
            if (this.currentTool === 'select') {
                isDragging = true;
                startPos = { x: e.clientX, y: e.clientY };
                element.style.zIndex = '1000';
                e.preventDefault();
            }
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - startPos.x;
                const deltaY = e.clientY - startPos.y;
                
                component.x = this.snapToGrid(component.x + deltaX);
                component.y = this.snapToGrid(component.y + deltaY);
                
                element.style.left = `${component.x}px`;
                element.style.top = `${component.y}px`;
                
                startPos = { x: e.clientX, y: e.clientY };
            }
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                element.style.zIndex = '';
            }
        });
    }

    // Simulation Engine
    startSimulation() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.simulationTime = 0;
        
        // Update UI
        document.getElementById('runSimulation').disabled = true;
        document.getElementById('pauseSimulation').disabled = false;
        document.getElementById('stopSimulation').disabled = false;
        document.getElementById('simulationState').textContent = 'Running';
        
        // Start simulation loop
        this.simulationInterval = setInterval(() => {
            this.updateSimulation();
        }, 50); // 20 FPS
        
        this.showNotification('Simulation started', 'success');
    }

    updateSimulation() {
        this.simulationTime += 0.05;
        document.getElementById('simulationTime').textContent = `${this.simulationTime.toFixed(2)}s`;
        
        // Perform circuit analysis
        this.analyzeCircuit();
        
        // Update component animations
        this.updateComponentAnimations();
    }

    analyzeCircuit() {
        // Simple circuit analysis for demonstration
        const analysis = {
            totalPower: 0,
            totalCurrent: 0,
            nodeVoltages: new Map()
        };
        
        // Calculate basic values
        this.components.forEach(component => {
            if (component.type === 'battery') {
                analysis.totalCurrent += component.properties.voltage / 1000; // Simplified
            }
        });
        
        // Update analysis display
        this.updateAnalysisDisplay(analysis);
    }

    updateAnalysisDisplay(analysis) {
        const display = document.getElementById('analysisDisplay');
        if (display) {
            display.innerHTML = `
                <div class="analysis-item">
                    <span>Total Current:</span>
                    <span>${(analysis.totalCurrent * 1000).toFixed(2)} mA</span>
                </div>
                <div class="analysis-item">
                    <span>Total Power:</span>
                    <span>${analysis.totalPower.toFixed(2)} W</span>
                </div>
            `;
        }
    }

    pauseSimulation() {
        if (!this.isRunning) return;
        
        clearInterval(this.simulationInterval);
        this.isRunning = false;
        
        // Update UI
        document.getElementById('runSimulation').disabled = false;
        document.getElementById('pauseSimulation').disabled = true;
        document.getElementById('simulationState').textContent = 'Paused';
        
        this.showNotification('Simulation paused', 'info');
    }

    stopSimulation() {
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
        }
        
        this.isRunning = false;
        this.simulationTime = 0;
        
        // Update UI
        document.getElementById('runSimulation').disabled = false;
        document.getElementById('pauseSimulation').disabled = true;
        document.getElementById('stopSimulation').disabled = true;
        document.getElementById('simulationState').textContent = 'Stopped';
        document.getElementById('simulationTime').textContent = '0.00s';
        
        // Clear analysis
        document.getElementById('analysisDisplay').innerHTML = '<p class="no-analysis">Run simulation to see analysis</p>';
        
        this.showNotification('Simulation stopped', 'info');
    }

    // Utility Functions
    snapToGrid(value) {
        return Math.round(value / this.gridSize) * this.gridSize;
    }

    generateId() {
        return 'comp_' + Math.random().toString(36).substr(2, 9);
    }

    updateComponentCount() {
        const count = this.components.size;
        document.getElementById('componentCount').textContent = count;
        
        // Show/hide welcome message
        const welcomeMessage = document.getElementById('welcomeMessage');
        if (welcomeMessage) {
            welcomeMessage.style.display = count > 0 ? 'none' : 'flex';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            border-radius: 4px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Save/Load functionality
    saveCircuit() {
        const circuitData = {
            components: Array.from(this.components.values()),
            connections: this.connections,
            timestamp: new Date().toISOString()
        };
        
        const dataStr = JSON.stringify(circuitData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `circuit_${Date.now()}.json`;
        link.click();
        
        this.showNotification('Circuit saved successfully', 'success');
    }

    clearCircuit() {
        if (confirm('Are you sure you want to clear the entire circuit?')) {
            // Stop simulation if running
            this.stopSimulation();
            
            // Clear components
            this.components.clear();
            this.connections = [];
            
            // Clear canvas
            const canvas = document.getElementById('circuitCanvas');
            const components = canvas.querySelectorAll('.circuit-component');
            components.forEach(comp => comp.remove());
            
            // Update UI
            this.updateComponentCount();
            
            this.showNotification('Circuit cleared', 'info');
        }
    }
}

// Initialize simulator
let simulator;

function initializeSimulator() {
    simulator = new CircuitSimulator();
    console.log('🔬 Virtual Circuit Simulator initialized successfully!');
}

// Tutorial functions
function startTutorial() {
    document.getElementById('tutorialOverlay').style.display = 'none';
    localStorage.setItem('simulatorTutorialCompleted', 'true');
    
    // Start interactive tutorial
    showTutorialStep(1);
}

function skipTutorial() {
    document.getElementById('tutorialOverlay').style.display = 'none';
    localStorage.setItem('simulatorTutorialCompleted', 'true');
}

function showTutorialStep(step) {
    // Tutorial implementation would go here
    console.log(`Tutorial step ${step}`);
}

// Export for global access
window.CircuitSimulator = CircuitSimulator;
window.initializeSimulator = initializeSimulator;
