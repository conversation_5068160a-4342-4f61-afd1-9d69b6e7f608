/* Electronics Fundamentals Specific Styles */

/* Hero Section */
.fundamentals-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 20px 80px;
    margin-top: 80px;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.hero-text p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.stat-item i {
    font-size: 1.2rem;
    color: #4ecdc4;
}

/* Electron Flow Animation */
.hero-animation {
    display: flex;
    justify-content: center;
    align-items: center;
}

.electron-flow-demo {
    position: relative;
    width: 300px;
    height: 200px;
}

.conductor {
    width: 250px;
    height: 20px;
    background: linear-gradient(90deg, #ddd, #bbb);
    border-radius: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    overflow: hidden;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
}

.electron {
    width: 12px;
    height: 12px;
    background: radial-gradient(circle, #feca57, #ff9ff3);
    border-radius: 50%;
    position: absolute;
    top: 4px;
    animation: electronFlow 3s linear infinite;
    box-shadow: 0 0 10px #feca57;
}

@keyframes electronFlow {
    0% { left: -15px; }
    100% { left: 250px; }
}

.voltage-source {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    font-size: 1.5rem;
    font-weight: bold;
}

.voltage-source span:first-child {
    color: #ff6b6b;
}

.voltage-source span:last-child {
    color: #4ecdc4;
}

/* Learning Overview */
.learning-overview {
    padding: 80px 20px;
    background: #f8f9fa;
}

.learning-overview h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.journey-timeline {
    display: flex;
    justify-content: space-between;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.journey-timeline::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    z-index: 1;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-marker {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
}

.timeline-marker:hover {
    transform: scale(1.1);
}

.timeline-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.timeline-content p {
    font-size: 0.9rem;
    color: #666;
    max-width: 150px;
}

/* Module Cards */
.modules-section {
    padding: 80px 20px;
    background: white;
}

.modules-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.module-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 3rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.module-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.module-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    flex-shrink: 0;
}

.module-info {
    flex: 1;
}

.module-info h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.module-info p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.module-meta {
    display: flex;
    gap: 1.5rem;
}

.module-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 600;
}

.module-progress {
    position: relative;
}

.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(#667eea 0deg, #e9ecef 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 60px;
    height: 60px;
    background: white;
    border-radius: 50%;
    position: absolute;
}

.progress-circle span {
    position: relative;
    z-index: 1;
    font-weight: bold;
    color: #333;
}

.module-content {
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.module-description h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.module-description ul {
    list-style: none;
    padding: 0;
}

.module-description li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 0.8rem;
    color: #666;
}

.module-description i {
    color: #4ecdc4;
    font-size: 0.9rem;
}

.module-preview {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
}

.module-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

/* Preview Simulations */
.current-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.wire-segment {
    width: 200px;
    height: 15px;
    background: linear-gradient(90deg, #ddd, #bbb);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.flowing-electrons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.electron-particle {
    width: 8px;
    height: 8px;
    background: #feca57;
    border-radius: 50%;
    position: absolute;
    top: 3.5px;
    animation: electronFlow 2s linear infinite;
    box-shadow: 0 0 5px #feca57;
}

.electron-particle:nth-child(1) { animation-delay: 0s; }
.electron-particle:nth-child(2) { animation-delay: 0.7s; }
.electron-particle:nth-child(3) { animation-delay: 1.4s; }

.ohms-law-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.formula-triangle {
    width: 120px;
    height: 120px;
    position: relative;
    background: linear-gradient(135deg, #667eea, #764ba2);
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.triangle-section {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.triangle-section:hover {
    transform: scale(1.1);
}

.voltage { top: 20%; }
.current { bottom: 20%; left: 20%; }
.resistance { bottom: 20%; right: 20%; }

.components-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.component-showcase {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.component-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.component-item:hover {
    transform: scale(1.1);
}

.resistor-body {
    width: 40px;
    height: 15px;
    background: #f4e4bc;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;
}

.color-band {
    width: 4px;
    height: 100%;
    border-radius: 1px;
}

.red { background: #ff0000; }
.orange { background: #ffa500; }
.brown { background: #8b4513; }
.gold { background: #ffd700; }

.capacitor-plates {
    display: flex;
    gap: 3px;
    align-items: center;
}

.plate {
    width: 3px;
    height: 25px;
    background: #333;
    border-radius: 1px;
}

.inductor-coil {
    display: flex;
    gap: 2px;
}

.coil-turn {
    width: 12px;
    height: 12px;
    border: 2px solid #333;
    border-radius: 50%;
    border-bottom: transparent;
}

.circuit-demo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.circuit-diagram {
    width: 200px;
    height: 120px;
    position: relative;
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.circuit-element {
    position: absolute;
    border-radius: 5px;
}

.battery {
    width: 30px;
    height: 20px;
    background: linear-gradient(90deg, #ff6b6b 50%, #333 50%);
    left: 20px;
    top: 50px;
}

.resistor {
    width: 40px;
    height: 15px;
    background: #f4e4bc;
    border: 1px solid #ddd;
}

.r1 {
    right: 80px;
    top: 30px;
}

.r2 {
    right: 80px;
    bottom: 30px;
}

.circuit-wire {
    position: absolute;
    background: #333;
    height: 2px;
}

.wire1 {
    width: 50px;
    top: 60px;
    left: 50px;
}

.wire2 {
    width: 50px;
    bottom: 60px;
    left: 50px;
}

.current-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #ff6b6b;
    animation: pulse 1s infinite;
}

.demo-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 600;
}

/* Resources Section */
.resources-section {
    padding: 80px 20px;
    background: #f8f9fa;
}

.resources-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.resource-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
}

.resource-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.resource-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.resource-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

/* Achievements Section */
.achievements-section {
    padding: 80px 20px;
    background: white;
}

.achievements-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.achievement-badge {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.achievement-badge.locked {
    opacity: 0.6;
    filter: grayscale(100%);
}

.achievement-badge.unlocked {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9c4, #fff);
    transform: scale(1.05);
}

.achievement-badge i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.achievement-badge.unlocked i {
    color: #ffd700;
}

.achievement-badge h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.achievement-badge p {
    color: #666;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .journey-timeline {
        flex-direction: column;
        gap: 2rem;
    }
    
    .journey-timeline::before {
        display: none;
    }
    
    .module-content {
        grid-template-columns: 1fr;
    }
    
    .module-header {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .component-showcase {
        flex-direction: column;
        gap: 1rem;
    }
}
