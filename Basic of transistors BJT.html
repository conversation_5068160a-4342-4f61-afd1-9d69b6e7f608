<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أساسيات الترانزستور ثنائي القطبية (BJT)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Application Structure Plan: 
        Structure: A top navigation bar allowing users to switch between thematic sections. Each section corresponds to a key concept from the slides (e.g., "ما هو الترانزستور؟", "تركيب الترانزستور", etc.).
        Interactions:
            - Clickable elements to reveal more details (e.g., for BJT structure components).
            - Interactive simulation for amplification (slider for base current, display of collector current, simple chart).
            - Tabs/buttons for navigation between sections.
        User Flow: Users can navigate sequentially or jump to a specific section. This supports both linear learning and quick reference.
        Justification: This structure breaks down complex information into digestible chunks, suitable for an educational tool. Interactivity aims to engage the user and reinforce understanding, particularly for concepts like amplification.
    -->
    <!-- Visualization & Content Choices:
        - Home (Slide 1): Title, author. Goal: Introduction. Method: HTML text.
        - What is BJT? (Slide 2): Definitions. Goal: Inform. Method: HTML text.
        - Basic Structure (Slide 3): Emitter, Base, Collector. Goal: Inform/Organize. Method: HTML/CSS layout with descriptive text for each part. Interaction: Static display, clear visual separation. Justification: Clear presentation of structural components.
        - BJT Types (Slide 4): NPN vs PNP. Goal: Compare. Method: Side-by-side HTML layout with distinct visual cues for NPN and PNP, showing current flow textually. Justification: Clear comparison.
        - How it works (Amplification - Slide 5): Explanation, formula β = Ic / Ib. Goal: Explain/Interact. Method: HTML text, JS-powered slider for Ib, dynamic display of Ic (calculated using a fixed β), and a Chart.js line chart illustrating input vs. amplified output conceptually. Interaction: Slider, dynamic text/chart update. Justification: Makes amplification tangible. Library: Chart.js.
        - Uses (Slide 6): List with Unicode icons. Goal: Inform. Method: HTML list. Justification: Quick overview.
        - Comparison with FET (Slide 7): Table. Goal: Compare. Method: HTML table. Justification: Direct feature comparison.
        - Conclusion (Slide 8): Summary. Goal: Summarize. Method: HTML list.
        - Overall: Navigation via HTML buttons + JS.
    -->
    <style>
        body {
            font-family: 'Simplified Arabic', 'Tahoma', sans-serif;
            background-color: #f8fafc; /* bg-slate-50 */
        }
        .rtl {
            direction: rtl;
        }
        .ltr {
            direction: ltr;
        }
        .nav-button {
            @apply px-4 py-2 mx-1 my-1 text-sm font-medium text-white bg-sky-600 rounded-lg hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50 transition-colors;
        }
        .nav-button.active {
            @apply bg-sky-800 ring-2 ring-sky-500;
        }
        .content-section {
            @apply p-6 bg-white rounded-lg shadow-lg min-h-[400px];
        }
        .section-title {
            @apply text-2xl font-bold text-sky-700 mb-4 pb-2 border-b-2 border-sky-200;
        }
        .card {
            @apply bg-slate-100 p-4 rounded-lg shadow mb-3;
        }
        .highlight {
            @apply text-sky-600 font-semibold;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px; /* max-w-2xl */
            margin-left: auto;
            margin-right: auto;
            height: 300px; /* Tailwind: h-72 */
            max-height: 400px; /* Tailwind: max-h-96 */
            @apply sm:h-80 md:h-96;
        }
         /* Custom styles for Arabic text in Chart.js tooltips and labels if needed */
        .chartjs-tooltip {
            direction: rtl;
            font-family: 'Simplified Arabic', 'Tahoma', sans-serif !important;
        }
        .table th, .table td {
            @apply px-4 py-2 border border-slate-300 text-right;
        }
        .table th {
            @apply bg-sky-100 text-sky-700 font-semibold;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800 rtl">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <header class="text-center mb-8">
            <h1 class="text-4xl font-bold text-sky-700">الترانزستور ثنائي القطبية (BJT)</h1>
            <p class="text-lg text-slate-600">الأساسيات، الأنواع، والاستخدامات</p>
            <p class="text-sm text-slate-500 mt-1">أعد بواسطة: د. محمد يعقوب إسماعيل | جامعة السودان للعلوم والتكنولوجيا</p>
        </header>

        <nav id="navigation" class="mb-8 flex flex-wrap justify-center" aria-label="Main navigation">
            <button class="nav-button active" data-target="intro">ما هو الـ BJT؟</button>
            <button class="nav-button" data-target="structure">البنية الأساسية</button>
            <button class="nav-button" data-target="types">أنواع BJT</button>
            <button class="nav-button" data-target="howitworks">كيف يعمل؟ (التضخيم)</button>
            <button class="nav-button" data-target="uses">الاستخدامات</button>
            <button class="nav-button" data-target="comparison">مقارنة مع FET</button>
            <button class="nav-button" data-target="conclusion">الخلاصة</button>
        </nav>

        <main id="content-area">
            <section id="intro" class="content-section">
                <h2 class="section-title">ما هو الترانزستور ثنائي القطبية (BJT)؟</h2>
                <p class="mb-4">هذا القسم يقدم تعريفاً أساسياً للترانزستور ثنائي القطبية (BJT)، وهو عنصر إلكتروني حيوي في العديد من الدوائر. ستتعرف على طبيعته كمكون شبه موصل ثلاثي الطبقات ودوره الرئيسي في تضخيم الإشارات أو العمل كمفتاح إلكتروني.</p>
                <div class="card">
                    <h3 class="text-xl font-semibold text-sky-600 mb-2">تعريف ١:</h3>
                    <p>الترانزستور ثنائي القطبية (BJT) هو مكون شبه موصل يتألف من ثلاث طبقات من مادة شبه موصلة. هذه الطبقات تكون إما ترتيب N-P-N (طبقتان من النوع N تفصل بينهما طبقة رقيقة من النوع P) أو P-N-P (طبقتان من النوع P تفصل بينهما طبقة رقيقة من النوع N).</p>
                </div>
                <div class="card">
                    <h3 class="text-xl font-semibold text-sky-600 mb-2">تعريف ٢:</h3>
                    <p>يُستخدم الترانزستور BJT بشكل أساسي في الدوائر الإلكترونية إما <span class="highlight">لتضخيم الإشارات الكهربائية</span> (زيادة قوتها) أو <span class="highlight">كأداة تبديل إلكترونية</span> سريعة (لفتح وإغلاق الدوائر).</p>
                </div>
                <div class="mt-6 p-4 bg-sky-50 rounded-lg border border-sky-200">
                     <h4 class="font-semibold text-sky-700 mb-2">نظرة عامة على البنية:</h4>
                     <p class="text-sm text-slate-700">تتكون بنية BJT من ثلاث مناطق رئيسية: الباعث (Emitter)، القاعدة (Base)، والمجمع (Collector). التحكم في التيار الصغير المار عبر القاعدة يسمح بالتحكم في تيار أكبر بكثير يمر بين المجمع والباعث، وهذا هو أساس عمله كمضخم.</p>
                </div>
            </section>

            <section id="structure" class="content-section hidden">
                <h2 class="section-title">البنية الأساسية للـ BJT (3 طبقات)</h2>
                <p class="mb-4">هنا نستعرض الأجزاء الثلاثة الرئيسية التي تشكل الترانزستور BJT. فهم دور كل طبقة ضروري لفهم آلية عمل الترانزستور بشكل عام.</p>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="card text-center">
                        <h3 class="text-xl font-bold text-sky-700 mb-2">الباعث (Emitter)</h3>
                        <span class="text-4xl mb-2 block">📤</span>
                        <p>وظيفته الأساسية هي <span class="highlight">إصدار أو حقن حاملات الشحنة</span> (إلكترونات في NPN أو فجوات في PNP) إلى القاعدة. يتم تطعيمه بكثافة عالية لضمان توفر عدد كبير من حاملات الشحنة.</p>
                    </div>
                    <div class="card text-center">
                        <h3 class="text-xl font-bold text-sky-700 mb-2">القاعدة (Base)</h3>
                        <span class="text-4xl mb-2 block">⚖️</span>
                        <p>طبقة رقيقة جداً (عادةً بسماكة نانومترية) وذات تطعيم خفيف. دورها هو <span class="highlight">التحكم في تدفق حاملات الشحنة</span> من الباعث إلى المجمع. تيار صغير يمر عبر القاعدة يمكنه التحكم في تيار أكبر بكثير بين المجمع والباعث.</p>
                    </div>
                    <div class="card text-center">
                        <h3 class="text-xl font-bold text-sky-700 mb-2">المجمع (Collector)</h3>
                        <span class="text-4xl mb-2 block">📥</span>
                        <p>وظيفته هي <span class="highlight">تجميع حاملات الشحنة</span> التي عبرت من الباعث عبر القاعدة. تكون مساحة المجمع عادة أكبر من الباعث لتبديد الحرارة الناتجة عن مرور التيار.</p>
                    </div>
                </div>
                <div class="mt-6 card">
                    <h4 class="text-lg font-semibold text-sky-600 mb-2">توضيح مهم:</h4>
                    <p>سماكة طبقة القاعدة تكون أقل بكثير (<<) من سماكة كل من الباعث والمجمع. هذا التصميم بالغ الأهمية لعمل الترانزستور، حيث يسمح لمعظم حاملات الشحنة المحقونة من الباعث بالمرور عبر القاعدة الرقيقة والوصول إلى المجمع.</p>
                </div>
            </section>

            <section id="types" class="content-section hidden">
                <h2 class="section-title">نوعا الترانزستور BJT</h2>
                <p class="mb-4">يأتي الترانزستور BJT بنوعين رئيسيين: NPN و PNP. يعتمد كل نوع على ترتيب مختلف لطبقات المواد شبه الموصلة من النوع N (سالب) والنوع P (موجب)، مما يؤدي إلى اختلاف في اتجاه تدفق التيار وحاملات الشحنة الأغلبية.</p>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="card">
                        <h3 class="text-xl font-bold text-sky-700 mb-2 text-center">NPN</h3>
                        <div class="text-center mb-3">
                            <p class="text-5xl"><sub>N</sub> P <sub>N</sub></p>
                            <p class="text-sm text-slate-600">(إلكترونات هي حاملات الشحنة الأغلبية)</p>
                        </div>
                        <p class="mb-2">في ترانزستور NPN، تتكون الطبقات من مادة من النوع N (الباعث)، ثم طبقة رقيقة من النوع P (القاعدة)، ثم طبقة أخرى من النوع N (المجمع).</p>
                        <p class="font-semibold">اتجاه التيار الاصطلاحي:</p>
                        <p>يكون التيار الاصطلاحي (تدفق الشحنات الموجبة) من <span class="highlight">المجمع إلى الباعث</span>، عندما يتم تطبيق جهد موجب مناسب على القاعدة بالنسبة للباعث، وجهد موجب أكبر على المجمع بالنسبة للباعث.</p>
                        <p class="mt-2">رمز الدائرة:  <span class="font-mono text-lg ltr inline-block transform scale-x-[-1]">Q</span>  (السهم على الباعث يشير إلى الخارج)</p>
                    </div>
                    <div class="card">
                        <h3 class="text-xl font-bold text-sky-700 mb-2 text-center">PNP</h3>
                        <div class="text-center mb-3">
                             <p class="text-5xl"><sub>P</sub> N <sub>P</sub></p>
                             <p class="text-sm text-slate-600">(فجوات هي حاملات الشحنة الأغلبية)</p>
                        </div>
                        <p class="mb-2">في ترانزستور PNP، تتكون الطبقات من مادة من النوع P (الباعث)، ثم طبقة رقيقة من النوع N (القاعدة)، ثم طبقة أخرى من النوع P (المجمع).</p>
                        <p class="font-semibold">اتجاه التيار الاصطلاحي:</p>
                        <p>يكون التيار الاصطلاحي من <span class="highlight">الباعث إلى المجمع</span>، عندما يتم تطبيق جهد سالب مناسب على القاعدة بالنسبة للباعث، وجهد سالب أكبر على المجمع بالنسبة للباعث.</p>
                        <p class="mt-2">رمز الدائرة: <span class="font-mono text-lg ltr inline-block transform scale-x-[-1]">Q</span> (السهم على الباعث يشير إلى الداخل)</p>

                    </div>
                </div>
                 <p class="mt-4 text-sm text-slate-600 card">عادةً ما تكون ترانزستورات NPN أكثر شيوعًا في التطبيقات بسبب أن حركة الإلكترونات (حاملات الشحنة الأغلبية في NPN) أسرع من حركة الفجوات (حاملات الشحنة الأغلبية في PNP)، مما يوفر أداءً أفضل في الترددات العالية.</p>
            </section>

            <section id="howitworks" class="content-section hidden">
                <h2 class="section-title">كيف يعمل BJT؟ (وضع التضخيم)</h2>
                <p class="mb-4">أحد أهم وظائف الترانزستور BJT هو قدرته على تضخيم الإشارات. هذا يعني أن تيارًا صغيرًا يتم التحكم به عند طرف القاعدة يمكن أن ينتج عنه تيار أكبر بكثير يتدفق بين المجمع والباعث. هذا القسم يوضح هذه العملية بشكل مبسط وتفاعلي.</p>
                
                <div class="card mb-6">
                    <h3 class="text-lg font-semibold text-sky-600 mb-2">شرح مبدأ التضخيم:</h3>
                    <p class="mb-2">عندما يتم تطبيق تيار صغير ($I_b$) على قاعدة الترانزستور (في وضع التشغيل النشط)، فإنه يسمح بتدفق تيار أكبر بكثير ($I_c$) من المجمع إلى الباعث (في NPN) أو من الباعث إلى المجمع (في PNP).</p>
                    <p> العلاقة بين تيار المجمع ($I_c$) وتيار القاعدة ($I_b$) تُعرف بـ <span class="highlight">كسب التيار (Current Gain)</span>، ويرمز لها بالرمز $\beta$ (بيتا) أو $h_{FE}$.</p>
                    <p class="text-lg font-semibold my-3 text-center ltr"> $\beta = \frac{I_c}{I_b}$ </p>
                    <p> هذا يعني أن $I_c = \beta \times I_b$. إذا كانت قيمة $\beta$ كبيرة (مثلاً 100)، فإن تيار قاعدة صغير جداً يمكن أن ينتج تيار مجمع أكبر بـ 100 مرة.</p>
                </div>

                <div class="card">
                    <h3 class="text-lg font-semibold text-sky-600 mb-2">تجربة تفاعلية لمفهوم التضخيم:</h3>
                    <p class="mb-3 text-sm">حرك المؤشر أدناه لتغيير قيمة تيار القاعدة ($I_b$) الافتراضي. لاحظ كيف يتغير تيار المجمع ($I_c$) بناءً على قيمة $\beta$ ثابتة (نفترض $\beta = 100$ لهذه التجربة). الرسم البياني يوضح بشكل مبسط إشارة الدخل (متناسبة مع $I_b$) وإشارة الخرج المضخمة (متناسبة مع $I_c$).</p>
                    
                    <div class="mb-4 ltr">
                        <label for="baseCurrent" class="block mb-1 text-sm font-medium text-slate-700 rtl">تيار القاعدة ($I_b$): <span id="baseCurrentValue" class="font-bold text-sky-700">50</span> µA</label>
                        <input type="range" id="baseCurrent" min="1" max="100" value="50" class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer accent-sky-600">
                    </div>
                    <p class="mb-4 rtl">تيار المجمع المحسوب ($I_c$): <span id="collectorCurrentValue" class="font-bold text-sky-700">5</span> mA</p>
                    
                    <div class="chart-container">
                        <canvas id="amplificationChart"></canvas>
                    </div>
                </div>
            </section>

            <section id="uses" class="content-section hidden">
                <h2 class="section-title">الاستخدامات الرئيسية للـ BJT</h2>
                <p class="mb-6">بفضل قدرته على التضخيم والتبديل، يُستخدم الترانزستور BJT في مجموعة واسعة جداً من التطبيقات الإلكترونية. هذا القسم يسلط الضوء على بعض الاستخدامات الرئيسية.</p>
                <div class="grid md:grid-cols-3 gap-4">
                    <div class="card text-center p-6">
                        <span class="text-5xl mb-3 block">🔊</span>
                        <h3 class="text-xl font-semibold text-sky-700 mb-2">المضخمات (Amplifiers)</h3>
                        <p>يستخدم BJT لتضخيم الإشارات الصوتية، إشارات الراديو، وفي مراحل التضخيم المختلفة في الأجهزة الإلكترونية. على سبيل المثال، في أجهزة الراديو، التلفزيون، وأنظمة الصوت.</p>
                    </div>
                    <div class="card text-center p-6">
                        <span class="text-5xl mb-3 block">🔌</span>
                        <h3 class="text-xl font-semibold text-sky-700 mb-2">المفاتيح الإلكترونية (Switches)</h3>
                        <p>يمكن استخدام BJT كمفتاح إلكتروني يتم التحكم فيه بواسطة تيار صغير. يستخدم لتشغيل وإيقاف الأحمال مثل المصابيح، المحركات الصغيرة، والمرحلات (Relays).</p>
                    </div>
                    <div class="card text-center p-6">
                        <span class="text-5xl mb-3 block">🧮</span>
                        <h3 class="text-xl font-semibold text-sky-700 mb-2">دوائر المنطق الرقمي (Digital Logic)</h3>
                        <p>على الرغم من أن عائلات منطقية أخرى (مثل CMOS) هي الأكثر شيوعًا الآن، إلا أن BJT كان أساسيًا في بناء البوابات المنطقية الأولية (مثل AND, OR, NOT) في بدايات عصر الإلكترونيات الرقمية.</p>
                    </div>
                </div>
                <div class="mt-6 card">
                     <h4 class="text-lg font-semibold text-sky-600 mb-2">تطبيق محدد: أجهزة السمع الطبية</h4>
                     <p>تعتبر أجهزة السمع الطبية مثالاً ممتازاً على استخدام BJT. في هذه الأجهزة، تقوم ترانزستورات BJT بتضخيم الإشارات الصوتية الضعيفة الملتقطة بواسطة الميكروفون إلى مستوى يمكن للمستخدم سماعه بوضوح. هذا يوضح أهمية BJT في تحسين جودة الحياة للأفراد الذين يعانون من ضعف السمع.</p>
                </div>
            </section>

            <section id="comparison" class="content-section hidden">
                <h2 class="section-title">مقارنة بين BJT و FET</h2>
                <p class="mb-6">الترانزستور BJT ليس النوع الوحيد من الترانزستورات؛ فهناك عائلة أخرى مهمة هي ترانزستورات تأثير المجال (FETs). هذا الجدول يوضح بعض الفروقات الرئيسية بينهما، مما يساعد على فهم متى يكون استخدام كل نوع هو الأنسب.</p>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-slate-200 table">
                        <thead>
                            <tr>
                                <th>المعيار</th>
                                <th>الترانزستور ثنائي القطبية (BJT)</th>
                                <th>ترانزستور تأثير المجال (FET)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>نوع التحكم</td>
                                <td>يتم التحكم فيه بواسطة <span class="highlight">التيار</span> (Current-controlled device) - تيار القاعدة يتحكم في تيار المجمع.</td>
                                <td>يتم التحكم فيه بواسطة <span class="highlight">الجهد</span> (Voltage-controlled device) - جهد البوابة يتحكم في تيار المصرف.</td>
                            </tr>
                            <tr>
                                <td>حاملات الشحنة</td>
                                <td>ثنائي القطبية (Bipolar) - يشارك كل من <span class="highlight">الإلكترونات والفجوات</span> في عملية التوصيل.</td>
                                <td>أحادي القطبية (Unipolar) - يشارك <span class="highlight">إما الإلكترونات أو الفجوات</span> (وليس كلاهما) في التوصيل، حسب نوع FET (n-channel أو p-channel).</td>
                            </tr>
                            <tr>
                                <td>مقاومة الدخل</td>
                                <td><span class="highlight">منخفضة نسبياً</span> (بسبب وصلة القاعدة-الباعث المتحيزة أمامياً).</td>
                                <td><span class="highlight">عالية جداً</span> (بسبب عزل البوابة في MOSFETs أو وصلة البوابة-المصدر المتحيزة عكسياً في JFETs).</td>
                            </tr>
                            <tr>
                                <td>كسب التيار (β أو $h_{FE}$)</td>
                                <td>عادة ما يكون له كسب تيار محدد ومعروف.</td>
                                <td>المفهوم المماثل هو التوصيلية البينية (Transconductance, $g_m$).</td>
                            </tr>
                             <tr>
                                <td>الاستجابة الترددية</td>
                                <td>محدودة نسبياً، خاصة في الترددات العالية جداً (MHz). بعض الأنواع مصممة للترددات العالية.</td>
                                <td>يمكن أن يكون لها استجابة ترددية <span class="highlight">ممتازة</span>، خاصة MOSFETs، وتصل إلى نطاق GHz.</td>
                            </tr>
                            <tr>
                                <td>الضوضاء</td>
                                <td>أكثر عرضة للضوضاء مقارنة بـ FETs في بعض التطبيقات.</td>
                                <td>أقل ضوضاء، مما يجعلها مناسبة لتضخيم الإشارات الضعيفة.</td>
                            </tr>
                            <tr>
                                <td>استهلاك الطاقة</td>
                                <td>قد يكون أعلى بسبب تيار القاعدة المستمر في وضع التشغيل.</td>
                                <td>MOSFETs تستهلك طاقة منخفضة جداً في حالة السكون (لا يوجد تيار بوابة تقريباً).</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                 <div class="mt-6 card">
                    <h4 class="text-lg font-semibold text-sky-600 mb-2">تحليل موجز:</h4>
                    <p>بشكل عام، يُفضل استخدام <span class="highlight">BJT</span> في تطبيقات التضخيم ذات الترددات المنخفضة إلى المتوسطة حيث يكون كسب التيار العالي مطلوبًا وتكلفة المكون مهمة. بينما يُفضل استخدام <span class="highlight">FET</span> (خاصة MOSFET) في الدوائر التي تتطلب مقاومة دخل عالية جداً (مثل الحساسات)، استهلاك طاقة منخفض، وسرعات تبديل عالية أو تطبيقات الترددات العالية جداً (RF).</p>
                </div>
            </section>

            <section id="conclusion" class="content-section hidden">
                <h2 class="section-title">الخلاصة</h2>
                <p class="mb-6">لقد استعرضنا في هذا التطبيق التفاعلي المفاهيم الأساسية للترانزستور ثنائي القطبية (BJT). نأمل أن يكون هذا العرض قد ساعد في توضيح بنية هذا المكون الهام، أنواعه المختلفة، آلية عمله، وأبرز استخداماته.</p>
                <ul class="list-disc list-inside space-y-3">
                    <li><span class="highlight">مبدأ العمل الأساسي:</span> تيار صغير يتم التحكم به عند طرف القاعدة قادر على التحكم في تيار أكبر بكثير يتدفق بين المجمع والباعث. هذا هو جوهر قدرة BJT على التضخيم والتبديل.</li>
                    <li><span class="highlight">نوعا BJT:</span> النوعان الرئيسيان هما NPN (حيث تكون الإلكترونات هي حاملات الشحنة الأغلبية المتحركة) و PNP (حيث تكون الفجوات هي حاملات الشحنة الأغلبية). لكل نوع خصائصه واتجاهاته المميزة لتدفق التيار.</li>
                    <li><span class="highlight">البنية الثلاثية:</span> يتكون BJT من ثلاث طبقات متميزة: الباعث (مصدر حاملات الشحنة)، القاعدة (الطبقة الرقيقة المتحكمة)، والمجمع (جامع حاملات الشحنة).</li>
                    <li><span class="highlight">تطبيقات واسعة:</span> يستخدم BJT في عدد لا يحصى من التطبيقات، من أهمها المضخمات (الصوتية، RF)، المفاتيح الإلكترونية السريعة، وفي بعض الدوائر المنطقية. تشمل تطبيقاته مجالات متنوعة مثل الهندسة الطبية (مثل الأعصاب الاصطناعية وأجهزة السمع)، إلكترونيات السيارات، وأنظمة الاتصالات.</li>
                    <li><span class="highlight">مقارنة مع FET:</span> بينما BJT هو جهاز يتم التحكم فيه بالتيار وله مقاومة دخل منخفضة، فإن FET هو جهاز يتم التحكم فيه بالجهد وله مقاومة دخل عالية جداً، مما يجعل لكل منهما مجالات تطبيق مفضلة.</li>
                </ul>
                 <p class="mt-6 p-4 bg-sky-50 rounded-lg border border-sky-200 text-sm">يظل الترانزستور BJT، على الرغم من ظهور تقنيات أحدث، مكونًا أساسيًا في عالم الإلكترونيات بسبب موثوقيته، تكلفته المنخفضة نسبيًا، وقدراته الممتازة في العديد من سيناريوهات التضخيم والتبديل.</p>
            </section>
        </main>

        <footer class="text-center mt-12 py-4 border-t border-slate-300">
            <p class="text-sm text-slate-600">&copy; <span id="currentYear"></span> - تطبيق تفاعلي حول أساسيات BJT. مستوحى من مواد د. محمد يعقوب إسماعيل.</p>
             <p class="text-xs text-slate-400 mt-1">
                روابط مواد تعليمية خارجية: 
                <a href="https://www.edrawmax.com/templates/category/electrical-diagram-templates.html" target="_blank" rel="noopener noreferrer" class="text-sky-500 hover:text-sky-700">Edraw Gallery</a> | 
                <a href="https://www.tinkercad.com/circuits" target="_blank" rel="noopener noreferrer" class="text-sky-500 hover:text-sky-700">Tinkercad Circuits</a>
            </p>
        </footer>
    </div>

    <script>
        const navButtons = document.querySelectorAll('.nav-button');
        const contentSections = document.querySelectorAll('.content-section');
        let amplificationChartInstance = null;

        function displaySection(targetId) {
            contentSections.forEach(section => {
                section.classList.add('hidden');
                if (section.id === targetId) {
                    section.classList.remove('hidden');
                }
            });

            navButtons.forEach(button => {
                button.classList.remove('active');
                if (button.dataset.target === targetId) {
                    button.classList.add('active');
                }
            });
        }

        navButtons.forEach(button => {
            button.addEventListener('click', () => {
                displaySection(button.dataset.target);
            });
        });

        // Display the first section by default
        if (navButtons.length > 0) {
            displaySection(navButtons[0].dataset.target);
        }

        // Amplification Chart Logic
        const baseCurrentSlider = document.getElementById('baseCurrent');
        const baseCurrentValueDisplay = document.getElementById('baseCurrentValue');
        const collectorCurrentValueDisplay = document.getElementById('collectorCurrentValue');
        const beta = 100; // Fixed beta for this example

        function updateAmplificationExample() {
            const ib_uA = parseInt(baseCurrentSlider.value); // Base current in microamperes
            const ic_mA = (ib_uA * beta) / 1000; // Collector current in milliamperes

            baseCurrentValueDisplay.textContent = ib_uA;
            collectorCurrentValueDisplay.textContent = ic_mA.toFixed(2);

            // Update chart
            if (amplificationChartInstance) {
                // conceptual representation: input signal proportional to Ib, output to Ic
                const inputSignalAmplitude = ib_uA / 10; 
                const outputSignalAmplitude = ic_mA * 2; // Scale for visualization

                // Generate simple sine wave data for conceptual visualization
                const labels = Array.from({length: 20}, (_, i) => i);
                const inputSignalData = labels.map(x => Math.sin(x * 0.5) * inputSignalAmplitude);
                const outputSignalData = labels.map(x => Math.sin(x * 0.5) * outputSignalAmplitude);
                
                amplificationChartInstance.data.labels = labels;
                amplificationChartInstance.data.datasets[0].data = inputSignalData;
                amplificationChartInstance.data.datasets[1].data = outputSignalData;
                amplificationChartInstance.update();
            }
        }
        
        function initAmplificationChart() {
            const ctx = document.getElementById('amplificationChart').getContext('2d');
            amplificationChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [], // Will be populated by updateAmplificationExample
                    datasets: [
                        {
                            label: 'إشارة الدخل (متناسبة مع Ib)',
                            data: [],
                            borderColor: 'rgb(56, 189, 248)', // sky-500
                            tension: 0.1,
                            borderWidth: 2,
                        },
                        {
                            label: 'إشارة الخرج المضخمة (متناسبة مع Ic)',
                            data: [],
                            borderColor: 'rgb(239, 68, 68)', // red-500
                            tension: 0.1,
                            borderWidth: 2,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false, // Allow negative for sine wave
                            ticks: {
                                callback: function(value, index, values) { return parseFloat(value).toFixed(1); },
                                font: { family: "'Simplified Arabic', 'Tahoma', sans-serif" }
                            }
                        },
                        x: {
                           display: false // Hide x-axis labels for this conceptual graph
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                font: { family: "'Simplified Arabic', 'Tahoma', sans-serif" }
                            }
                        },
                        tooltip: {
                            enabled: true,
                            mode: 'index',
                            intersect: false,
                            bodyFont: { family: "'Simplified Arabic', 'Tahoma', sans-serif" },
                            titleFont: { family: "'Simplified Arabic', 'Tahoma', sans-serif" },
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toFixed(2);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
            updateAmplificationExample(); // Initial calculation and chart draw
        }

        if (baseCurrentSlider) {
            baseCurrentSlider.addEventListener('input', updateAmplificationExample);
            // Initialize chart when its section becomes visible or on page load if it's the default
            // For simplicity, initializing it here. Consider IntersectionObserver for lazy loading.
             if (document.getElementById('amplificationChart')) {
                initAmplificationChart();
            }
        }
        
        document.getElementById('currentYear').textContent = new Date().getFullYear();
    </script>
</body>
</html>
