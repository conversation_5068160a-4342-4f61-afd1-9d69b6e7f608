<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Passive Components - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/passive-components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="module-title">Passive Components</div>
            <div class="progress-indicator">
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
                <span class="progress-text">Progress: 0%</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="components-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Passive Electronic Components</h1>
                <p>Explore the fundamental building blocks of electronic circuits: resistors, capacitors, and inductors with practical biomedical applications.</p>
                <div class="hero-objectives">
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Understand resistor types, color codes, and applications</li>
                        <li><i class="fas fa-check"></i> Learn capacitor behavior in AC and DC circuits</li>
                        <li><i class="fas fa-check"></i> Explore inductor properties and magnetic fields</li>
                        <li><i class="fas fa-check"></i> Apply components in biomedical device design</li>
                        <li><i class="fas fa-check"></i> Calculate component values for specific applications</li>
                    </ul>
                </div>
            </div>
            <div class="hero-visual">
                <div class="components-showcase">
                    <div class="component-grid">
                        <div class="component-item resistor-item">
                            <div class="component-icon">
                                <i class="fas fa-minus"></i>
                            </div>
                            <h4>Resistors</h4>
                            <p>Control current flow</p>
                            <div class="component-value">1kΩ ±5%</div>
                        </div>
                        <div class="component-item capacitor-item">
                            <div class="component-icon">
                                <i class="fas fa-battery-half"></i>
                            </div>
                            <h4>Capacitors</h4>
                            <p>Store electrical energy</p>
                            <div class="component-value">100μF 25V</div>
                        </div>
                        <div class="component-item inductor-item">
                            <div class="component-icon">
                                <i class="fas fa-circle-notch"></i>
                            </div>
                            <h4>Inductors</h4>
                            <p>Store magnetic energy</p>
                            <div class="component-value">10mH ±10%</div>
                        </div>
                    </div>
                    <div class="circuit-example">
                        <h4>RC Filter Circuit</h4>
                        <div class="filter-demo">
                            <div class="input-signal">Input</div>
                            <div class="resistor-demo">R</div>
                            <div class="capacitor-demo">C</div>
                            <div class="output-signal">Output</div>
                            <div class="frequency-response">
                                <span>Low-pass filter</span>
                                <span>fc = 1/(2πRC)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Slide Presentation -->
    <section class="slide-presentation">
        <div class="container">
            <div class="presentation-header">
                <h2><i class="fas fa-microchip"></i> Component Deep Dive</h2>
                <div class="slide-controls">
                    <button type="button" class="slide-btn" id="prevSlide" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <div class="slide-counter">1 / 12</div>
                    <button type="button" class="slide-btn" id="nextSlide">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="slides-container">
                <!-- Slide 1: Resistors Introduction -->
                <div class="slide active" data-slide="1">
                    <div class="slide-content">
                        <h3><i class="fas fa-minus"></i> Resistors: The Current Controllers</h3>
                        <div class="slide-layout">
                            <div class="slide-text">
                                <p>Resistors are passive components that oppose the flow of electric current, following Ohm's Law (V = IR).</p>
                                <div class="resistor-types">
                                    <h4>Common Types:</h4>
                                    <ul>
                                        <li><strong>Carbon Film:</strong> General purpose, low cost</li>
                                        <li><strong>Metal Film:</strong> Higher precision, better stability</li>
                                        <li><strong>Wire Wound:</strong> High power applications</li>
                                        <li><strong>Surface Mount (SMD):</strong> Compact, automated assembly</li>
                                    </ul>
                                </div>
                                <div class="medical-application">
                                    <h4>Biomedical Applications:</h4>
                                    <p>In ECG machines, precision resistors set amplifier gain and input impedance. Current-limiting resistors protect sensitive biological tissues in electrotherapy devices.</p>
                                </div>
                            </div>
                            <div class="slide-visual">
                                <div class="resistor-demo">
                                    <h4>Color Code Calculator</h4>
                                    <div class="resistor-visual">
                                        <div class="resistor-body">
                                            <div class="color-band band1" data-color="brown"></div>
                                            <div class="color-band band2" data-color="black"></div>
                                            <div class="color-band band3" data-color="red"></div>
                                            <div class="color-band band4" data-color="gold"></div>
                                        </div>
                                        <div class="resistor-leads"></div>
                                    </div>
                                    <div class="color-calculation">
                                        <h5>Reading: Brown-Black-Red-Gold</h5>
                                        <p>1st digit: 1 (Brown)</p>
                                        <p>2nd digit: 0 (Black)</p>
                                        <p>Multiplier: ×100 (Red)</p>
                                        <p>Tolerance: ±5% (Gold)</p>
                                        <p class="result"><strong>Value: 1,000Ω ±5% (1kΩ)</strong></p>
                                    </div>
                                    <div class="interactive-selector">
                                        <button type="button" class="color-btn" onclick="changeResistorColor()">Try Different Colors</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: Capacitors Introduction -->
                <div class="slide" data-slide="2">
                    <div class="slide-content">
                        <h3><i class="fas fa-battery-half"></i> Capacitors: Energy Storage Elements</h3>
                        <div class="slide-layout">
                            <div class="slide-text">
                                <p>Capacitors store electrical energy in an electric field between two conductive plates separated by a dielectric material.</p>
                                <div class="capacitor-formula">
                                    <h4>Key Equations:</h4>
                                    <div class="formula">C = Q/V</div>
                                    <div class="formula">E = ½CV²</div>
                                    <div class="formula">I = C(dV/dt)</div>
                                </div>
                                <div class="capacitor-types">
                                    <h4>Common Types:</h4>
                                    <ul>
                                        <li><strong>Ceramic:</strong> Small, stable, high frequency</li>
                                        <li><strong>Electrolytic:</strong> High capacitance, polarized</li>
                                        <li><strong>Tantalum:</strong> Stable, reliable, compact</li>
                                        <li><strong>Film:</strong> Low loss, high voltage</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="slide-visual">
                                <div class="capacitor-demo">
                                    <h4>Capacitor Charging/Discharging</h4>
                                    <div class="capacitor-circuit">
                                        <div class="voltage-source-cap">5V</div>
                                        <div class="switch-demo" id="capSwitch">Switch</div>
                                        <div class="resistor-cap">1kΩ</div>
                                        <div class="capacitor-visual">
                                            <div class="cap-plate plate1"></div>
                                            <div class="cap-plate plate2"></div>
                                            <div class="cap-label">100μF</div>
                                        </div>
                                    </div>
                                    <div class="capacitor-graph">
                                        <h5>Voltage vs Time</h5>
                                        <div class="graph-container">
                                            <canvas id="capacitorGraph" width="300" height="150"></canvas>
                                        </div>
                                        <p>τ = RC = 1kΩ × 100μF = 0.1s</p>
                                    </div>
                                    <button type="button" class="demo-btn" onclick="toggleCapacitorDemo()">Start Charging</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Practical Example - ECG Filter -->
                <div class="slide" data-slide="3">
                    <div class="slide-content">
                        <h3><i class="fas fa-heartbeat"></i> Practical Example: ECG Signal Filtering</h3>
                        <div class="slide-layout">
                            <div class="slide-text">
                                <p>In ECG systems, passive components create filters to remove noise and artifacts while preserving the cardiac signal.</p>
                                <div class="problem-statement">
                                    <h4>Design Challenge:</h4>
                                    <p>Design a low-pass filter for an ECG amplifier that:</p>
                                    <ul>
                                        <li>Passes frequencies up to 100Hz (cardiac signals)</li>
                                        <li>Attenuates 60Hz power line interference</li>
                                        <li>Uses standard component values</li>
                                    </ul>
                                </div>
                                <div class="solution-steps">
                                    <h4>Solution Steps:</h4>
                                    <ol>
                                        <li>Choose cutoff frequency: fc = 100Hz</li>
                                        <li>Use formula: fc = 1/(2πRC)</li>
                                        <li>Select R = 1.6kΩ (standard value)</li>
                                        <li>Calculate C = 1/(2π × 1600 × 100) = 1μF</li>
                                        <li>Verify: fc = 1/(2π × 1600 × 1×10⁻⁶) = 99.5Hz ✓</li>
                                    </ol>
                                </div>
                            </div>
                            <div class="slide-visual">
                                <div class="filter-example">
                                    <h4>ECG Filter Circuit</h4>
                                    <div class="ecg-filter-circuit">
                                        <div class="ecg-input">ECG Signal<br>+ Noise</div>
                                        <div class="filter-resistor">R = 1.6kΩ</div>
                                        <div class="filter-capacitor">C = 1μF</div>
                                        <div class="ecg-output">Clean ECG<br>Signal</div>
                                    </div>
                                    <div class="frequency-response-graph">
                                        <h5>Frequency Response</h5>
                                        <div class="response-curve">
                                            <div class="passband">Passband<br>0-100Hz</div>
                                            <div class="transition">Transition</div>
                                            <div class="stopband">Stopband<br>>100Hz</div>
                                            <div class="power-line">60Hz Line<br>Interference</div>
                                        </div>
                                    </div>
                                    <div class="component-calculator">
                                        <h5>Interactive Calculator</h5>
                                        <div class="calc-inputs">
                                            <label>R (Ω): <input type="number" id="filterR" value="1600"></label>
                                            <label>C (μF): <input type="number" id="filterC" value="1" step="0.1"></label>
                                        </div>
                                        <div class="calc-result">
                                            <span>Cutoff Frequency: </span>
                                            <span id="cutoffResult">99.5 Hz</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Component Calculator Section -->
    <section class="component-calculator">
        <div class="container">
            <h2><i class="fas fa-calculator"></i> Component Value Calculator</h2>
            <div class="calculator-tabs">
                <button type="button" class="tab-btn active" data-tab="resistor">Resistor Color Code</button>
                <button type="button" class="tab-btn" data-tab="capacitor">Capacitor Calculator</button>
                <button type="button" class="tab-btn" data-tab="inductor">Inductor Calculator</button>
                <button type="button" class="tab-btn" data-tab="filter">Filter Design</button>
            </div>
            
            <div class="calculator-content">
                <!-- Resistor Calculator -->
                <div class="calc-panel active" id="resistor-calc">
                    <div class="calc-inputs">
                        <h3>Resistor Color Code Calculator</h3>
                        <div class="color-selector">
                            <div class="band-selector">
                                <label>1st Band:</label>
                                <select id="band1">
                                    <option value="0">Black (0)</option>
                                    <option value="1" selected>Brown (1)</option>
                                    <option value="2">Red (2)</option>
                                    <option value="3">Orange (3)</option>
                                    <option value="4">Yellow (4)</option>
                                    <option value="5">Green (5)</option>
                                    <option value="6">Blue (6)</option>
                                    <option value="7">Violet (7)</option>
                                    <option value="8">Gray (8)</option>
                                    <option value="9">White (9)</option>
                                </select>
                            </div>
                            <div class="band-selector">
                                <label>2nd Band:</label>
                                <select id="band2">
                                    <option value="0" selected>Black (0)</option>
                                    <option value="1">Brown (1)</option>
                                    <option value="2">Red (2)</option>
                                    <option value="3">Orange (3)</option>
                                    <option value="4">Yellow (4)</option>
                                    <option value="5">Green (5)</option>
                                    <option value="6">Blue (6)</option>
                                    <option value="7">Violet (7)</option>
                                    <option value="8">Gray (8)</option>
                                    <option value="9">White (9)</option>
                                </select>
                            </div>
                            <div class="band-selector">
                                <label>Multiplier:</label>
                                <select id="multiplier">
                                    <option value="1">Black (×1)</option>
                                    <option value="10">Brown (×10)</option>
                                    <option value="100" selected>Red (×100)</option>
                                    <option value="1000">Orange (×1K)</option>
                                    <option value="10000">Yellow (×10K)</option>
                                    <option value="100000">Green (×100K)</option>
                                    <option value="1000000">Blue (×1M)</option>
                                </select>
                            </div>
                            <div class="band-selector">
                                <label>Tolerance:</label>
                                <select id="tolerance">
                                    <option value="1">Brown (±1%)</option>
                                    <option value="2">Red (±2%)</option>
                                    <option value="5" selected>Gold (±5%)</option>
                                    <option value="10">Silver (±10%)</option>
                                </select>
                            </div>
                        </div>
                        <button type="button" class="calc-btn" onclick="calculateResistor()">Calculate Value</button>
                    </div>
                    <div class="calc-results">
                        <h3>Resistor Value</h3>
                        <div class="resistor-display">
                            <div class="resistor-visual-calc">
                                <div class="resistor-body-calc">
                                    <div class="color-band-calc band1-calc"></div>
                                    <div class="color-band-calc band2-calc"></div>
                                    <div class="color-band-calc band3-calc"></div>
                                    <div class="color-band-calc band4-calc"></div>
                                </div>
                            </div>
                        </div>
                        <div class="result-display">
                            <div class="result-item">
                                <span class="label">Nominal Value:</span>
                                <span class="value" id="resistorValue">1,000 Ω</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Tolerance:</span>
                                <span class="value" id="resistorTolerance">±5%</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Range:</span>
                                <span class="value" id="resistorRange">950 - 1,050 Ω</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../js/passive-components.js"></script>
</body>
</html>
