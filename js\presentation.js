// Electronics Fundamentals Presentation JavaScript

// Global variables
let currentSlideIndex = 0;
let totalSlides = 0;
let isPlaying = false;
let playInterval = null;
let animationFrameId = null;

// Simulation workbench variables
let canvas = null;
let ctx = null;
let components = [];
let connections = [];
let selectedTool = 'select';
let selectedComponent = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let currentView = 'schematic';

// Initialize presentation
document.addEventListener('DOMContentLoaded', function() {
    initializePresentation();
    setupEventListeners();
    initializeAnimations();
    setupSimulationWorkbench();
});

function initializePresentation() {
    const slides = document.querySelectorAll('.slide');
    totalSlides = slides.length;
    
    // Update slide counter
    document.getElementById('totalSlides').textContent = totalSlides;
    
    // Initialize first slide
    showSlide(0);
    
    // Setup slide thumbnails
    generateSlideThumbnails();
    
    console.log('Presentation initialized with', totalSlides, 'slides');
}

function setupEventListeners() {
    // Navigation controls
    document.getElementById('prevBtn').addEventListener('click', previousSlide);
    document.getElementById('nextBtn').addEventListener('click', nextSlide);
    document.getElementById('playBtn').addEventListener('click', toggleAutoPlay);
    
    // Fullscreen control
    document.getElementById('fullscreenBtn').addEventListener('click', toggleFullscreen);
    
    // Simulation modal
    document.getElementById('simulationBtn').addEventListener('click', openSimulation);
    document.getElementById('closeSimulation').addEventListener('click', closeSimulation);
    
    // Keyboard navigation
    document.addEventListener('keydown', handleKeyPress);
    
    // Ohm's Law triangle interaction
    setupOhmsLawInteraction();
    
    // Calculator functionality
    setupCalculator();
    
    // Component interactions
    setupComponentInteractions();
    
    // Voltage slider
    setupVoltageSlider();
}

function initializeAnimations() {
    // Initialize title canvas animation
    initializeTitleCanvas();
    
    // Setup GSAP animations for slide transitions
    gsap.registerPlugin();
    
    // Animate list items on slide change
    animateCurrentSlideElements();
}

function initializeTitleCanvas() {
    const canvas = document.getElementById('titleCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
    
    // Particle system for background
    const particles = [];
    const particleCount = 50;
    
    // Create particles
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            radius: Math.random() * 3 + 1,
            opacity: Math.random() * 0.5 + 0.2
        });
    }
    
    function animateParticles() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw connections
        ctx.strokeStyle = 'rgba(79, 172, 254, 0.1)';
        ctx.lineWidth = 1;
        
        for (let i = 0; i < particles.length; i++) {
            for (let j = i + 1; j < particles.length; j++) {
                const dx = particles[i].x - particles[j].x;
                const dy = particles[i].y - particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 100) {
                    ctx.beginPath();
                    ctx.moveTo(particles[i].x, particles[i].y);
                    ctx.lineTo(particles[j].x, particles[j].y);
                    ctx.stroke();
                }
            }
        }
        
        // Draw and update particles
        particles.forEach(particle => {
            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(79, 172, 254, ${particle.opacity})`;
            ctx.fill();
            
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Bounce off edges
            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
        });
        
        animationFrameId = requestAnimationFrame(animateParticles);
    }
    
    animateParticles();
}

function showSlide(index) {
    const slides = document.querySelectorAll('.slide');
    
    // Hide all slides
    slides.forEach((slide, i) => {
        slide.classList.remove('active', 'prev');
        if (i < index) {
            slide.classList.add('prev');
        }
    });
    
    // Show current slide
    if (slides[index]) {
        slides[index].classList.add('active');
        currentSlideIndex = index;
        
        // Update UI
        updateSlideCounter();
        updateProgressBar();
        updateNavigationButtons();
        
        // Animate slide elements
        animateCurrentSlideElements();
        
        // Handle slide-specific functionality
        handleSlideSpecificActions(index);
    }
}

function nextSlide() {
    if (currentSlideIndex < totalSlides - 1) {
        showSlide(currentSlideIndex + 1);
    }
}

function previousSlide() {
    if (currentSlideIndex > 0) {
        showSlide(currentSlideIndex - 1);
    }
}

function toggleAutoPlay() {
    const playBtn = document.getElementById('playBtn');
    const icon = playBtn.querySelector('i');
    
    if (isPlaying) {
        clearInterval(playInterval);
        isPlaying = false;
        icon.className = 'fas fa-play';
        playBtn.style.background = 'var(--success-gradient)';
    } else {
        playInterval = setInterval(() => {
            if (currentSlideIndex < totalSlides - 1) {
                nextSlide();
            } else {
                toggleAutoPlay(); // Stop at end
            }
        }, 5000);
        isPlaying = true;
        icon.className = 'fas fa-pause';
        playBtn.style.background = 'var(--danger-color)';
    }
}

function updateSlideCounter() {
    document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
}

function updateProgressBar() {
    const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
    document.getElementById('progressFill').style.width = `${progress}%`;
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    prevBtn.disabled = currentSlideIndex === 0;
    nextBtn.disabled = currentSlideIndex === totalSlides - 1;
}

function animateCurrentSlideElements() {
    const currentSlide = document.querySelector('.slide.active');
    if (!currentSlide) return;
    
    // Animate list items
    const listItems = currentSlide.querySelectorAll('.animated-list li');
    listItems.forEach((item, index) => {
        gsap.fromTo(item, 
            { opacity: 0, x: -30 },
            { 
                opacity: 1, 
                x: 0, 
                duration: 0.6, 
                delay: index * 0.2,
                ease: "power2.out"
            }
        );
    });
    
    // Animate component cards
    const componentCards = currentSlide.querySelectorAll('.component-card');
    componentCards.forEach((card, index) => {
        gsap.fromTo(card,
            { opacity: 0, y: 50, scale: 0.8 },
            {
                opacity: 1,
                y: 0,
                scale: 1,
                duration: 0.8,
                delay: index * 0.3,
                ease: "back.out(1.7)"
            }
        );
    });
}

function handleSlideSpecificActions(slideIndex) {
    switch(slideIndex) {
        case 0: // Title slide
            animateTitleIcons();
            break;
        case 2: // Basic concepts with water analogy
            animateWaterAnalogy();
            break;
        case 3: // Voltage demonstration
            initializeVoltageDemo();
            break;
        case 4: // Current flow
            animateCurrentFlow();
            break;
        case 5: // Ohm's law triangle
            initializeOhmsTriangle();
            break;
        case 6: // Calculator
            resetCalculator();
            break;
        case 7: // Passive components
            animateComponentShowcase();
            break;
        case 8: // Resistance deep dive
            initializeResistanceDemo();
            break;
        case 9: // Power calculations
            initializePowerCalculator();
            break;
        case 10: // Solved example
            animateSolutionSteps();
            break;
        case 11: // Resistor color code
            initializeResistorColorCode();
            break;
        case 12: // Capacitor fundamentals
            initializeCapacitorDemo();
            break;
        case 13: // Inductor fundamentals
            initializeInductorDemo();
            break;
    }
}

function animateTitleIcons() {
    const icons = document.querySelectorAll('.icon-item');
    icons.forEach((icon, index) => {
        gsap.fromTo(icon,
            { opacity: 0, y: 100, rotationY: 180 },
            {
                opacity: 1,
                y: 0,
                rotationY: 0,
                duration: 1,
                delay: 0.6 + index * 0.2,
                ease: "back.out(1.7)"
            }
        );
    });
}

function animateWaterAnalogy() {
    // Animate water level
    const waterLevel = document.getElementById('waterLevel');
    if (waterLevel) {
        gsap.fromTo(waterLevel,
            { height: '0%' },
            { height: '70%', duration: 2, ease: "power2.out" }
        );
    }
    
    // Animate gauge needle
    const needle = document.getElementById('voltageNeedle');
    if (needle) {
        gsap.fromTo(needle,
            { rotation: -90 },
            { rotation: 30, duration: 2, ease: "elastic.out(1, 0.3)" }
        );
    }
}

function initializeVoltageDemo() {
    const voltageSlider = document.getElementById('voltageSlider');
    const voltageDisplay = document.getElementById('voltageDisplay');
    const voltageReadout = document.getElementById('voltageReadout');
    const batteryVisual = document.getElementById('batteryVisual');
    
    if (voltageSlider && voltageDisplay && voltageReadout) {
        voltageSlider.addEventListener('input', function() {
            const voltage = parseFloat(this.value);
            voltageDisplay.textContent = `${voltage}V`;
            voltageReadout.textContent = `${voltage}V`;
            
            // Update battery visual based on voltage
            if (batteryVisual) {
                const intensity = Math.min(voltage / 24, 1);
                batteryVisual.style.filter = `brightness(${0.5 + intensity * 0.5})`;
            }
        });
    }
}

function animateCurrentFlow() {
    // Current flow is handled by CSS animations
    // Add any additional JavaScript-based animations here
}

function setupOhmsLawInteraction() {
    const triangleSections = document.querySelectorAll('.triangle-section');
    const formulaDisplay = document.getElementById('formulaDisplay');
    
    triangleSections.forEach(section => {
        section.addEventListener('click', function() {
            const variable = this.dataset.variable;
            let formula = '';
            
            switch(variable) {
                case 'V':
                    formula = 'V = I × R';
                    break;
                case 'I':
                    formula = 'I = V / R';
                    break;
                case 'R':
                    formula = 'R = V / I';
                    break;
            }
            
            if (formulaDisplay) {
                formulaDisplay.textContent = formula;
                
                // Animate formula change
                gsap.fromTo(formulaDisplay,
                    { scale: 0.8, opacity: 0.5 },
                    { scale: 1, opacity: 1, duration: 0.3, ease: "back.out(1.7)" }
                );
            }
            
            // Highlight selected section
            triangleSections.forEach(s => s.classList.remove('selected'));
            this.classList.add('selected');
        });
    });
}

function setupCalculator() {
    const inputs = document.querySelectorAll('#calcVoltage, #calcCurrent, #calcResistance');
    
    inputs.forEach(input => {
        input.addEventListener('input', calculateOhmsLaw);
    });
}

function calculateOhmsLaw() {
    const voltage = parseFloat(document.getElementById('calcVoltage').value) || 0;
    const current = parseFloat(document.getElementById('calcCurrent').value) || 0;
    const resistance = parseFloat(document.getElementById('calcResistance').value) || 0;
    
    let calculatedV = voltage;
    let calculatedI = current;
    let calculatedR = resistance;
    let calculatedP = 0;
    
    // Calculate missing values
    if (voltage && current && !resistance) {
        calculatedR = voltage / current;
    } else if (voltage && resistance && !current) {
        calculatedI = voltage / resistance;
    } else if (current && resistance && !voltage) {
        calculatedV = current * resistance;
    }
    
    // Calculate power
    if (calculatedV && calculatedI) {
        calculatedP = calculatedV * calculatedI;
    }
    
    // Update results
    document.getElementById('resultV').textContent = calculatedV ? `${calculatedV.toFixed(2)}V` : '--';
    document.getElementById('resultI').textContent = calculatedI ? `${calculatedI.toFixed(3)}A` : '--';
    document.getElementById('resultR').textContent = calculatedR ? `${calculatedR.toFixed(2)}Ω` : '--';
    document.getElementById('resultP').textContent = calculatedP ? `${calculatedP.toFixed(3)}W` : '--';
}

function resetCalculator() {
    document.getElementById('calcVoltage').value = '';
    document.getElementById('calcCurrent').value = '';
    document.getElementById('calcResistance').value = '';
    
    document.getElementById('resultV').textContent = '--';
    document.getElementById('resultI').textContent = '--';
    document.getElementById('resultR').textContent = '--';
    document.getElementById('resultP').textContent = '--';
}

function setupComponentInteractions() {
    const componentCards = document.querySelectorAll('.component-card');
    
    componentCards.forEach(card => {
        card.addEventListener('click', function() {
            const component = this.dataset.component;
            showComponentDetails(component);
        });
    });
}

function showComponentDetails(component) {
    // This could open a detailed view or navigate to specific slides
    console.log('Showing details for component:', component);
}

function animateComponentShowcase() {
    const componentCards = document.querySelectorAll('.component-card');
    
    componentCards.forEach((card, index) => {
        // Add hover animations
        card.addEventListener('mouseenter', function() {
            gsap.to(this, {
                y: -10,
                scale: 1.05,
                duration: 0.3,
                ease: "power2.out"
            });
        });
        
        card.addEventListener('mouseleave', function() {
            gsap.to(this, {
                y: 0,
                scale: 1,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });
}

function setupVoltageSlider() {
    const voltageSlider = document.getElementById('voltageSlider');
    if (voltageSlider) {
        voltageSlider.addEventListener('input', function() {
            const voltage = parseFloat(this.value);
            updateVoltageVisualization(voltage);
        });
    }
}

function updateVoltageVisualization(voltage) {
    // Update voltage display elements
    const displays = document.querySelectorAll('.voltage-display, .voltage-readout');
    displays.forEach(display => {
        display.textContent = `${voltage}V`;
    });
    
    // Update visual effects based on voltage
    const batteryVisual = document.getElementById('batteryVisual');
    if (batteryVisual) {
        const intensity = Math.min(voltage / 24, 1);
        batteryVisual.style.filter = `brightness(${0.5 + intensity * 0.5}) saturate(${0.8 + intensity * 0.4})`;
    }
}

function handleKeyPress(event) {
    switch(event.key) {
        case 'ArrowLeft':
            event.preventDefault();
            previousSlide();
            break;
        case 'ArrowRight':
        case ' ':
            event.preventDefault();
            nextSlide();
            break;
        case 'Escape':
            if (document.fullscreenElement) {
                document.exitFullscreen();
            }
            break;
        case 'f':
        case 'F':
            toggleFullscreen();
            break;
    }
}

function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

function generateSlideThumbnails() {
    const thumbnailContainer = document.getElementById('slideThumbnails');
    if (!thumbnailContainer) return;
    
    const slides = document.querySelectorAll('.slide');
    
    slides.forEach((slide, index) => {
        const thumbnail = document.createElement('div');
        thumbnail.className = 'slide-thumbnail';
        thumbnail.dataset.slide = index;
        
        const slideTitle = slide.querySelector('h2, h1');
        const title = slideTitle ? slideTitle.textContent : `Slide ${index + 1}`;
        
        thumbnail.innerHTML = `
            <div class="thumbnail-preview"></div>
            <div class="thumbnail-title">${title}</div>
        `;
        
        thumbnail.addEventListener('click', () => showSlide(index));
        thumbnailContainer.appendChild(thumbnail);
    });
}

// Simulation Workbench Functions
function setupSimulationWorkbench() {
    // Initialize canvas
    canvas = document.getElementById('designCanvas');
    if (canvas) {
        ctx = canvas.getContext('2d');
        setupCanvasEvents();
    }
    
    // Setup tool buttons
    setupToolButtons();
    setupViewButtons();
    setupActionButtons();
    setupComponentLibrary();
}

function openSimulation() {
    const modal = document.getElementById('simulationModal');
    modal.classList.add('active');
    
    // Initialize canvas if not already done
    if (canvas && !ctx) {
        ctx = canvas.getContext('2d');
        setupCanvasEvents();
    }
    
    drawCanvas();
}

function closeSimulation() {
    const modal = document.getElementById('simulationModal');
    modal.classList.remove('active');
}

function setupToolButtons() {
    const toolButtons = document.querySelectorAll('.tool-btn');
    toolButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            toolButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            selectedTool = this.dataset.tool;
        });
    });
}

function setupViewButtons() {
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            viewButtons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentView = this.dataset.view;
            drawCanvas();
        });
    });
}

function setupActionButtons() {
    document.getElementById('simulateBtn').addEventListener('click', runSimulation);
    document.getElementById('clearBtn').addEventListener('click', clearCanvas);
}

function setupComponentLibrary() {
    const componentItems = document.querySelectorAll('.component-item');
    componentItems.forEach(item => {
        item.addEventListener('click', function() {
            const componentType = this.dataset.component;
            addComponent(componentType);
        });
    });
}

function setupCanvasEvents() {
    canvas.addEventListener('mousedown', handleCanvasMouseDown);
    canvas.addEventListener('mousemove', handleCanvasMouseMove);
    canvas.addEventListener('mouseup', handleCanvasMouseUp);
}

function handleCanvasMouseDown(event) {
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    if (selectedTool === 'select') {
        selectedComponent = getComponentAt(x, y);
        if (selectedComponent) {
            isDragging = true;
            dragOffset.x = x - selectedComponent.x;
            dragOffset.y = y - selectedComponent.y;
        }
    }
}

function handleCanvasMouseMove(event) {
    if (isDragging && selectedComponent) {
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        selectedComponent.x = x - dragOffset.x;
        selectedComponent.y = y - dragOffset.y;
        
        drawCanvas();
    }
}

function handleCanvasMouseUp(event) {
    isDragging = false;
    selectedComponent = null;
}

function addComponent(type) {
    const component = {
        id: Date.now(),
        type: type,
        x: 100 + Math.random() * 200,
        y: 100 + Math.random() * 200,
        width: 60,
        height: 40,
        value: getDefaultValue(type),
        connections: []
    };
    
    components.push(component);
    drawCanvas();
}

function getDefaultValue(type) {
    switch(type) {
        case 'resistor': return '1kΩ';
        case 'capacitor': return '100μF';
        case 'inductor': return '10mH';
        case 'voltage-source': return '12V';
        default: return '';
    }
}

function getComponentAt(x, y) {
    return components.find(component => 
        x >= component.x && x <= component.x + component.width &&
        y >= component.y && y <= component.y + component.height
    );
}

function drawCanvas() {
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid
    drawGrid();
    
    // Draw components based on current view
    switch(currentView) {
        case 'schematic':
            drawSchematicView();
            break;
        case 'pcb':
            drawPCBView();
            break;
        case 'block':
            drawBlockDiagramView();
            break;
    }
}

function drawGrid() {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;
    
    const gridSize = 20;
    
    for (let x = 0; x <= canvas.width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }
    
    for (let y = 0; y <= canvas.height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
}

function drawSchematicView() {
    components.forEach(component => {
        drawSchematicComponent(component);
    });
}

function drawPCBView() {
    // Draw PCB background
    ctx.fillStyle = '#2d5016';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    components.forEach(component => {
        drawPCBComponent(component);
    });
}

function drawBlockDiagramView() {
    components.forEach(component => {
        drawBlockComponent(component);
    });
}

function drawSchematicComponent(component) {
    ctx.save();
    ctx.translate(component.x, component.y);
    
    switch(component.type) {
        case 'resistor':
            drawResistorSymbol();
            break;
        case 'capacitor':
            drawCapacitorSymbol();
            break;
        case 'inductor':
            drawInductorSymbol();
            break;
        case 'voltage-source':
            drawVoltageSourceSymbol();
            break;
        case 'ground':
            drawGroundSymbol();
            break;
    }
    
    // Draw component value
    ctx.fillStyle = 'white';
    ctx.font = '12px Inter';
    ctx.textAlign = 'center';
    ctx.fillText(component.value, component.width/2, component.height + 15);
    
    ctx.restore();
}

function drawResistorSymbol() {
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.rect(10, 15, 40, 10);
    ctx.stroke();
    
    // Leads
    ctx.beginPath();
    ctx.moveTo(0, 20);
    ctx.lineTo(10, 20);
    ctx.moveTo(50, 20);
    ctx.lineTo(60, 20);
    ctx.stroke();
}

function drawCapacitorSymbol() {
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    
    // Plates
    ctx.beginPath();
    ctx.moveTo(25, 10);
    ctx.lineTo(25, 30);
    ctx.moveTo(35, 10);
    ctx.lineTo(35, 30);
    ctx.stroke();
    
    // Leads
    ctx.beginPath();
    ctx.moveTo(0, 20);
    ctx.lineTo(25, 20);
    ctx.moveTo(35, 20);
    ctx.lineTo(60, 20);
    ctx.stroke();
}

function drawInductorSymbol() {
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    // Coil arcs
    for (let i = 0; i < 4; i++) {
        ctx.arc(15 + i * 8, 20, 4, Math.PI, 0, false);
    }
    ctx.stroke();
    
    // Leads
    ctx.beginPath();
    ctx.moveTo(0, 20);
    ctx.lineTo(11, 20);
    ctx.moveTo(49, 20);
    ctx.lineTo(60, 20);
    ctx.stroke();
}

function drawVoltageSourceSymbol() {
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    
    // Circle
    ctx.beginPath();
    ctx.arc(30, 20, 15, 0, Math.PI * 2);
    ctx.stroke();
    
    // Plus and minus
    ctx.fillStyle = 'white';
    ctx.font = '14px Inter';
    ctx.textAlign = 'center';
    ctx.fillText('+', 25, 17);
    ctx.fillText('-', 35, 17);
    
    // Leads
    ctx.beginPath();
    ctx.moveTo(0, 20);
    ctx.lineTo(15, 20);
    ctx.moveTo(45, 20);
    ctx.lineTo(60, 20);
    ctx.stroke();
}

function drawGroundSymbol() {
    ctx.strokeStyle = 'white';
    ctx.lineWidth = 2;
    
    ctx.beginPath();
    ctx.moveTo(30, 0);
    ctx.lineTo(30, 20);
    ctx.moveTo(20, 20);
    ctx.lineTo(40, 20);
    ctx.moveTo(23, 25);
    ctx.lineTo(37, 25);
    ctx.moveTo(26, 30);
    ctx.lineTo(34, 30);
    ctx.stroke();
}

function drawPCBComponent(component) {
    // Draw PCB footprint
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(component.x, component.y, component.width, component.height);
    
    // Draw pads
    ctx.fillStyle = '#FFD700';
    ctx.fillRect(component.x + 5, component.y + 15, 10, 10);
    ctx.fillRect(component.x + component.width - 15, component.y + 15, 10, 10);
}

function drawBlockComponent(component) {
    // Draw block
    ctx.fillStyle = 'rgba(79, 172, 254, 0.3)';
    ctx.fillRect(component.x, component.y, component.width, component.height);
    
    ctx.strokeStyle = '#4facfe';
    ctx.lineWidth = 2;
    ctx.strokeRect(component.x, component.y, component.width, component.height);
    
    // Draw label
    ctx.fillStyle = 'white';
    ctx.font = '12px Inter';
    ctx.textAlign = 'center';
    ctx.fillText(component.type.toUpperCase(), component.x + component.width/2, component.y + component.height/2 + 4);
}

function runSimulation() {
    console.log('Running simulation with components:', components);
    // Implement simulation logic here
    alert('Simulation started! (This would run actual circuit simulation)');
}

function clearCanvas() {
    components = [];
    connections = [];
    drawCanvas();
}

// New interactive functions for additional slides
function initializeResistanceDemo() {
    const lengthSlider = document.getElementById('lengthSlider');
    const areaSlider = document.getElementById('areaSlider');
    const materialSelect = document.getElementById('materialSelect');

    if (lengthSlider && areaSlider && materialSelect) {
        const updateResistance = () => {
            const length = parseFloat(lengthSlider.value);
            const area = parseFloat(areaSlider.value);
            const material = materialSelect.value;

            // Resistivity values (Ω⋅m)
            const resistivities = {
                copper: 1.7e-8,
                aluminum: 2.8e-8,
                iron: 10e-8,
                carbon: 3500e-8
            };

            const resistivity = resistivities[material];
            const resistance = (resistivity * length) / (area * 1e-6); // Convert mm² to m²

            document.getElementById('lengthValue').textContent = `${length}m`;
            document.getElementById('areaValue').textContent = `${area}mm²`;
            document.getElementById('resistanceResult').textContent = `${(resistance * 1000).toFixed(2)} mΩ`;
        };

        lengthSlider.addEventListener('input', updateResistance);
        areaSlider.addEventListener('input', updateResistance);
        materialSelect.addEventListener('change', updateResistance);

        updateResistance(); // Initial calculation
    }
}

function initializePowerCalculator() {
    const powerVoltage = document.getElementById('powerVoltage');
    const powerCurrent = document.getElementById('powerCurrent');
    const powerResistance = document.getElementById('powerResistance');

    if (powerVoltage && powerCurrent && powerResistance) {
        const calculatePower = () => {
            const V = parseFloat(powerVoltage.value) || 0;
            const I = parseFloat(powerCurrent.value) || 0;
            const R = parseFloat(powerResistance.value) || 0;

            let powerVI = 0, powerI2R = 0, powerV2R = 0, energy = 0;

            if (V && I) powerVI = V * I;
            if (I && R) powerI2R = I * I * R;
            if (V && R) powerV2R = (V * V) / R;
            if (powerVI) energy = powerVI; // Wh per hour

            document.getElementById('powerVI').textContent = powerVI ? `${powerVI.toFixed(3)}W` : '--';
            document.getElementById('powerI2R').textContent = powerI2R ? `${powerI2R.toFixed(3)}W` : '--';
            document.getElementById('powerV2R').textContent = powerV2R ? `${powerV2R.toFixed(3)}W` : '--';
            document.getElementById('energyPerHour').textContent = energy ? `${energy.toFixed(3)}Wh` : '--';
        };

        powerVoltage.addEventListener('input', calculatePower);
        powerCurrent.addEventListener('input', calculatePower);
        powerResistance.addEventListener('input', calculatePower);
    }
}

function animateSolutionSteps() {
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        gsap.fromTo(step,
            { opacity: 0, x: -50 },
            {
                opacity: 1,
                x: 0,
                duration: 0.8,
                delay: index * 0.3,
                ease: "power2.out"
            }
        );
    });
}

function initializeResistorColorCode() {
    // Initialize with default values
    updateResistorValue();
}

function updateResistorValue() {
    const digit1 = document.getElementById('digit1Select');
    const digit2 = document.getElementById('digit2Select');
    const multiplier = document.getElementById('multiplierSelect');
    const tolerance = document.getElementById('toleranceSelect');

    if (digit1 && digit2 && multiplier && tolerance) {
        const d1 = parseInt(digit1.value);
        const d2 = parseInt(digit2.value);
        const mult = parseInt(multiplier.value);
        const tol = parseInt(tolerance.value);

        const resistance = (d1 * 10 + d2) * mult;
        const minValue = resistance * (1 - tol / 100);
        const maxValue = resistance * (1 + tol / 100);

        // Update visual bands
        updateColorBands(digit1.selectedOptions[0].dataset.color,
                        digit2.selectedOptions[0].dataset.color,
                        multiplier.selectedOptions[0].dataset.color,
                        tolerance.selectedOptions[0].dataset.color);

        // Update display
        document.getElementById('resistorValueResult').textContent = formatResistance(resistance) + ` ±${tol}%`;
        document.getElementById('resistorRange').textContent = `${formatResistance(minValue)} - ${formatResistance(maxValue)}`;
    }
}

function updateColorBands(color1, color2, color3, color4) {
    const band1 = document.getElementById('band1Large');
    const band2 = document.getElementById('band2Large');
    const band3 = document.getElementById('band3Large');
    const band4 = document.getElementById('band4Large');

    if (band1) band1.style.backgroundColor = getColorValue(color1);
    if (band2) band2.style.backgroundColor = getColorValue(color2);
    if (band3) band3.style.backgroundColor = getColorValue(color3);
    if (band4) band4.style.backgroundColor = getColorValue(color4);
}

function getColorValue(colorName) {
    const colors = {
        black: '#000000',
        brown: '#8b4513',
        red: '#ff0000',
        orange: '#ffa500',
        yellow: '#ffff00',
        green: '#008000',
        blue: '#0000ff',
        violet: '#8b00ff',
        gray: '#808080',
        white: '#ffffff',
        gold: '#ffd700',
        silver: '#c0c0c0'
    };
    return colors[colorName] || '#000000';
}

function formatResistance(value) {
    if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'MΩ';
    } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'kΩ';
    } else {
        return value.toFixed(0) + 'Ω';
    }
}

function initializeCapacitorDemo() {
    const startCharging = document.getElementById('startCharging');
    const discharge = document.getElementById('discharge');
    const chargeSwitch = document.getElementById('chargeSwitch');

    if (startCharging && discharge && chargeSwitch) {
        startCharging.addEventListener('click', () => {
            chargeSwitch.classList.add('on');
            animateCapacitorCharging();
        });

        discharge.addEventListener('click', () => {
            chargeSwitch.classList.remove('on');
            animateCapacitorDischarging();
        });
    }

    // Initialize charging graph
    initializeChargingGraph();
}

function animateCapacitorCharging() {
    const positivePlate = document.querySelector('.positive-plate');
    const negativePlate = document.querySelector('.negative-plate');

    if (positivePlate && negativePlate) {
        gsap.to(positivePlate, {
            boxShadow: '0 0 20px #ff6b6b',
            duration: 2,
            ease: "power2.out"
        });

        gsap.to(negativePlate, {
            boxShadow: '0 0 20px #4facfe',
            duration: 2,
            ease: "power2.out"
        });
    }
}

function animateCapacitorDischarging() {
    const positivePlate = document.querySelector('.positive-plate');
    const negativePlate = document.querySelector('.negative-plate');

    if (positivePlate && negativePlate) {
        gsap.to([positivePlate, negativePlate], {
            boxShadow: 'none',
            duration: 1,
            ease: "power2.out"
        });
    }
}

function initializeChargingGraph() {
    const canvas = document.getElementById('chargingGraph');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // Draw charging curve
    ctx.clearRect(0, 0, width, height);
    ctx.strokeStyle = '#4facfe';
    ctx.lineWidth = 2;
    ctx.beginPath();

    for (let x = 0; x < width; x++) {
        const t = (x / width) * 5; // 5 time constants
        const voltage = 5 * (1 - Math.exp(-t)); // Charging equation
        const y = height - (voltage / 5) * height;

        if (x === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    }
    ctx.stroke();

    // Draw axes
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, height);
    ctx.lineTo(width, height);
    ctx.moveTo(0, 0);
    ctx.lineTo(0, height);
    ctx.stroke();
}

function initializeInductorDemo() {
    const turnsInput = document.getElementById('turnsInput');
    const areaInput = document.getElementById('areaInput');
    const lengthInput = document.getElementById('lengthInput');

    if (turnsInput && areaInput && lengthInput) {
        const calculateInductance = () => {
            const N = parseInt(turnsInput.value) || 0;
            const A = parseFloat(areaInput.value) * 1e-4 || 0; // Convert cm² to m²
            const l = parseFloat(lengthInput.value) * 1e-2 || 0; // Convert cm to m

            const mu0 = 4 * Math.PI * 1e-7; // Permeability of free space
            const inductance = (mu0 * N * N * A) / l;

            document.getElementById('inductanceValue').textContent = `${(inductance * 1e6).toFixed(1)} μH`;
        };

        turnsInput.addEventListener('input', calculateInductance);
        areaInput.addEventListener('input', calculateInductance);
        lengthInput.addEventListener('input', calculateInductance);

        calculateInductance(); // Initial calculation
    }
}

// Export functions for global access
window.nextSlide = nextSlide;
window.previousSlide = previousSlide;
window.toggleAutoPlay = toggleAutoPlay;
window.openSimulation = openSimulation;
window.closeSimulation = closeSimulation;
window.updateResistorValue = updateResistorValue;
