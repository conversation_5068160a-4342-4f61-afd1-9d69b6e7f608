
import React from 'react';

interface IconProps {
  className?: string;
  strokeWidth?: number; // Allow overriding strokeWidth
}

export const LightbulbIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.355a7.5 7.5 0 0 1-3 0m3 0a7.5 7.5 0 0 0-3 0M12 9.75M12 9.75A3 3 0 0 0 9 6.75m3 3A3 3 0 0 1 15 6.75m-3 3v2.25m0 0A1.5 1.5 0 0 0 9.75 12M12 12A1.5 1.5 0 0 1 14.25 12M12 6.75a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z" />
  </svg>
);

export const CalculatorIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V18Zm2.498-6.75h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V13.5Zm0 2.25h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V18Zm2.504-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5Zm0 2.25h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V18Zm2.498-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9A1.875 1.875 0 0 1 6.375 6h11.25a1.875 1.875 0 0 1 1.875 1.875v3.75A1.875 1.875 0 0 1 17.625 15H6.375A1.875 1.875 0 0 1 4.5 13.125V7.5Z" />
  </svg>
);

export const ChipIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M8.25 21v-1.5M21 15.75h-1.5M15.75 3v1.5M3 15.75h1.5M21 8.25h-1.5m-10.5 3.75h6.75m-6.75 0H6.75m5.25 0v6.75m0-6.75V9m0 3.75h6.75M12 9v6.75M17.25 9v6.75M6.75 9v6.75M9 3.75H6.75A2.25 2.25 0 0 0 4.5 6v12a2.25 2.25 0 0 0 2.25 2.25h10.5A2.25 2.25 0 0 0 19.5 18V6a2.25 2.25 0 0 0-2.25-2.25H15M9 3.75V6.75M15 3.75V6.75" />
  </svg>
);

export const CircuitBoardIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M21.75 9.75C21.75 7.99033 20.2597 6.5 18.5 6.5H17V5.5C17 4.94772 16.5523 4.5 16 4.5H8C7.44772 4.5 7 4.94772 7 5.5V6.5H5.5C3.74033 6.5 2.25 7.99033 2.25 9.75V14.25C2.25 16.0097 3.74033 17.5 5.5 17.5H7V18.5C7 19.0523 7.44772 19.5 8 19.5H16C16.5523 19.5 17 19.0523 17 18.5V17.5H18.5C20.2597 17.5 21.75 16.0097 21.75 14.25V9.75Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M7 12H9M11 12H13M15 12H17" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M7 9.5V8M7 16V14.5" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M17 9.5V8M17 16V14.5" />
    <circle cx="5.5" cy="12" r="1" fill="currentColor" />
    <circle cx="18.5" cy="12" r="1" fill="currentColor" />
 </svg>
);

export const ArrowLeftIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
  </svg>
);

export const CheckCircleIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const XCircleIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const BookOpenIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25" />
  </svg>
);

export const AcademicCapIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.627 48.627 0 0 1 12 20.904a48.627 48.627 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.57 50.57 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5" />
  </svg>
);

export const PlayIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
  </svg>
);

export const SignalIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
     <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 17.25c-.798 0-1.44.715-1.44 1.59V21a.75.75 0 0 0 .75.75h3.75a.75.75 0 0 0 .75-.75v-2.16c0-.874-.642-1.59-1.44-1.59H3.75ZM3.75 17.25h2.06C7.057 17.25 7.5 16.634 7.5 15.84V12.75c0-1.236.722-2.331 1.804-2.811l1.612-.806a1.125 1.125 0 0 1 1.184 0l1.612.806c1.082.48 1.804 1.575 1.804 2.811v3.09c0 .793.443 1.41.803 1.41h2.06c.798 0 1.44-.716 1.44-1.59V12a.75.75 0 0 0-.75-.75h-3.75a.75.75 0 0 0-.75.75v2.16c0 .874.642 1.59 1.44 1.59h.211a1.125 1.125 0 0 1 1.125 1.125v2.625c0 .621-.504 1.125-1.125 1.125H9.375a1.125 1.125 0 0 1-1.125-1.125V18.375c0-.621.504-1.125 1.125-1.125h.211" />
  </svg>
);

export const HeartIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z" />
  </svg>
);

export const WrenchScrewdriverIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.472-2.472a.383.383 0 0 0 .03-.493l-1.228-1.964a.383.383 0 0 0-.492-.03L9.013 12.48a.383.383 0 0 0-.03.493l1.228 1.964a.383.383 0 0 0 .492.03Zm-.384-4.054.856-.537a.383.383 0 0 0 .036-.508l-1.098-1.755a.383.383 0 0 0-.508-.035l-.856.536a.383.383 0 0 0-.035.508l1.098 1.755a.383.383 0 0 0 .508.036Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 11.25A8.25 8.25 0 0 1 11.25 19.5A8.25 8.25 0 0 1 3 11.25A8.25 8.25 0 0 1 11.25 3c1.006 0 1.963.19 2.85.53M19.5 11.25h-3.36a1.125 1.125 0 0 0-1.125 1.125v3.36c0 .622.503 1.125 1.125 1.125h3.36A1.125 1.125 0 0 0 20.625 15.75v-3.36a1.125 1.125 0 0 0-1.125-1.125Z" />
  </svg>
);

export const ChartBarIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25A1.125 1.125 0 0 1 9.75 19.875V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z" />
  </svg>
);

export const ChevronDownIcon: React.FC<IconProps> = ({ className, strokeWidth = 1.5 }) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={strokeWidth} stroke="currentColor" className={className || "w-6 h-6"}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
  </svg>
);


export const Icons = {
    LightbulbIcon,
    CalculatorIcon,
    ChipIcon,
    CircuitBoardIcon,
    ArrowLeftIcon,
    CheckCircleIcon,
    XCircleIcon,
    BookOpenIcon,
    AcademicCapIcon,
    PlayIcon,
    SignalIcon,
    HeartIcon,
    WrenchScrewdriverIcon,
    ChartBarIcon,
    ChevronDownIcon
};
