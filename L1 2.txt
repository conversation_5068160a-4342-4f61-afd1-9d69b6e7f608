<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات القياس الأساسية</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            overflow: hidden;
        }
        .slide {
            width: 1280px;
            min-height: 720px;
            position: relative;
            overflow: hidden;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 1.5s ease-in-out;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.9; }
            50% { transform: scale(1.05); opacity: 1; }
            100% { transform: scale(1); opacity: 0.9; }
        }
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.4);
        }
        .circuit-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: dash 6s linear forwards infinite;
        }
        @keyframes dash {
            to { stroke-dashoffset: 0; }
        }
        .meter-needle {
            transform-origin: bottom center;
            animation: needleSwing 4s ease-in-out infinite;
        }
        @keyframes needleSwing {
            0%, 100% { transform: rotate(-40deg); }
            50% { transform: rotate(40deg); }
        }
        .scope-wave {
            stroke-dasharray: 400;
            stroke-dashoffset: 400;
            animation: waveDraw 3s linear infinite;
        }
        @keyframes waveDraw {
            to { stroke-dashoffset: -400; }
        }
        .blink {
            animation: blink 2s ease-in-out infinite;
        }
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        .meter-display-flash {
            animation: displayFlash 4s linear infinite;
        }
        @keyframes displayFlash {
            0%, 100% { background-color: rgba(79, 209, 197, 0.2); }
            50% { background-color: rgba(79, 209, 197, 0.5); }
        }
        .probe-move {
            animation: probeMove 5s ease-in-out infinite;
        }
        @keyframes probeMove {
            0%, 100% { transform: translate(0, 0); }
            25% { transform: translate(5px, -5px); }
            75% { transform: translate(-5px, 5px); }
        }
    </style>
</head>
<body>
    <div class="slide bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900 flex flex-col p-10 text-white">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg width="1280" height="720" viewBox="0 0 1280 720">
                <defs>
                    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4fd1c5" stroke-width="1" opacity="0.3"/>
                    </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                <path class="circuit-line" d="M50,200 C150,250 250,150 350,200 L600,200" fill="none" stroke="#4fd1c5" stroke-width="2"/>
                <path class="circuit-line" d="M1200,500 C1100,450 1000,550 900,500 L650,500" fill="none" stroke="#4fd1c5" stroke-width="2"/>
            </svg>
        </div>
        
        <!-- Header with animated icon -->
        <div class="flex items-center mb-6 fade-in">
            <div class="pulse bg-teal-600 bg-opacity-30 rounded-full p-3 ml-4">
                <i class="fas fa-tools text-teal-300 text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-teal-300">أدوات القياس الأساسية</h1>
        </div>
        
        <!-- Main content in two columns -->
        <div class="flex gap-8">
            <!-- Left column - Digital Multimeter -->
            <div class="w-1/2 fade-in" style="animation-delay: 0.3s">
                <div class="bg-black bg-opacity-30 rounded-lg p-5 shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-700 bg-opacity-50 rounded-full p-2 ml-3">
                            <i class="fas fa-tachometer-alt text-yellow-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-300">الملتيميتر الرقمي (DMM)</h2>
                    </div>
                    
                    <!-- Digital multimeter animation -->
                    <div class="mb-4">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4 flex flex-col items-center">
                            <svg width="240" height="180" viewBox="0 0 240 180">
                                <!-- Multimeter body -->
                                <rect x="40" y="20" width="160" height="140" rx="10" fill="#2D3748" stroke="#4FD1C5" stroke-width="2" />
                                
                                <!-- Display -->
                                <rect x="60" y="30" width="120" height="40" rx="5" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                <rect x="70" y="40" width="100" height="20" class="meter-display-flash" />
                                <text x="120" y="55" text-anchor="middle" fill="#4FD1C5" font-size="16">12.47 V</text>
                                
                                <!-- Dial -->
                                <circle cx="120" cy="110" r="30" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                <path d="M95,110 L145,110 M120,85 L120,135" stroke="#4FD1C5" stroke-width="0.5" opacity="0.5"/>
                                
                                <!-- Dial markings -->
                                <text x="95" y="110" text-anchor="middle" fill="#4FD1C5" font-size="8">V</text>
                                <text x="145" y="110" text-anchor="middle" fill="#4FD1C5" font-size="8">A</text>
                                <text x="120" y="85" text-anchor="middle" fill="#4FD1C5" font-size="8">Ω</text>
                                <text x="120" y="135" text-anchor="middle" fill="#4FD1C5" font-size="8">Hz</text>
                                
                                <!-- Dial pointer -->
                                <line x1="120" y1="110" x2="120" y2="90" stroke="#FBBF24" stroke-width="2" class="meter-needle" />
                                <circle cx="120" cy="110" r="3" fill="#FBBF24" />
                                
                                <!-- Probes -->
                                <line x1="60" y1="170" x2="20" y2="180" stroke="#E53E3E" stroke-width="2" class="probe-move" />
                                <circle cx="20" cy="180" r="5" fill="#E53E3E" />
                                <line x1="180" y1="170" x2="220" y2="180" stroke="#3182CE" stroke-width="2" class="probe-move" style="animation-delay: 0.5s" />
                                <circle cx="220" cy="180" r="5" fill="#3182CE" />
                            </svg>
                            <p class="text-sm text-center text-gray-300 mt-2">
                                جهاز قياس متعدد الوظائف لقياس الجهد، التيار، المقاومة وغيرها
                            </p>
                        </div>
                    </div>
                    
                    <!-- Multimeter specifications -->
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">المواصفات الهامة:</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-yellow-300 to-yellow-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">الدقة (Accuracy)</h4>
                                </div>
                                <p class="text-xs text-gray-300">±0.5% + 2 خانات</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-green-300 to-green-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">المجالات (Ranges)</h4>
                                </div>
                                <p class="text-xs text-gray-300">mV إلى مئات V، µA إلى A</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-blue-300 to-blue-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">True RMS</h4>
                                </div>
                                <p class="text-xs text-gray-300">لقياس الإشارات المترددة بدقة</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-red-300 to-red-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">تصنيف السلامة</h4>
                                </div>
                                <p class="text-xs text-gray-300">CAT III 600V للحماية</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Medical device application -->
                    <div>
                        <h3 class="text-lg font-bold mb-2 text-teal-300">التطبيقات في الأجهزة الطبية:</h3>
                        <ul class="list-disc list-inside text-sm text-gray-300 space-y-1 bg-teal-900 bg-opacity-20 p-3 rounded-lg">
                            <li>فحص توصيلات الدوائر وقياس جهود التغذية في أجهزة المراقبة الحيوية</li>
                            <li>اختبار المقاومات والمكثفات للتأكد من مطابقتها للمواصفات</li>
                            <li>قياس التيار المسحوب بواسطة مكونات الأجهزة الطبية</li>
                            <li>اختبار استمرارية الدوائر (Continuity) والتأكد من عدم وجود قطع</li>
                            <li>فحص تسرب التيار للتأكد من سلامة المريض</li>
                            <li>قياس مقاومة العزل في الأجهزة الطبية</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Right column - Oscilloscope -->
            <div class="w-1/2">
                <div class="bg-black bg-opacity-30 rounded-lg p-5 shadow-lg fade-in" style="animation-delay: 0.6s">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-700 bg-opacity-50 rounded-full p-2 ml-3">
                            <i class="fas fa-wave-square text-green-400"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-blue-300">راسم الإشارة (الأوسيلوسكوب)</h2>
                    </div>
                    
                    <!-- Oscilloscope animation -->
                    <div class="mb-4">
                        <div class="bg-gray-800 bg-opacity-50 rounded-lg p-4 flex flex-col items-center">
                            <svg width="240" height="180" viewBox="0 0 240 180">
                                <!-- Scope body -->
                                <rect x="30" y="20" width="180" height="140" rx="5" fill="#2D3748" stroke="#4FD1C5" stroke-width="2" />
                                
                                <!-- Screen -->
                                <rect x="40" y="30" width="120" height="90" rx="3" fill="#111827" stroke="#4FD1C5" stroke-width="1" />
                                
                                <!-- Grid lines -->
                                <line x1="40" y1="75" x2="160" y2="75" stroke="#4FD1C5" stroke-width="0.5" opacity="0.3" />
                                <line x1="100" y1="30" x2="100" y2="120" stroke="#4FD1C5" stroke-width="0.5" opacity="0.3" />
                                <path d="M50,75 L60,75 M70,75 L80,75 M90,75 L110,75 M120,75 L130,75 M140,75 L150,75" stroke="#4FD1C5" stroke-width="0.3" opacity="0.2"/>
                                <path d="M100,40 L100,50 M100,60 L100,70 M100,80 L100,90 M100,100 L100,110" stroke="#4FD1C5" stroke-width="0.3" opacity="0.2"/>
                                
                                <!-- ECG waveform on oscilloscope -->
                                <path d="M40,75 L50,75 L60,55 L70,95 L80,75 L90,75 L100,75 L110,75 L120,55 L130,95 L140,75 L150,75 L160,75" 
                                     stroke="#4FD1C5" stroke-width="1.5" fill="none" class="scope-wave" />
                                
                                <!-- Controls -->
                                <circle cx="180" cy="40" r="8" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                <circle cx="180" cy="65" r="8" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                <circle cx="180" cy="90" r="8" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                <circle cx="180" cy="115" r="8" fill="#1A202C" stroke="#4FD1C5" stroke-width="1" />
                                
                                <!-- Small LED indicators -->
                                <circle cx="170" cy="40" r="3" fill="#E53E3E" class="blink" />
                                <circle cx="170" cy="55" r="3" fill="#FBBF24" class="blink" style="animation-delay: 0.3s" />
                                <circle cx="170" cy="70" r="3" fill="#4FD1C5" class="blink" style="animation-delay: 0.6s" />
                                
                                <!-- Probes -->
                                <path d="M60,130 C60,150 40,160 30,170" stroke="#E53E3E" stroke-width="2" fill="none" class="probe-move"/>
                                <circle cx="30" cy="170" r="4" fill="#E53E3E" />
                                <path d="M100,130 C100,150 120,160 130,170" stroke="#3182CE" stroke-width="2" fill="none" class="probe-move" style="animation-delay: 0.5s"/>
                                <circle cx="130" cy="170" r="4" fill="#3182CE" />
                                
                                <!-- Display values -->
                                <text x="100" y="140" text-anchor="middle" fill="#4FD1C5" font-size="8">1mV/div 50ms/div</text>
                            </svg>
                            <p class="text-sm text-center text-gray-300 mt-2">
                                جهاز لعرض وتحليل الإشارات المتغيرة مع الزمن
                            </p>
                        </div>
                    </div>
                    
                    <!-- Oscilloscope specifications -->
                    <div class="mb-4">
                        <h3 class="text-lg font-bold mb-2 text-teal-300">المواصفات الهامة:</h3>
                        <div class="grid grid-cols-2 gap-2">
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-indigo-300 to-indigo-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">عرض النطاق (BW)</h4>
                                </div>
                                <p class="text-xs text-gray-300">50MHz - 200MHz للأجهزة الطبية</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-teal-300 to-teal-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">معدل العينات</h4>
                                </div>
                                <p class="text-xs text-gray-300">1GSa/s أو أعلى</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-purple-300 to-purple-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">عدد القنوات</h4>
                                </div>
                                <p class="text-xs text-gray-300">2 أو 4 قنوات متزامنة</p>
                            </div>
                            <div class="hover-card bg-blue-900 bg-opacity-20 p-3 rounded-lg">
                                <div class="flex items-center mb-1">
                                    <div class="w-4 h-4 rounded-full bg-gradient-to-r from-yellow-300 to-yellow-500 ml-2"></div>
                                    <h4 class="font-semibold text-blue-300">عمق الذاكرة</h4>
                                </div>
                                <p class="text-xs text-gray-300">لتخزين إشارات أطول</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Medical device application -->
                    <div>
                        <h3 class="text-lg font-bold mb-2 text-teal-300">التطبيقات في الأجهزة الطبية:</h3>
                        <ul class="list-disc list-inside text-sm text-gray-300 space-y-1 bg-teal-900 bg-opacity-20 p-3 rounded-lg">
                            <li>فحص إشارات الإخراج في دوائر تكبير الإشارات الحيوية (ECG, EEG)</li>
                            <li>مراقبة أداء النبضات في أجهزة تنظيم ضربات القلب</li>
                            <li>تشخيص مشاكل التداخل والضوضاء في الإشارات الحيوية</li>
                            <li>قياس توقيتات الإشارات في دوائر التحكم الرقمية</li>
                            <li>فحص إشارات المذبذبات في أجهزة الموجات فوق الصوتية</li>
                            <li>تحليل أشكال موجات تغذية الطاقة للكشف عن مشاكل التموج</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom section - Practical example -->
        <div class="mt-4 fade-in" style="animation-delay: 0.9s">
            <div class="bg-black bg-opacity-30 rounded-lg p-4 shadow-lg">
                <h3 class="text-xl font-bold mb-3 text-teal-300 text-center">
                    <i class="fas fa-heartbeat ml-2"></i>
                    مثال تطبيقي: تشخيص مشكلة في جهاز مراقبة قلبية (ECG Monitor)
                </h3>
                <div class="flex gap-6">
                    <!-- Troubleshooting diagram -->
                    <div class="flex-1 flex justify-center">
                        <svg width="420" height="120" viewBox="0 0 420 120">
                            <!-- Patient module -->
                            <rect x="20" y="50" width="60" height="40" rx="5" fill="none" stroke="#4FD1C5" stroke-width="1" />
                            <text x="50" y="75" text-anchor="middle" fill="#ffffff" font-size="10">وحدة المريض</text>
                            
                            <!-- Leads -->
                            <path d="M80,70 C100,70 120,30 140,30" stroke="#E53E3E" stroke-width="1" fill="none" />
                            <path d="M80,70 C100,70 120,70 140,70" stroke="#FBBF24" stroke-width="1" fill="none" />
                            <path d="M80,70 C100,70 120,110 140,110" stroke="#3182CE" stroke-width="1" fill="none" />
                            
                            <!-- Amplifier -->
                            <rect x="140" y="50" width="60" height="40" rx="5" fill="none" stroke="#4FD1C5" stroke-width="1" />
                            <text x="170" y="75" text-anchor="middle" fill="#ffffff" font-size="10">مكبر الإشارة</text>
                            
                            <!-- Signal path -->
                            <line x1="200" y1="70" x2="230" y2="70" stroke="#4FD1C5" stroke-width="1" />
                            
                            <!-- Processor -->
                            <rect x="230" y="50" width="60" height="40" rx="5" fill="none" stroke="#4FD1C5" stroke-width="1" />
                            <text x="260" y="75" text-anchor="middle" fill="#ffffff" font-size="10">المعالج</text>
                            
                            <!-- Output -->
                            <line x1="290" y1="70" x2="320" y2="70" stroke="#4FD1C5" stroke-width="1" />
                            
                            <!-- Display -->
                            <rect x="320" y="40" width="80" height="60" rx="5" fill="none" stroke="#4FD1C5" stroke-width="1" />
                            <path d="M330,70 L340,70 L350,50 L360,90 L370,70 L390,70" stroke="#FF5555" stroke-width="2" fill="none" />
                            <text x="360" y="110" text-anchor="middle" fill="#ffffff" font-size="10">شاشة العرض</text>
                            
                            <!-- Test points -->
                            <circle cx="170" cy="30" r="5" fill="#FBBF24" stroke="#ffffff" stroke-width="1" />
                            <text x="170" y="20" text-anchor="middle" fill="#ffffff" font-size="8">TP1</text>
                            
                            <circle cx="215" cy="70" r="5" fill="#E53E3E" stroke="#ffffff" stroke-width="1" />
                            <text x="215" y="60" text-anchor="middle" fill="#ffffff" font-size="8">TP2</text>
                            
                            <circle cx="305" cy="70" r="5" fill="#4FD1C5" stroke="#ffffff" stroke-width="1" />
                            <text x="305" y="60" text-anchor="middle" fill="#ffffff" font-size="8">TP3</text>
                            
                            <!-- Multimeter probes connecting to test point -->
                            <path d="M170,30 C180,20 190,10 210,10" stroke="#FBBF24" stroke-width="2" fill="none" />
                            <path d="M215,70 C225,50 235,30 250,10" stroke="#E53E3E" stroke-width="2" fill="none" />
                        </svg>
                    </div>
                    
                    <!-- Example explanation -->
                    <div class="flex-1">
                        <p class="text-sm text-gray-300 mb-2">
                            <span class="text-teal-300 font-bold">المشكلة:</span> 
                            جهاز مراقبة قلبية يعرض إشارة مشوهة وضعيفة.
                        </p>
                        <p class="text-sm text-gray-300 mb-2">
                            <span class="text-teal-300 font-bold">منهجية التشخيص:</span>
                        </p>
                        <ol class="list-decimal list-inside text-sm text-gray-300 space-y-1">
                            <li>استخدام الملتيميتر لقياس جهود التغذية في كل مرحلة</li>
                            <li>استخدام الأوسيلوسكوب لفحص الإشارة عند نقاط الاختبار:
                                <ul class="list-disc list-inside mr-6 text-xs">
                                    <li>TP1: مخرج وحدة المريض (إشارة ضعيفة)</li>
                                    <li>TP2: بعد التكبير (يجب أن تكون مكبرة)</li>
                                    <li>TP3: بعد المعالجة (يجب أن تكون نظيفة)</li>
                                </ul>
                            </li>
                            <li>كشف التحليل عن إشارة مشوهة عند TP2 مع تموجات بسبب تأثير مكثف تالف في دائرة التكبير</li>
                        </ol>
                        <p class="text-sm text-gray-300 mt-2">
                            <span class="text-teal-300 font-bold">الحل:</span>
                            استبدال المكثف التالف، ث