<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنفوجرافيك: اتجاهات وأبحاث سوق الإلكترونيات الطبية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #F0F4F8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .kpi-card {
            background-color: #FFFFFF;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 1.5rem;
            text-align: center;
            color: #073B4C;
        }
        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #118AB2;
        }
        .kpi-label {
            font-size: 1rem;
            color: #073B4C;
            margin-top: 0.5rem;
        }
        .section-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #073B4C;
            margin-bottom: 1rem;
            text-align: center;
        }
        .section-intro {
            font-size: 1.125rem;
            color: #073B4C;
            text-align: center;
            margin-bottom: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            color: #073B4C;
        }
        .flow-step {
            background-color: #FFFFFF;
            border: 2px solid #118AB2;
            color: #073B4C;
            padding: 1rem;
            border-radius: 0.5rem;
            text-align: center;
            margin-bottom: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .flow-arrow {
            font-size: 1.5rem;
            color: #118AB2;
            text-align: center;
            margin: 0.25rem 0;
        }

        .trend-card {
            background-color: #ffffff;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
            text-align: center;
        }
        .trend-icon {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
        }
        .trend-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #073B4C;
        }
        .trend-description {
            font-size: 0.875rem;
            color: #073B4C;
        }
    </style>
</head>
<body class="bg-[#F0F4F8] text-[#073B4C]" dir="rtl">
    <!-- 
        Chosen Color Palette: Energetic & Playful
        Primary: #FF6B6B (Coral Red)
        Secondary: #FFD166 (Sunny Yellow)
        Accent 1: #06D6A0 (Tealish Green)
        Accent 2: #118AB2 (Cerulean Blue)
        Neutral/Background: #F0F4F8 (Light Grayish Blue), #FFFFFF (White)
        Text: #073B4C (Dark Blue/Almost Black)

        Narrative Plan:
        1. Introduction: Hook with market size, critical role of electronics.
        2. Current Landscape: Analog/Digital/Hybrid systems.
        3. Growth Drivers: Factors pushing the industry.
        4. Emerging Tech Trends: Wearables, AI, Telemedicine etc.
        5. Standards & Safety: Importance of compliance.
        6. Future Market Outlook: Projections and key growth areas.
        7. Conclusion: Tech-powered healthy future.

        Visualization Choices (Confirming NO SVG & NO MERMAID JS):
        - Section 1 Stat: Single Big Number (HTML/CSS). Goal: Inform. Justification: Standout stat. Method: HTML/CSS. NO SVG.
        - Section 2 Analog/Digital/Hybrid: Donut Chart. Goal: Compare. Justification: Shows proportion. Method: Chart.js (Canvas). NO SVG.
        - Section 3 Growth Drivers: Bar Chart. Goal: Compare. Justification: Compares magnitudes. Method: Chart.js (Canvas). NO SVG.
        - Section 4 Emerging Tech Trends: HTML/CSS Cards with Unicode Icons. Goal: Inform/Organize. Justification: Combines icons and text. Method: HTML/CSS, Unicode. NO SVG.
        - Section 5 Standards/Safety Flow: HTML/CSS based flow diagram. Goal: Organize. Justification: Illustrates process. Method: HTML/CSS. NO SVG, NO MERMAID JS.
        - Section 6 Market Growth Projection: Line Chart. Goal: Change. Justification: Shows trends over time. Method: Chart.js (Canvas). NO SVG.
        
        Mermaid JS was NOT used. SVG graphics were NOT used.
    -->

    <header class="bg-[#118AB2] text-white p-6 shadow-lg">
        <div class="container mx-auto text-center">
            <h1 class="text-3xl font-bold">تحليل اتجاهات سوق الإلكترونيات الطبية وأبحاثه</h1>
            <p class="text-lg mt-2">نظرة معمقة على واقع ومستقبل تكنولوجيا الرعاية الصحية</p>
        </div>
    </header>

    <main class="container mx-auto p-4 md:p-8">

        <section id="introduction" class="mb-12">
            <h2 class="section-title text-[#073B4C]">مقدمة: نبض التكنولوجيا في الرعاية الصحية</h2>
            <p class="section-intro">
                تعد الإلكترونيات الطبية حجر الزاوية في نظام الرعاية الصحية الحديث، حيث تقود الابتكار في التشخيص والعلاج ومراقبة المرضى. يشهد هذا القطاع نموًا متسارعًا مدفوعًا بالتقدم التكنولوجي والطلب المتزايد على حلول رعاية صحية أكثر كفاءة وفعالية. يستعرض هذا الإنفوجرافيك أبرز اتجاهات السوق والتطورات التكنولوجية التي تشكل مستقبل هذا المجال الحيوي.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="kpi-card bg-white">
                    <div class="kpi-value text-[#FF6B6B]">٥٠٠ مليار دولار</div>
                    <div class="kpi-label">الحجم المتوقع لسوق الأجهزة الطبية العالمي بحلول ٢٠٢٧ (بيانات توضيحية)</div>
                </div>
                <div class="kpi-card bg-white">
                    <div class="kpi-value text-[#06D6A0]">٨٪ نمو سنوي</div>
                    <div class="kpi-label">معدل النمو السنوي المركب المتوقع لسوق الإلكترونيات الطبية (بيانات توضيحية)</div>
                </div>
            </div>
        </section>

        <section id="current-landscape" class="mb-12">
            <div class="card">
                <h2 class="section-title text-[#073B4C]">المشهد الحالي للإلكترونيات الطبية</h2>
                <p class="text-center mb-6 text-lg text-[#073B4C]">
                    تتميز الأجهزة الطبية المعاصرة بتكامل الأنظمة التناظرية والرقمية. الإشارات التناظرية من المستشعرات الحيوية غالبًا ما يتم تحويلها إلى بيانات رقمية لمعالجتها وتحليلها، مما يوفر دقة ومرونة أكبر. فهم هذا التوازن ضروري لتقدير تطورات السوق.
                </p>
                <div class="chart-container h-[300px] md:h-[350px] max-h-[400px]">
                    <canvas id="systemsTypeChart"></canvas>
                </div>
                <p class="text-center mt-4 text-sm text-[#073B4C]">يوضح الرسم البياني أعلاه التوزيع النسبي التقريبي للأجهزة الطبية بناءً على نوع الأنظمة الإلكترونية المستخدمة (بيانات توضيحية لأغراض العرض).</p>
            </div>
        </section>
        
        <section id="growth-drivers" class="mb-12">
            <div class="card">
                <h2 class="section-title text-[#073B4C]">محركات النمو والابتكار في القطاع</h2>
                <p class="text-center mb-6 text-lg text-[#073B4C]">
                    عدة عوامل رئيسية تدفع عجلة النمو والابتكار في سوق الإلكترونيات الطبية. هذه المحركات تخلق فرصًا جديدة وتحديات تتطلب حلولًا تكنولوجية متقدمة.
                </p>
                <div class="chart-container h-[300px] md:h-[350px] max-h-[450px]">
                    <canvas id="growthDriversChart"></canvas>
                </div>
                <p class="text-center mt-4 text-sm text-[#073B4C]">يعرض المخطط تأثير العوامل المختلفة على نمو سوق الإلكترونيات الطبية (تقييمات توضيحية).</p>
            </div>
        </section>

        <section id="emerging-tech" class="mb-12">
            <h2 class="section-title text-[#073B4C]">اتجاهات التكنولوجيا الناشئة</h2>
            <p class="section-intro">
                يشهد قطاع الإلكترونيات الطبية موجة من الابتكارات التكنولوجية التي تعد بإحداث تحول جذري في تقديم الرعاية الصحية. هذه الاتجاهات لا تقتصر على تحسين الأجهزة الحالية فحسب، بل تفتح آفاقًا لتطبيقات جديدة تمامًا.
            </p>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="trend-card border-t-4 border-[#FF6B6B]">
                    <div class="trend-icon text-[#FF6B6B]">⌚</div>
                    <h3 class="trend-title">الأجهزة القابلة للارتداء</h3>
                    <p class="trend-description">مراقبة مستمرة للمؤشرات الحيوية، وتوفير بيانات قيمة للأفراد والأطباء، مما يعزز الرعاية الصحية الوقائية والشخصية.</p>
                </div>
                <div class="trend-card border-t-4 border-[#FFD166]">
                    <div class="trend-icon text-[#FFD166]">🧠</div>
                    <h3 class="trend-title">الذكاء الاصطناعي وتعلم الآلة</h3>
                    <p class="trend-description">تحليل البيانات الطبية المعقدة، والمساعدة في التشخيص المبكر، وتخصيص خطط العلاج، وتحسين كفاءة الأنظمة الصحية.</p>
                </div>
                <div class="trend-card border-t-4 border-[#06D6A0]">
                    <div class="trend-icon text-[#06D6A0]">💻</div>
                    <h3 class="trend-title">الطب عن بعد</h3>
                    <p class="trend-description">تقديم الاستشارات والتشخيص والمتابعة الطبية عن بعد، مما يزيد من إمكانية الوصول إلى الرعاية خاصة في المناطق النائية.</p>
                </div>
                <div class="trend-card border-t-4 border-[#118AB2]">
                    <div class="trend-icon text-[#118AB2]">🔗</div>
                    <h3 class="trend-title">الإلكترونيات الحيوية المتكاملة</h3>
                    <p class="trend-description">تطوير أجهزة مرنة تتكامل بسلاسة مع الجسم البشري، مثل الرقع الجلدية الذكية والغرسات المتطورة، لتحسين المراقبة والعلاج.</p>
                </div>
                 <div class="trend-card border-t-4 border-[#FF6B6B]">
                    <div class="trend-icon text-[#FF6B6B]">🔬</div>
                    <h3 class="trend-title">تكنولوجيا النانو</h3>
                    <p class="trend-description">تطبيقات واعدة في توصيل الأدوية بدقة متناهية، وأدوات تشخيصية فائقة الحساسية، وتطوير مواد حيوية جديدة للأجهزة الطبية.</p>
                </div>
                 <div class="trend-card border-t-4 border-[#FFD166]">
                    <div class="trend-icon text-[#FFD166]">🔒</div>
                    <h3 class="trend-title">الأمن السيبراني في الأجهزة الطبية</h3>
                    <p class="trend-description">مع تزايد اتصال الأجهزة، أصبح تأمين البيانات وحماية خصوصية المرضى وضمان سلامة وظائف الجهاز أمرًا بالغ الأهمية.</p>
                </div>
            </div>
        </section>

        <section id="standards-safety" class="mb-12">
            <div class="card">
                <h2 class="section-title text-[#073B4C]">أهمية المعايير والسلامة</h2>
                <p class="text-center mb-6 text-lg text-[#073B4C]">
                    تعتبر المعايير الدولية مثل IEC 60601 أساسية لضمان سلامة وفعالية الأجهزة الطبية. الالتزام بهذه المعايير ليس فقط مطلبًا تنظيميًا ولكنه أيضًا عامل ثقة أساسي للمستخدمين والمرضى.
                </p>
                <div class="max-w-2xl mx-auto">
                    <div class="flow-step bg-[#E0F7FA] border-[#118AB2]">تحديد المتطلبات (ISO 13485, IEC 60601)</div>
                    <div class="flow-arrow">▼</div>
                    <div class="flow-step bg-[#E0F7FA] border-[#118AB2]">تصميم مع مراعاة السلامة (تحليل المخاطر FMEA)</div>
                    <div class="flow-arrow">▼</div>
                    <div class="flow-step bg-[#E0F7FA] border-[#118AB2]">الاختبار والتحقق الصارم (الاختبارات الكهربائية، التوافق الكهرومغناطيسي EMC)</div>
                    <div class="flow-arrow">▼</div>
                    <div class="flow-step bg-[#E0F7FA] border-[#118AB2]">التوثيق والحصول على الموافقات التنظيمية</div>
                    <div class="flow-arrow">▼</div>
                    <div class="flow-step bg-[#C8E6C9] border-[#06D6A0] font-bold">جهاز طبي آمن وفعال في السوق</div>
                </div>
                 <p class="text-center mt-4 text-sm text-[#073B4C]">يوضح هذا التدفق المبسّط المراحل الرئيسية في عملية تطوير الأجهزة الطبية مع التركيز على الامتثال لمعايير السلامة. إنها عملية معقدة تضمن أن الأجهزة آمنة للاستخدام وفعالة في أدائها المقصود.</p>
            </div>
        </section>

        <section id="future-outlook" class="mb-12">
            <div class="card">
                <h2 class="section-title text-[#073B4C]">توقعات السوق المستقبلية</h2>
                <p class="text-center mb-6 text-lg text-[#073B4C]">
                    يبدو مستقبل سوق الإلكترونيات الطبية واعدًا، مع توقعات بنمو مطرد مدفوع بالابتكار المستمر والحاجة المتزايدة لحلول رعاية صحية متقدمة وشخصية.
                </p>
                <div class="chart-container h-[300px] md:h-[350px] max-h-[400px]">
                    <canvas id="marketProjectionChart"></canvas>
                </div>
                <p class="text-center mt-4 text-sm text-[#073B4C]">يمثل الرسم البياني النمو المتوقع لحجم سوق الإلكترونيات الطبية عالميًا خلال السنوات الخمس القادمة (قيم توضيحية).</p>
                <div class="mt-8">
                    <h3 class="text-xl font-semibold text-center mb-4 text-[#073B4C]">أبرز مجالات النمو المتوقعة:</h3>
                    <ul class="list-disc list-inside text-center max-w-md mx-auto space-y-1 text-[#073B4C]">
                        <li>أجهزة المراقبة عن بعد والعناية المنزلية.</li>
                        <li>حلول الطب الشخصي المعتمدة على البيانات الجينية والفردية.</li>
                        <li>الأجهزة الطبية الذكية والمتصلة (إنترنت الأشياء الطبية - IoMT).</li>
                        <li>تطبيقات الروبوتات في الجراحة وإعادة التأهيل.</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="conclusion" class="mb-12 text-center">
            <h2 class="section-title text-[#073B4C]">خاتمة: نحو مستقبل صحي مدعوم بالتكنولوجيا</h2>
            <p class="section-intro max-w-3xl">
                إن التقدم في مجال الإلكترونيات الطبية لا يغير فقط طريقة تقديم الرعاية الصحية، بل يعيد تعريف إمكانيات الحفاظ على الصحة والوقاية من الأمراض. مع استمرار الابتكار والتركيز على السلامة والجودة، يمكننا أن نتطلع إلى مستقبل يتمتع فيه المزيد من الناس بصحة أفضل وحياة أطول، بفضل التكنولوجيا التي تخدم الإنسانية. إن التعاون بين المهندسين والأطباء والباحثين هو مفتاح تحقيق هذا المستقبل الواعد.
            </p>
             <div class="mt-6">
                <a href="#top" class="bg-[#FF6B6B] hover:bg-[#e05252] text-white font-bold py-3 px-6 rounded-lg shadow transition duration-300">
                    العودة إلى الأعلى
                </a>
            </div>
        </section>
    </main>

    <footer class="bg-[#073B4C] text-white text-center p-6 mt-12">
        <p>&copy; ٢٠٢٤ إنفوجرافيك اتجاهات سوق الإلكترونيات الطبية. جميع الحقوق محفوظة (محتوى توضيحي).</p>
        <p class="text-xs mt-1">تم إعداد هذا الإنفوجرافيك بناءً على تحليل اتجاهات الصناعة والبيانات المتاحة بشكل عام.</p>
    </footer>

    <script>
        const CHART_COLORS = {
            primary: '#FF6B6B',
            secondary: '#FFD166',
            accent1: '#06D6A0',
            accent2: '#118AB2',
            textDark: '#073B4C',
        };

        function wrapLabels(label, maxWidth) {
            if (typeof label !== 'string') return label;
            const words = label.split(' ');
            let currentLine = '';
            const lines = [];
            for (const word of words) {
                if ((currentLine + word).length > maxWidth && currentLine.length > 0) {
                    lines.push(currentLine.trim());
                    currentLine = '';
                }
                currentLine += word + ' ';
            }
            if (currentLine.trim().length > 0) {
                lines.push(currentLine.trim());
            }
            return lines.length > 0 ? lines : [label];
        }
        
        const defaultTooltipCallbacks = {
            title: function(tooltipItems) {
                const item = tooltipItems[0];
                let label = item.chart.data.labels[item.dataIndex];
                if (Array.isArray(label)) {
                    return label.join(' ');
                } else {
                    return label;
                }
            }
        };

        const systemsTypeCtx = document.getElementById('systemsTypeChart').getContext('2d');
        new Chart(systemsTypeCtx, {
            type: 'doughnut',
            data: {
                labels: [wrapLabels('أنظمة تناظرية بالكامل',16), wrapLabels('أنظمة رقمية بالكامل',16), wrapLabels('أنظمة هجينة (تناظرية ورقمية)',20)],
                datasets: [{
                    label: 'توزيع أنواع الأنظمة',
                    data: [15, 25, 60],
                    backgroundColor: [
                        CHART_COLORS.primary,
                        CHART_COLORS.secondary,
                        CHART_COLORS.accent1,
                    ],
                    borderColor: '#FFFFFF',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: CHART_COLORS.textDark, font: { size: 12, family: "'Noto Sans Arabic', sans-serif" } }
                    },
                    title: {
                        display: true,
                        text: 'توزيع أنواع الأنظمة في الأجهزة الطبية (تقديري)',
                        color: CHART_COLORS.textDark,
                        font: { size: 16, family: "'Noto Sans Arabic', sans-serif", weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: defaultTooltipCallbacks,
                        bodyFont: { family: "'Noto Sans Arabic', sans-serif" },
                        titleFont: { family: "'Noto Sans Arabic', sans-serif" }
                    }
                }
            }
        });

        const growthDriversCtx = document.getElementById('growthDriversChart').getContext('2d');
        new Chart(growthDriversCtx, {
            type: 'bar',
            data: {
                labels: [
                    wrapLabels('التقدم التكنولوجي المستمر', 20), 
                    wrapLabels('زيادة الوعي الصحي لدى الأفراد', 25), 
                    wrapLabels('ارتفاع معدلات شيخوخة السكان عالميًا', 30), 
                    wrapLabels('زيادة الإنفاق الحكومي على الرعاية الصحية', 30),
                    wrapLabels('الطلب على الطب الشخصي والعلاج الدقيق', 30)
                ],
                datasets: [{
                    label: 'قوة التأثير (تقديري)',
                    data: [90, 75, 85, 70, 80],
                    backgroundColor: [
                        CHART_COLORS.accent2,
                        CHART_COLORS.primary,
                        CHART_COLORS.secondary,
                        CHART_COLORS.accent1,
                        '#EF476F'
                    ],
                    borderColor: '#FFFFFF',
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: { display: false },
                        ticks: { color: CHART_COLORS.textDark, font: { size: 10, family: "'Noto Sans Arabic', sans-serif" } }
                    },
                    y: {
                        ticks: { color: CHART_COLORS.textDark, font: { size: 11, family: "'Noto Sans Arabic', sans-serif" } }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'أبرز محركات النمو في سوق الإلكترونيات الطبية',
                        color: CHART_COLORS.textDark,
                        font: { size: 16, family: "'Noto Sans Arabic', sans-serif", weight: 'bold' }
                    },
                    tooltip: {
                        callbacks: defaultTooltipCallbacks,
                         bodyFont: { family: "'Noto Sans Arabic', sans-serif" },
                        titleFont: { family: "'Noto Sans Arabic', sans-serif" }
                    }
                }
            }
        });
        
        const marketProjectionCtx = document.getElementById('marketProjectionChart').getContext('2d');
        new Chart(marketProjectionCtx, {
            type: 'line',
            data: {
                labels: ['٢٠٢٤', '٢٠٢٥', '٢٠٢٦', '٢٠٢٧', '٢٠٢٨', '٢٠٢٩'],
                datasets: [{
                    label: 'حجم السوق (مليار دولار أمريكي - تقديري)',
                    data: [480, 520, 565, 615, 670, 730],
                    borderColor: CHART_COLORS.primary,
                    backgroundColor: 'rgba(255, 107, 107, 0.2)',
                    fill: true,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                 scales: {
                    y: {
                        beginAtZero: false,
                        ticks: { color: CHART_COLORS.textDark, font: { size: 12, family: "'Noto Sans Arabic', sans-serif" } },
                        title: { display: true, text: 'مليار دولار أمريكي', color: CHART_COLORS.textDark, font: {size: 12, family: "'Noto Sans Arabic', sans-serif" } }
                    },
                    x: {
                         ticks: { color: CHART_COLORS.textDark, font: { size: 12, family: "'Noto Sans Arabic', sans-serif" } }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: { color: CHART_COLORS.textDark, font: { size: 12, family: "'Noto Sans Arabic', sans-serif" } }
                    },
                    title: {
                        display: true,
                        text: 'توقعات نمو سوق الإلكترونيات الطبية العالمي',
                        color: CHART_COLORS.textDark,
                        font: { size: 16, family: "'Noto Sans Arabic', sans-serif", weight: 'bold' }
                    },
                     tooltip: {
                        callbacks: defaultTooltipCallbacks,
                         bodyFont: { family: "'Noto Sans Arabic', sans-serif" },
                        titleFont: { family: "'Noto Sans Arabic', sans-serif" }
                    }
                }
            }
        });

    </script>
</body>
</html>
