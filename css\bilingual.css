/* Bilingual Support Styles */
/* أنماط دعم اللغتين */

/* Language Direction Support */
[dir="rtl"] {
    text-align: right;
}

[dir="ltr"] {
    text-align: left;
}

/* Font Family Support */
html[lang="en"] {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

html[lang="ar"] {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Language Toggle Component */
.language-toggle {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 4px;
    margin: 0 15px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.lang-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
    min-width: 45px;
}

.lang-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.lang-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* RTL Layout Adjustments */
[dir="rtl"] .nav-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-menu {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-buttons {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .features-grid {
    direction: rtl;
}

[dir="rtl"] .courses-grid {
    direction: rtl;
}

[dir="rtl"] .simulator-content {
    flex-direction: row-reverse;
}

/* Icon Adjustments for RTL */
[dir="rtl"] .fas.fa-chevron-right::before {
    content: "\f053"; /* chevron-left */
}

[dir="rtl"] .fas.fa-chevron-left::before {
    content: "\f054"; /* chevron-right */
}

[dir="rtl"] .fas.fa-arrow-right::before {
    content: "\f060"; /* arrow-left */
}

[dir="rtl"] .fas.fa-arrow-left::before {
    content: "\f061"; /* arrow-right */
}

/* Text Alignment */
[dir="rtl"] .section-header,
[dir="rtl"] .hero-text,
[dir="rtl"] .feature-card,
[dir="rtl"] .course-card {
    text-align: right;
}

[dir="ltr"] .section-header,
[dir="ltr"] .hero-text,
[dir="ltr"] .feature-card,
[dir="ltr"] .course-card {
    text-align: left;
}

/* Margin and Padding Adjustments */
[dir="rtl"] .nav-logo {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .nav-actions {
    margin-right: 0;
    margin-left: auto;
}

[dir="rtl"] .hero-scroll-indicator {
    right: auto;
    left: 50%;
}

/* Button Icon Spacing */
[dir="rtl"] .btn i {
    margin-left: 8px;
    margin-right: 0;
}

[dir="ltr"] .btn i {
    margin-right: 8px;
    margin-left: 0;
}

/* Feature Card Icons */
[dir="rtl"] .feature-icon {
    margin-left: 0;
    margin-right: auto;
}

[dir="ltr"] .feature-icon {
    margin-right: 0;
    margin-left: auto;
}

/* Course Card Adjustments */
[dir="rtl"] .course-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .course-modules {
    flex-direction: row-reverse;
}

/* Animation Adjustments for RTL */
[dir="rtl"] .hero-scroll-indicator {
    animation: bounceRTL 2s infinite;
}

@keyframes bounceRTL {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Language-specific Typography */
html[lang="ar"] {
    line-height: 1.8;
}

html[lang="ar"] h1,
html[lang="ar"] h2,
html[lang="ar"] h3,
html[lang="ar"] h4,
html[lang="ar"] h5,
html[lang="ar"] h6 {
    font-weight: 600;
    line-height: 1.4;
}

html[lang="ar"] p {
    line-height: 1.8;
}

html[lang="en"] {
    line-height: 1.6;
}

/* Mobile RTL Adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .mobile-menu-toggle {
        order: -1;
    }
    
    [dir="rtl"] .nav-menu {
        text-align: right;
    }
    
    [dir="rtl"] .hero-content {
        flex-direction: column;
    }
    
    [dir="rtl"] .simulator-content {
        flex-direction: column;
    }
}

/* Language Toggle Mobile */
@media (max-width: 480px) {
    .language-toggle {
        margin: 0 10px;
    }
    
    .lang-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 35px;
    }
}

/* Smooth Transitions for Language Switch */
.fade-transition {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.fade-transition.active {
    opacity: 1;
}

/* Loading State for Language Switch */
.language-loading {
    position: relative;
    overflow: hidden;
}

.language-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .language-toggle {
        border: 2px solid currentColor;
    }
    
    .lang-btn.active {
        background: currentColor;
        color: white;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .fade-transition,
    .language-loading::after {
        animation: none;
        transition: none;
    }
}

/* Print Styles */
@media print {
    .language-toggle,
    .theme-toggle,
    .mobile-menu-toggle {
        display: none;
    }
}

/* Focus Styles for Accessibility */
.lang-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.lang-btn:focus:not(:focus-visible) {
    outline: none;
}

/* Dark Mode Adjustments */
.dark-mode .language-toggle {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .lang-btn {
    color: #e2e8f0;
}

.dark-mode .lang-btn.active {
    background: #667eea;
    color: white;
}

.dark-mode .lang-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}
