
import React, { useState, useEffect } from 'react';

const OhmLawCalculator: React.FC = () => {
  const [voltage, setVoltage] = useState<string>('');
  const [current, setCurrent] = useState<string>('');
  const [resistance, setResistance] = useState<string>('');
  const [error, setError] = useState<string>('');

  const calculate = () => {
    setError('');
    const v = parseFloat(voltage);
    const i = parseFloat(current);
    const r = parseFloat(resistance);

    const numDefined = [v, i, r].filter(val => !isNaN(val)).length;

    if (numDefined < 2) {
      setError('Please enter at least two values.');
      return;
    }
    if (numDefined === 3) {
        setError('Clear one field to calculate it.');
        return;
    }

    if (isNaN(v)) { // Calculate Voltage
      if (isNaN(i) || isNaN(r)) { setError('Current and Resistance must be numbers to calculate Voltage.'); return; }
      setVoltage((i * r).toPrecision(4));
    } else if (isNaN(i)) { // Calculate Current
      if (isNaN(v) || isNaN(r)) { setError('Voltage and Resistance must be numbers to calculate Current.'); return; }
      if (r === 0) { setError('Resistance cannot be zero for current calculation (division by zero).'); return; }
      setCurrent((v / r).toPrecision(4));
    } else if (isNaN(r)) { // Calculate Resistance
      if (isNaN(v) || isNaN(i)) { setError('Voltage and Current must be numbers to calculate Resistance.'); return; }
      if (i === 0) { setError('Current cannot be zero for resistance calculation (division by zero).'); return; }
      setResistance((v / i).toPrecision(4));
    }
  };
  
  // Debounce calculation
   useEffect(() => {
    const definedCount = [voltage, current, resistance].filter(s => s.trim() !== '' && !isNaN(parseFloat(s))).length;
    if (definedCount === 2) {
        const timer = setTimeout(() => {
            calculate();
        }, 500); // Calculate after 500ms of inactivity if two fields are filled
        return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [voltage, current, resistance]);


  const handleInputChange = (setter: React.Dispatch<React.SetStateAction<string>>) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    // Allow empty, numbers, decimal point
    if (val === '' || /^-?\d*\.?\d*$/.test(val)) {
      setter(val);
    }
  };

  const clearField = (setter: React.Dispatch<React.SetStateAction<string>>) => {
    setter('');
    setError('');
  };

  const clearAll = () => {
    setVoltage('');
    setCurrent('');
    setResistance('');
    setError('');
  };

  const InputField: React.FC<{label: string, value: string, unit: string, onChange: (e: React.ChangeEvent<HTMLInputElement>) => void, onClear: () => void }> = 
    ({label, value, unit, onChange, onClear}) => (
    <div className="mb-4">
      <label className="block text-sm font-medium text-slate-700 mb-1">{label} ({unit})</label>
      <div className="flex">
        <input
          type="text"
          value={value}
          onChange={onChange}
          placeholder={`Enter ${label.toLowerCase()}`}
          className="flex-grow p-2 border border-slate-300 rounded-l-md focus:ring-sky-500 focus:border-sky-500"
        />
        {value && (
            <button onClick={onClear} className="p-2 bg-slate-200 border border-slate-300 border-l-0 rounded-r-md hover:bg-slate-300 text-slate-600 text-xs">
                Clear
            </button>
        )}
      </div>
    </div>
  );

  return (
    <div className="p-6 bg-sky-50 rounded-lg shadow-md border border-sky-200 my-6">
      <h3 className="text-xl font-semibold text-sky-700 mb-4">Ohm's Law Calculator</h3>
      <p className="text-sm text-slate-600 mb-4">Enter any two values to calculate the third. The result will appear automatically.</p>
      
      <InputField label="Voltage" value={voltage} unit="V" onChange={handleInputChange(setVoltage)} onClear={() => clearField(setVoltage)} />
      <InputField label="Current" value={current} unit="A" onChange={handleInputChange(setCurrent)} onClear={() => clearField(setCurrent)} />
      <InputField label="Resistance" value={resistance} unit="Ω" onChange={handleInputChange(setResistance)} onClear={() => clearField(setResistance)} />

      {error && <p className="text-red-600 text-sm mb-4">{error}</p>}
      
      <button
        onClick={clearAll}
        className="w-full bg-slate-500 text-white py-2 px-4 rounded-md hover:bg-slate-600 transition-colors"
      >
        Clear All
      </button>
    </div>
  );
};

export default OhmLawCalculator;
