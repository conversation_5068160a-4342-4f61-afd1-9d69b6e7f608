// Basic Concepts Module JavaScript

let currentSlide = 1;
const totalSlides = 8;
let moduleProgress = 0;

document.addEventListener('DOMContentLoaded', function() {
    initializeBasicConcepts();
    setupSlideNavigation();
    setupInteractiveElements();
    setupProgressTracking();
});

// Initialize the basic concepts module
function initializeBasicConcepts() {
    console.log('Basic Concepts module loaded');
    
    // Initialize slide presentation
    showSlide(1);
    
    // Setup interactive simulations
    initializeSimulations();
    
    // Load saved progress
    loadModuleProgress();
}

// Setup slide navigation
function setupSlideNavigation() {
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');
    const slideCounter = document.querySelector('.slide-counter');
    
    prevBtn.addEventListener('click', function() {
        if (currentSlide > 1) {
            showSlide(currentSlide - 1);
        }
    });
    
    nextBtn.addEventListener('click', function() {
        if (currentSlide < totalSlides) {
            showSlide(currentSlide + 1);
        }
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft' && currentSlide > 1) {
            showSlide(currentSlide - 1);
        } else if (e.key === 'ArrowRight' && currentSlide < totalSlides) {
            showSlide(currentSlide + 1);
        }
    });
}

// Show specific slide
function showSlide(slideNumber) {
    // Hide all slides
    document.querySelectorAll('.slide').forEach(slide => {
        slide.classList.remove('active');
    });
    
    // Show target slide
    const targetSlide = document.querySelector(`[data-slide="${slideNumber}"]`);
    if (targetSlide) {
        targetSlide.classList.add('active');
        currentSlide = slideNumber;
        
        // Update navigation
        updateSlideNavigation();
        
        // Update progress
        updateSlideProgress();
        
        // Initialize slide-specific interactions
        initializeSlideInteractions(slideNumber);
    }
}

// Update slide navigation buttons
function updateSlideNavigation() {
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');
    const slideCounter = document.querySelector('.slide-counter');
    
    prevBtn.disabled = currentSlide === 1;
    nextBtn.disabled = currentSlide === totalSlides;
    
    slideCounter.textContent = `${currentSlide} / ${totalSlides}`;
    
    // Update button text for last slide
    if (currentSlide === totalSlides) {
        nextBtn.innerHTML = 'Complete <i class="fas fa-check"></i>';
        nextBtn.onclick = completeModule;
    } else {
        nextBtn.innerHTML = 'Next <i class="fas fa-chevron-right"></i>';
        nextBtn.onclick = () => showSlide(currentSlide + 1);
    }
}

// Update slide progress
function updateSlideProgress() {
    const progress = Math.round((currentSlide / totalSlides) * 100);
    moduleProgress = Math.max(moduleProgress, progress);
    
    const progressBar = document.getElementById('moduleProgress');
    const progressText = document.querySelector('.progress-text');
    
    if (progressBar) {
        progressBar.style.width = `${moduleProgress}%`;
    }
    
    if (progressText) {
        progressText.textContent = `Progress: ${moduleProgress}%`;
    }
    
    // Save progress
    saveModuleProgress();
}

// Initialize slide-specific interactions
function initializeSlideInteractions(slideNumber) {
    switch(slideNumber) {
        case 2:
            setupCurrentSimulation();
            break;
        case 3:
            setupVoltageSimulation();
            break;
        case 4:
            setupResistanceSimulation();
            break;
        case 5:
            setupPowerSimulation();
            break;
        case 6:
            setupCircuitAnimation();
            break;
    }
}

// Setup interactive elements
function setupInteractiveElements() {
    // Current simulation
    const currentSlider = document.getElementById('currentSlider');
    if (currentSlider) {
        currentSlider.addEventListener('input', function() {
            updateCurrentSimulation(this.value);
        });
    }
    
    // Voltage simulation
    const voltageSlider = document.getElementById('voltageSlider');
    if (voltageSlider) {
        voltageSlider.addEventListener('input', function() {
            updateVoltageSimulation(this.value);
        });
    }
    
    // Resistance simulation
    const resistanceSlider = document.getElementById('resistanceSlider');
    if (resistanceSlider) {
        resistanceSlider.addEventListener('input', function() {
            updateResistanceSimulation(this.value);
        });
    }
    
    // Power simulation
    const powerVoltageSlider = document.getElementById('powerVoltageSlider');
    const powerCurrentSlider = document.getElementById('powerCurrentSlider');
    
    if (powerVoltageSlider) {
        powerVoltageSlider.addEventListener('input', function() {
            updatePowerSimulation();
        });
    }
    
    if (powerCurrentSlider) {
        powerCurrentSlider.addEventListener('input', function() {
            updatePowerSimulation();
        });
    }
}

// Initialize simulations
function initializeSimulations() {
    // Start atom animation
    startAtomAnimation();
    
    // Initialize default values
    updateCurrentSimulation(2.5);
    updateVoltageSimulation(12);
    updateResistanceSimulation(1000);
    updatePowerSimulation();
}

// Start atom animation
function startAtomAnimation() {
    const electronShells = document.querySelectorAll('.electron-shell');
    electronShells.forEach(shell => {
        shell.style.animationPlayState = 'running';
    });
}

// Current simulation
function setupCurrentSimulation() {
    const particles = document.querySelectorAll('.water-particle');
    particles.forEach((particle, index) => {
        particle.style.animationDelay = `${index * 0.7}s`;
    });
}

function updateCurrentSimulation(current) {
    const currentValue = document.getElementById('currentValue');
    const particles = document.querySelectorAll('.water-particle');
    
    if (currentValue) {
        currentValue.textContent = `${current} A`;
    }
    
    // Update particle speed based on current
    const speed = Math.max(0.5, current / 2.5); // Normalize speed
    particles.forEach(particle => {
        particle.style.animationDuration = `${2 / speed}s`;
    });
}

// Voltage simulation
function setupVoltageSimulation() {
    updateVoltageSimulation(12);
}

function updateVoltageSimulation(voltage) {
    const voltageValue = document.getElementById('voltageValue');
    const voltageLevel = document.getElementById('voltageLevel');
    
    if (voltageValue) {
        voltageValue.textContent = `${voltage} V`;
    }
    
    if (voltageLevel) {
        const percentage = (voltage / 24) * 100; // Max 24V
        voltageLevel.style.height = `${percentage}%`;
    }
}

// Resistance simulation
function setupResistanceSimulation() {
    updateResistanceSimulation(1000);
}

function updateResistanceSimulation(resistance) {
    const resistanceValue = document.getElementById('resistanceValue');
    
    if (resistanceValue) {
        resistanceValue.textContent = `${resistance} Ω`;
    }
    
    // Update color bands based on resistance value
    updateResistorColorBands(resistance);
}

function updateResistorColorBands(resistance) {
    const bands = document.querySelectorAll('.band');
    
    // Simplified color coding for demonstration
    const resistanceStr = resistance.toString().padStart(4, '0');
    const colors = {
        '0': '#000000', '1': '#8B4513', '2': '#FF0000', '3': '#FFA500',
        '4': '#FFFF00', '5': '#008000', '6': '#0000FF', '7': '#800080',
        '8': '#808080', '9': '#FFFFFF'
    };
    
    if (bands.length >= 3) {
        bands[0].style.backgroundColor = colors[resistanceStr[0]] || '#8B4513';
        bands[1].style.backgroundColor = colors[resistanceStr[1]] || '#FF0000';
        bands[2].style.backgroundColor = colors[resistanceStr[2]] || '#FFA500';
    }
}

// Power simulation
function setupPowerSimulation() {
    updatePowerSimulation();
}

function updatePowerSimulation() {
    const voltageSlider = document.getElementById('powerVoltageSlider');
    const currentSlider = document.getElementById('powerCurrentSlider');
    const powerValue = document.getElementById('powerValue');
    const powerVoltage = document.getElementById('powerVoltage');
    const powerCurrent = document.getElementById('powerCurrent');
    const bulbGlow = document.getElementById('bulbGlow');
    
    if (voltageSlider && currentSlider) {
        const voltage = parseFloat(voltageSlider.value);
        const current = parseFloat(currentSlider.value);
        const power = voltage * current;
        
        if (powerValue) {
            powerValue.textContent = `${Math.round(power)} W`;
        }
        
        if (powerVoltage) {
            powerVoltage.textContent = voltage;
        }
        
        if (powerCurrent) {
            powerCurrent.textContent = current;
        }
        
        if (bulbGlow) {
            const intensity = Math.min(power / 100, 1); // Max intensity at 100W
            bulbGlow.style.opacity = intensity;
        }
    }
}

// Circuit animation
function setupCircuitAnimation() {
    const currentFlowElements = document.querySelectorAll('.current-flow');
    const currentArrows = document.querySelectorAll('.current-arrow');
    
    currentFlowElements.forEach(element => {
        element.style.animationPlayState = 'running';
    });
    
    currentArrows.forEach(arrow => {
        arrow.style.animationPlayState = 'running';
    });
}

// Progress tracking
function setupProgressTracking() {
    // Track time spent on each slide
    let slideStartTime = Date.now();
    
    // Update time when slide changes
    const originalShowSlide = showSlide;
    showSlide = function(slideNumber) {
        const timeSpent = Date.now() - slideStartTime;
        trackSlideTime(currentSlide, timeSpent);
        slideStartTime = Date.now();
        originalShowSlide(slideNumber);
    };
}

function trackSlideTime(slideNumber, timeSpent) {
    const slideData = JSON.parse(localStorage.getItem('slideTimeData') || '{}');
    const moduleKey = 'basic-concepts';
    
    if (!slideData[moduleKey]) {
        slideData[moduleKey] = {};
    }
    
    if (!slideData[moduleKey][slideNumber]) {
        slideData[moduleKey][slideNumber] = 0;
    }
    
    slideData[moduleKey][slideNumber] += timeSpent;
    localStorage.setItem('slideTimeData', JSON.stringify(slideData));
}

// Save and load progress
function saveModuleProgress() {
    const progressData = {
        currentSlide: currentSlide,
        moduleProgress: moduleProgress,
        completedAt: currentSlide === totalSlides ? new Date().toISOString() : null,
        lastAccessed: new Date().toISOString()
    };
    
    localStorage.setItem('basicConceptsProgress', JSON.stringify(progressData));
    
    // Update overall fundamentals progress
    updateFundamentalsProgress('basic-concepts', moduleProgress);
}

function loadModuleProgress() {
    const savedProgress = localStorage.getItem('basicConceptsProgress');
    if (savedProgress) {
        const progressData = JSON.parse(savedProgress);
        moduleProgress = progressData.moduleProgress || 0;
        
        // Update progress display
        updateSlideProgress();
        
        // Optionally resume from last slide
        if (progressData.currentSlide && progressData.currentSlide > 1) {
            const resumeSlide = confirm(`Resume from slide ${progressData.currentSlide}?`);
            if (resumeSlide) {
                showSlide(progressData.currentSlide);
            }
        }
    }
}

function updateFundamentalsProgress(moduleId, progress) {
    const fundamentalsProgress = JSON.parse(localStorage.getItem('fundamentalsProgress') || '{}');
    fundamentalsProgress[moduleId] = progress;
    localStorage.setItem('fundamentalsProgress', JSON.stringify(fundamentalsProgress));
}

// Complete module
function completeModule() {
    moduleProgress = 100;
    saveModuleProgress();
    
    // Show completion modal
    showCompletionModal();
}

function showCompletionModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trophy"></i> Module Complete!</h3>
            </div>
            <div class="modal-body">
                <div class="completion-stats">
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <span>Basic Concepts Mastered</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>8 Slides Completed</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span>Ready for Next Module</span>
                    </div>
                </div>
                <p>Congratulations! You've successfully completed the Basic Electronics Concepts module. You're now ready to explore Ohm's Law.</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeCompletionModal()">Review Module</button>
                <button class="btn btn-primary" onclick="proceedToOhmsLaw()">Continue to Ohm's Law</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add completion modal styles
    if (!document.querySelector('#completion-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'completion-modal-styles';
        styles.textContent = `
            .completion-stats {
                display: flex;
                flex-direction: column;
                gap: 15px;
                margin: 20px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            
            .stat-item {
                display: flex;
                align-items: center;
                gap: 15px;
                font-size: 1.1rem;
            }
            
            .stat-item i {
                color: #4ecdc4;
                font-size: 1.3rem;
            }
        `;
        document.head.appendChild(styles);
    }
}

function closeCompletionModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

function proceedToOhmsLaw() {
    closeCompletionModal();
    window.location.href = 'ohms-law.html';
}

// Quiz functionality
function proceedToQuiz() {
    const quizSection = document.getElementById('conceptQuiz');
    if (quizSection) {
        quizSection.style.display = 'block';
        quizSection.scrollIntoView({ behavior: 'smooth' });
        initializeQuiz();
    }
}

function initializeQuiz() {
    const quizQuestions = [
        {
            question: "What is electric current?",
            options: [
                "The flow of electric charge",
                "The opposition to electron flow",
                "The potential difference between two points",
                "The rate of energy consumption"
            ],
            correct: 0,
            explanation: "Electric current is the flow of electric charge through a conductor, measured in Amperes."
        },
        {
            question: "What is the unit of voltage?",
            options: ["Amperes", "Volts", "Ohms", "Watts"],
            correct: 1,
            explanation: "Voltage is measured in Volts (V), named after Alessandro Volta."
        },
        {
            question: "Which material has the highest resistance?",
            options: ["Copper", "Silver", "Rubber", "Aluminum"],
            correct: 2,
            explanation: "Rubber is an insulator and has very high resistance to electric current."
        },
        {
            question: "What is the formula for electrical power?",
            options: ["P = I/V", "P = V/I", "P = V × I", "P = V + I"],
            correct: 2,
            explanation: "Electrical power is calculated as P = V × I (Voltage times Current)."
        }
    ];
    
    createQuizInterface(quizQuestions);
}

function createQuizInterface(questions) {
    const quizContainer = document.querySelector('.quiz-container');
    let currentQuestion = 0;
    let score = 0;
    
    function showQuestion(index) {
        const question = questions[index];
        quizContainer.innerHTML = `
            <div class="quiz-question">
                <h3>Question ${index + 1} of ${questions.length}</h3>
                <p class="question-text">${question.question}</p>
                <div class="quiz-options">
                    ${question.options.map((option, i) => `
                        <button class="quiz-option" onclick="selectAnswer(${i})" data-option="${i}">
                            ${option}
                        </button>
                    `).join('')}
                </div>
                <div class="quiz-feedback" id="quizFeedback"></div>
                <div class="quiz-navigation">
                    <button id="nextQuizQuestion" class="btn btn-primary" style="display: none;" onclick="nextQuestion()">
                        ${index === questions.length - 1 ? 'Finish Quiz' : 'Next Question'}
                    </button>
                </div>
            </div>
        `;
    }
    
    window.selectAnswer = function(selectedIndex) {
        const question = questions[currentQuestion];
        const options = document.querySelectorAll('.quiz-option');
        const feedback = document.getElementById('quizFeedback');
        const nextBtn = document.getElementById('nextQuizQuestion');
        
        // Disable all options
        options.forEach(option => option.disabled = true);
        
        // Show correct/incorrect styling
        options[selectedIndex].classList.add(selectedIndex === question.correct ? 'correct' : 'incorrect');
        if (selectedIndex !== question.correct) {
            options[question.correct].classList.add('correct');
        }
        
        // Show feedback
        feedback.innerHTML = `
            <div class="feedback-content ${selectedIndex === question.correct ? 'correct' : 'incorrect'}">
                <i class="fas fa-${selectedIndex === question.correct ? 'check' : 'times'}"></i>
                <p>${question.explanation}</p>
            </div>
        `;
        
        if (selectedIndex === question.correct) {
            score++;
        }
        
        nextBtn.style.display = 'block';
    };
    
    window.nextQuestion = function() {
        currentQuestion++;
        if (currentQuestion < questions.length) {
            showQuestion(currentQuestion);
        } else {
            showQuizResults();
        }
    };
    
    function showQuizResults() {
        const percentage = Math.round((score / questions.length) * 100);
        quizContainer.innerHTML = `
            <div class="quiz-results">
                <h3><i class="fas fa-trophy"></i> Quiz Complete!</h3>
                <div class="score-display">
                    <div class="score-circle">
                        <span class="score-percentage">${percentage}%</span>
                        <span class="score-fraction">${score}/${questions.length}</span>
                    </div>
                </div>
                <div class="quiz-feedback">
                    ${percentage >= 80 ? 
                        '<p class="success">Excellent! You have a solid understanding of basic electronics concepts.</p>' :
                        '<p class="needs-review">Consider reviewing the material before proceeding to the next module.</p>'
                    }
                </div>
                <div class="quiz-actions">
                    <button class="btn btn-secondary" onclick="retakeQuiz()">Retake Quiz</button>
                    ${percentage >= 80 ? 
                        '<button class="btn btn-primary" onclick="proceedToOhmsLaw()">Continue to Ohm\'s Law</button>' :
                        '<button class="btn btn-primary" onclick="reviewModule()">Review Module</button>'
                    }
                </div>
            </div>
        `;
        
        // Update module progress based on quiz score
        if (percentage >= 80) {
            moduleProgress = 100;
            saveModuleProgress();
        }
    }
    
    window.retakeQuiz = function() {
        currentQuestion = 0;
        score = 0;
        showQuestion(0);
    };
    
    window.reviewModule = function() {
        showSlide(1);
        document.querySelector('.slide-presentation').scrollIntoView({ behavior: 'smooth' });
    };
    
    // Start quiz
    showQuestion(0);
}

// Export functions for global access
window.showSlide = showSlide;
window.proceedToQuiz = proceedToQuiz;
window.proceedToOhmsLaw = proceedToOhmsLaw;
