/* Landing Page Styles */
/* أنماط الصفحة الرئيسية */

/* CSS Variables */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* Dark Mode Variables */
.dark-mode {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    
    --border-color: #374151;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-logo i {
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 30px;
    list-style: none;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    color: var(--text-primary);
}

.theme-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.05);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
}

.hero-title .highlight {
    background: linear-gradient(45deg, var(--accent-color), #fbbf24);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: 40px;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--accent-color);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 28px;
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.circuit-animation {
    width: 400px;
    height: 400px;
    position: relative;
}

.circuit-board {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.component {
    position: absolute;
    width: 30px;
    height: 20px;
    border-radius: 4px;
    animation: pulse 2s infinite;
}

.component.resistor {
    background: linear-gradient(45deg, #8B4513, #D2691E);
}

.component.capacitor {
    background: linear-gradient(45deg, #4169E1, #87CEEB);
}

.component.ic {
    background: linear-gradient(45deg, #2F4F4F, #708090);
    width: 40px;
    height: 25px;
}

.component.led {
    background: linear-gradient(45deg, #FF4500, #FFD700);
    border-radius: 50%;
    width: 20px;
    height: 20px;
}

.wire {
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    height: 2px;
}

.wire-1 {
    top: 30%;
    left: 20%;
    width: 60%;
    animation: currentFlow 3s infinite;
}

.wire-2 {
    top: 50%;
    left: 30%;
    width: 40%;
    animation: currentFlow 3s infinite 1s;
}

.wire-3 {
    top: 70%;
    left: 25%;
    width: 50%;
    animation: currentFlow 3s infinite 2s;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
    cursor: pointer;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features-section {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
}

.feature-card {
    background: var(--bg-primary);
    padding: 40px;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: white;
    font-size: 1.5rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Courses Section */
.courses-section {
    padding: 100px 0;
    background: var(--bg-primary);
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.course-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-xl);
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.course-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.course-card.beginner::before {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.course-card.intermediate::before {
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
}

.course-card.advanced::before {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.course-card.expert::before {
    background: linear-gradient(90deg, #ef4444, #f87171);
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.course-level {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.course-card.beginner .course-level {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.course-card.intermediate .course-level {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
}

.course-card.advanced .course-level {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.course-card.expert .course-level {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.course-duration {
    color: var(--text-light);
    font-size: 0.9rem;
}

.course-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.course-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.course-modules {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    color: var(--text-light);
    font-size: 0.9rem;
}

.course-modules span:first-child {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.course-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.course-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

@keyframes currentFlow {
    0% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.8);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    }
    100% {
        box-shadow: 0 0 0 rgba(255, 255, 255, 0.8);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        gap: 20px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .circuit-animation {
        width: 300px;
        height: 300px;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .features-grid,
    .courses-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }
    
    .btn {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
    
    .feature-card,
    .course-card {
        padding: 25px;
    }
}

/* Simulator Section */
.simulator-section {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.simulator-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.simulator-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 24px;
    color: var(--text-primary);
}

.simulator-text p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.simulator-features {
    margin-bottom: 40px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.feature-item i {
    color: var(--success-color);
    font-size: 1.1rem;
}

.simulator-preview {
    display: flex;
    justify-content: center;
    align-items: center;
}

.simulator-window {
    width: 100%;
    max-width: 500px;
    background: var(--bg-primary);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.window-header {
    background: var(--bg-tertiary);
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid var(--border-color);
}

.window-controls {
    display: flex;
    gap: 8px;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.close {
    background: #ff5f57;
}

.control.minimize {
    background: #ffbd2e;
}

.control.maximize {
    background: #28ca42;
}

.window-title {
    font-weight: 500;
    color: var(--text-primary);
}

.window-content {
    padding: 30px;
    height: 300px;
    background: #f8f9fa;
    position: relative;
}

.simulator-canvas {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.demo-circuit {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 100px;
}

.demo-component {
    position: absolute;
    border-radius: 4px;
    animation: pulse 2s infinite;
}

.demo-component.battery {
    width: 30px;
    height: 20px;
    background: linear-gradient(45deg, #333, #666);
    top: 10px;
    left: 10px;
}

.demo-component.resistor {
    width: 40px;
    height: 15px;
    background: linear-gradient(45deg, #8B4513, #D2691E);
    top: 40px;
    left: 80px;
}

.demo-component.led {
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #ff4444, #ff6666);
    border-radius: 50%;
    top: 10px;
    right: 10px;
}

.demo-wire {
    position: absolute;
    height: 2px;
    background: #333;
    top: 20px;
    left: 40px;
    width: 130px;
    animation: currentFlow 3s infinite;
}

/* Sponsors Section */
.sponsors-section {
    padding: 100px 0;
    background: var(--bg-primary);
}

.sponsors-grid {
    margin-bottom: 60px;
}

.sponsor-category {
    margin-bottom: 50px;
}

.sponsor-category h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 30px;
    text-align: center;
}

.sponsor-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.sponsor-card {
    background: var(--bg-secondary);
    padding: 30px;
    border-radius: var(--border-radius-xl);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.sponsor-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.sponsor-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.sponsor-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.sponsor-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.sponsor-benefits {
    background: var(--bg-secondary);
    padding: 50px;
    border-radius: var(--border-radius-xl);
    text-align: center;
    border: 1px solid var(--border-color);
}

.sponsor-benefits h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.sponsor-benefits p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
}

.benefit-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.benefit-item span {
    font-weight: 500;
}

/* Contact Section */
.contact-section {
    padding: 100px 0;
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 20px;
}

.contact-info p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 40px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
}

.contact-method i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.contact-method h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.contact-method p {
    color: var(--text-secondary);
    margin: 0;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 45px;
    height: 45px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.quick-actions {
    background: var(--bg-primary);
    padding: 40px;
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-color);
}

.quick-actions h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 30px;
    text-align: center;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: 2px solid transparent;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.action-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-btn.tertiary {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.action-btn.tertiary:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Footer */
.main-footer {
    background: var(--text-primary);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
}

.footer-logo i {
    font-size: 1.8rem;
    color: var(--primary-color);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 12px;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

.footer-links {
    display: flex;
    gap: 30px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* Additional Responsive Styles */
@media (max-width: 1024px) {
    .simulator-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .sponsor-logos {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .contact-content {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .social-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .sponsor-benefits {
        padding: 30px 20px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        padding: 25px;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}
