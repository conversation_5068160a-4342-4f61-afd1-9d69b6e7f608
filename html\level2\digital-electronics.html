<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Electronics - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/digital-electronics.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="module-title">Digital Electronics</div>
            <div class="progress-indicator">
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
                <span class="progress-text">Progress: 0%</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="digital-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Digital Electronics Fundamentals</h1>
                <p>Master the world of digital circuits, logic gates, and binary systems with interactive simulations and real-world biomedical applications.</p>
                <div class="hero-objectives">
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Understand binary number systems and Boolean algebra</li>
                        <li><i class="fas fa-check"></i> Master logic gates and truth tables</li>
                        <li><i class="fas fa-check"></i> Design combinational and sequential circuits</li>
                        <li><i class="fas fa-check"></i> Apply digital concepts to medical devices</li>
                        <li><i class="fas fa-check"></i> Build and simulate digital systems</li>
                    </ul>
                </div>
            </div>
            <div class="hero-visual">
                <div class="digital-showcase">
                    <div class="binary-animation">
                        <div class="binary-stream">
                            <span class="bit">1</span>
                            <span class="bit">0</span>
                            <span class="bit">1</span>
                            <span class="bit">1</span>
                            <span class="bit">0</span>
                            <span class="bit">0</span>
                            <span class="bit">1</span>
                            <span class="bit">0</span>
                        </div>
                        <div class="conversion-display">
                            <div class="binary-value">10110010₂</div>
                            <div class="decimal-value">178₁₀</div>
                            <div class="hex-value">B2₁₆</div>
                        </div>
                    </div>
                    <div class="logic-gates-preview">
                        <div class="gate-item">
                            <div class="gate-symbol and-gate">
                                <span>AND</span>
                            </div>
                            <div class="gate-inputs">
                                <div class="input high">1</div>
                                <div class="input high">1</div>
                            </div>
                            <div class="gate-output high">1</div>
                        </div>
                        <div class="gate-item">
                            <div class="gate-symbol or-gate">
                                <span>OR</span>
                            </div>
                            <div class="gate-inputs">
                                <div class="input high">1</div>
                                <div class="input low">0</div>
                            </div>
                            <div class="gate-output high">1</div>
                        </div>
                        <div class="gate-item">
                            <div class="gate-symbol not-gate">
                                <span>NOT</span>
                            </div>
                            <div class="gate-inputs">
                                <div class="input low">0</div>
                            </div>
                            <div class="gate-output high">1</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Learning Modules -->
    <section class="learning-modules">
        <div class="container">
            <h2><i class="fas fa-microchip"></i> Interactive Learning Modules</h2>
            <div class="modules-grid">
                <!-- Binary Number Systems -->
                <div class="module-card" onclick="openModule('binary-systems')">
                    <div class="module-icon">
                        <i class="fas fa-binary"></i>
                    </div>
                    <div class="module-content">
                        <h3>Binary Number Systems</h3>
                        <p>Learn binary, decimal, and hexadecimal conversions with interactive calculators</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-calculator"></i> Interactive Converter</span>
                            <span class="feature"><i class="fas fa-chart-line"></i> Visual Representations</span>
                        </div>
                    </div>
                </div>

                <!-- Logic Gates -->
                <div class="module-card" onclick="openModule('logic-gates')">
                    <div class="module-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <div class="module-content">
                        <h3>Logic Gates</h3>
                        <p>Master AND, OR, NOT, NAND, NOR, XOR gates with truth tables and simulations</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-table"></i> Truth Tables</span>
                            <span class="feature"><i class="fas fa-play"></i> Gate Simulator</span>
                        </div>
                    </div>
                </div>

                <!-- Boolean Algebra -->
                <div class="module-card" onclick="openModule('boolean-algebra')">
                    <div class="module-icon">
                        <i class="fas fa-function"></i>
                    </div>
                    <div class="module-content">
                        <h3>Boolean Algebra</h3>
                        <p>Understand Boolean laws, simplification techniques, and Karnaugh maps</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-compress-alt"></i> Simplification</span>
                            <span class="feature"><i class="fas fa-th"></i> K-Maps</span>
                        </div>
                    </div>
                </div>

                <!-- Combinational Circuits -->
                <div class="module-card" onclick="openModule('combinational-circuits')">
                    <div class="module-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="module-content">
                        <h3>Combinational Circuits</h3>
                        <p>Design encoders, decoders, multiplexers, and arithmetic circuits</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-cogs"></i> Circuit Design</span>
                            <span class="feature"><i class="fas fa-calculator"></i> Arithmetic Units</span>
                        </div>
                    </div>
                </div>

                <!-- Sequential Circuits -->
                <div class="module-card" onclick="openModule('sequential-circuits')">
                    <div class="module-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="module-content">
                        <h3>Sequential Circuits</h3>
                        <p>Learn flip-flops, counters, registers, and state machines</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-memory"></i> Memory Elements</span>
                            <span class="feature"><i class="fas fa-sync"></i> State Machines</span>
                        </div>
                    </div>
                </div>

                <!-- Medical Applications -->
                <div class="module-card" onclick="openModule('medical-applications')">
                    <div class="module-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="module-content">
                        <h3>Medical Applications</h3>
                        <p>Explore digital systems in medical devices and biomedical engineering</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-stethoscope"></i> Medical Devices</span>
                            <span class="feature"><i class="fas fa-chart-pulse"></i> Signal Processing</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Logic Gate Simulator -->
    <section class="gate-simulator">
        <div class="container">
            <h2><i class="fas fa-play"></i> Interactive Logic Gate Simulator</h2>
            <div class="simulator-content">
                <div class="gate-selection">
                    <h3>Select Logic Gate</h3>
                    <div class="gate-buttons">
                        <button type="button" class="gate-btn active" data-gate="and">AND</button>
                        <button type="button" class="gate-btn" data-gate="or">OR</button>
                        <button type="button" class="gate-btn" data-gate="not">NOT</button>
                        <button type="button" class="gate-btn" data-gate="nand">NAND</button>
                        <button type="button" class="gate-btn" data-gate="nor">NOR</button>
                        <button type="button" class="gate-btn" data-gate="xor">XOR</button>
                        <button type="button" class="gate-btn" data-gate="xnor">XNOR</button>
                    </div>
                </div>
                <div class="simulator-display">
                    <div class="gate-visual">
                        <div class="gate-inputs-section">
                            <div class="input-control">
                                <label>Input A:</label>
                                <button type="button" class="input-toggle" id="inputA" data-state="0">0</button>
                            </div>
                            <div class="input-control" id="inputBControl">
                                <label>Input B:</label>
                                <button type="button" class="input-toggle" id="inputB" data-state="0">0</button>
                            </div>
                        </div>
                        <div class="gate-symbol-display" id="gateSymbol">
                            <div class="gate-shape and-shape">
                                <span class="gate-label">AND</span>
                            </div>
                        </div>
                        <div class="gate-output-section">
                            <div class="output-display">
                                <label>Output:</label>
                                <div class="output-value" id="gateOutput">0</div>
                            </div>
                        </div>
                    </div>
                    <div class="truth-table">
                        <h4>Truth Table</h4>
                        <table id="truthTable">
                            <thead>
                                <tr>
                                    <th>A</th>
                                    <th>B</th>
                                    <th>Output</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>0</td>
                                    <td>0</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>0</td>
                                    <td>1</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>1</td>
                                    <td>0</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>1</td>
                                    <td>1</td>
                                    <td>1</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Number System Converter -->
    <section class="number-converter">
        <div class="container">
            <h2><i class="fas fa-exchange-alt"></i> Number System Converter</h2>
            <div class="converter-content">
                <div class="converter-inputs">
                    <div class="input-group">
                        <label for="decimalInput">Decimal:</label>
                        <input type="number" id="decimalInput" placeholder="Enter decimal number" min="0">
                    </div>
                    <div class="input-group">
                        <label for="binaryInput">Binary:</label>
                        <input type="text" id="binaryInput" placeholder="Enter binary number" pattern="[01]*">
                    </div>
                    <div class="input-group">
                        <label for="hexInput">Hexadecimal:</label>
                        <input type="text" id="hexInput" placeholder="Enter hex number" pattern="[0-9A-Fa-f]*">
                    </div>
                    <div class="input-group">
                        <label for="octalInput">Octal:</label>
                        <input type="text" id="octalInput" placeholder="Enter octal number" pattern="[0-7]*">
                    </div>
                </div>
                <div class="converter-display">
                    <h3>Conversion Results</h3>
                    <div class="conversion-grid">
                        <div class="conversion-item">
                            <span class="label">Decimal:</span>
                            <span class="value" id="decimalResult">0</span>
                        </div>
                        <div class="conversion-item">
                            <span class="label">Binary:</span>
                            <span class="value" id="binaryResult">0</span>
                        </div>
                        <div class="conversion-item">
                            <span class="label">Hexadecimal:</span>
                            <span class="value" id="hexResult">0</span>
                        </div>
                        <div class="conversion-item">
                            <span class="label">Octal:</span>
                            <span class="value" id="octalResult">0</span>
                        </div>
                    </div>
                    <div class="binary-breakdown" id="binaryBreakdown">
                        <!-- Binary place values will be shown here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Solved Examples -->
    <section class="solved-examples">
        <div class="container">
            <h2><i class="fas fa-lightbulb"></i> Solved Examples</h2>
            <div class="examples-grid">
                <!-- Example 1: Medical Device Logic -->
                <div class="example-card">
                    <div class="example-header">
                        <h3><i class="fas fa-heartbeat"></i> Example 1: Patient Monitor Alarm System</h3>
                    </div>
                    <div class="example-content">
                        <div class="problem-statement">
                            <h4>Problem:</h4>
                            <p>Design a logic circuit for a patient monitor that triggers an alarm when:</p>
                            <ul>
                                <li>Heart rate is too high (H = 1) OR too low (L = 1)</li>
                                <li>AND the system is enabled (E = 1)</li>
                                <li>AND NOT in maintenance mode (M = 0)</li>
                            </ul>
                        </div>
                        <div class="solution">
                            <h4>Solution:</h4>
                            <div class="logic-expression">
                                <p><strong>Boolean Expression:</strong> Alarm = (H + L) · E · M̄</p>
                            </div>
                            <div class="circuit-diagram">
                                <h5>Logic Circuit:</h5>
                                <div class="circuit-visual">
                                    <div class="logic-circuit">
                                        <div class="inputs">
                                            <div class="input-line">H</div>
                                            <div class="input-line">L</div>
                                            <div class="input-line">E</div>
                                            <div class="input-line">M</div>
                                        </div>
                                        <div class="gates">
                                            <div class="gate or-gate-ex">OR</div>
                                            <div class="gate not-gate-ex">NOT</div>
                                            <div class="gate and-gate-ex">AND</div>
                                        </div>
                                        <div class="output">
                                            <div class="output-line">Alarm</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Example 2: Binary Arithmetic -->
                <div class="example-card">
                    <div class="example-header">
                        <h3><i class="fas fa-calculator"></i> Example 2: Binary Addition in Medical Sensors</h3>
                    </div>
                    <div class="example-content">
                        <div class="problem-statement">
                            <h4>Problem:</h4>
                            <p>A temperature sensor outputs 8-bit binary values. Calculate the sum of two readings:</p>
                            <p>Reading 1: 01101100₂ (108°F)</p>
                            <p>Reading 2: 00110111₂ (55°F)</p>
                        </div>
                        <div class="solution">
                            <h4>Solution:</h4>
                            <div class="binary-addition">
                                <div class="addition-steps">
                                    <div class="step">
                                        <pre>  01101100  (108₁₀)
+ 00110111  (55₁₀)
----------
  10100011  (163₁₀)</pre>
                                    </div>
                                </div>
                                <div class="verification">
                                    <p><strong>Verification:</strong> 108 + 55 = 163</p>
                                    <p><strong>Result:</strong> 10100011₂ = 163₁₀</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../js/digital-electronics.js"></script>
</body>
</html>
