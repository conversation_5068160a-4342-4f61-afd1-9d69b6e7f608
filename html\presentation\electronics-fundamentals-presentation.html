<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electronics Fundamentals - Interactive Presentation</title>
    <link rel="stylesheet" href="../../css/presentation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="presentation-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="presentation-title">Electronics Fundamentals Presentation</div>
            <div class="presentation-controls">
                <button type="button" class="control-btn" id="fullscreenBtn">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="control-btn" id="simulationBtn">
                    <i class="fas fa-microchip"></i>
                    Simulation
                </button>
            </div>
        </div>
    </nav>

    <!-- Presentation Container -->
    <div class="presentation-container" id="presentationContainer">
        <!-- Slide Navigation -->
        <div class="slide-navigation">
            <div class="slide-counter">
                <span id="currentSlide">1</span> / <span id="totalSlides">20</span>
            </div>
            <div class="slide-controls">
                <button type="button" class="slide-btn prev-btn" id="prevBtn" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button type="button" class="slide-btn play-btn" id="playBtn">
                    <i class="fas fa-play"></i>
                </button>
                <button type="button" class="slide-btn next-btn" id="nextBtn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="slide-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <!-- Slides Container -->
        <div class="slides-wrapper">
            <!-- Slide 1: Title Slide -->
            <div class="slide active" data-slide="1">
                <div class="slide-content title-slide">
                    <div class="title-animation">
                        <h1 class="main-title">Electronics Fundamentals</h1>
                        <h2 class="subtitle">Interactive Learning Journey</h2>
                        <div class="title-icons">
                            <div class="icon-item" data-module="basic-concepts">
                                <i class="fas fa-atom"></i>
                                <span>Basic Concepts</span>
                            </div>
                            <div class="icon-item" data-module="ohms-law">
                                <i class="fas fa-calculator"></i>
                                <span>Ohm's Law</span>
                            </div>
                            <div class="icon-item" data-module="passive-components">
                                <i class="fas fa-microchip"></i>
                                <span>Passive Components</span>
                            </div>
                            <div class="icon-item" data-module="circuit-analysis">
                                <i class="fas fa-project-diagram"></i>
                                <span>Circuit Analysis</span>
                            </div>
                        </div>
                    </div>
                    <div class="animated-background">
                        <canvas id="titleCanvas"></canvas>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Basic Concepts Introduction -->
            <div class="slide" data-slide="2">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-atom"></i> Basic Electronics Concepts</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Fundamental Building Blocks</h3>
                                <ul class="animated-list">
                                    <li data-delay="0.2s">
                                        <i class="fas fa-bolt"></i>
                                        <strong>Voltage:</strong> The driving force that pushes electrons
                                    </li>
                                    <li data-delay="0.4s">
                                        <i class="fas fa-water"></i>
                                        <strong>Current:</strong> The flow of electric charge
                                    </li>
                                    <li data-delay="0.6s">
                                        <i class="fas fa-shield-alt"></i>
                                        <strong>Resistance:</strong> Opposition to current flow
                                    </li>
                                    <li data-delay="0.8s">
                                        <i class="fas fa-battery-full"></i>
                                        <strong>Power:</strong> Rate of energy consumption
                                    </li>
                                </ul>
                            </div>
                            <div class="visual-content">
                                <div class="interactive-diagram" id="basicConceptsDiagram">
                                    <div class="water-analogy">
                                        <div class="water-tank">
                                            <div class="water-level" id="waterLevel"></div>
                                            <div class="pressure-gauge">
                                                <span>Voltage</span>
                                                <div class="gauge-needle" id="voltageNeedle"></div>
                                            </div>
                                        </div>
                                        <div class="water-pipe">
                                            <div class="water-flow" id="waterFlow"></div>
                                            <div class="flow-meter">
                                                <span>Current</span>
                                                <div class="flow-indicator" id="currentIndicator"></div>
                                            </div>
                                        </div>
                                        <div class="water-valve">
                                            <div class="valve-control" id="valveControl"></div>
                                            <span>Resistance</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Voltage Deep Dive -->
            <div class="slide" data-slide="3">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-bolt"></i> Understanding Voltage</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Voltage: The Electric Potential</h3>
                                <div class="concept-explanation">
                                    <p>Voltage is the electric potential difference between two points. It's the force that drives current through a circuit.</p>
                                    <div class="voltage-examples">
                                        <div class="example-item" data-voltage="1.5">
                                            <i class="fas fa-battery-quarter"></i>
                                            <span>AA Battery: 1.5V</span>
                                        </div>
                                        <div class="example-item" data-voltage="12">
                                            <i class="fas fa-car-battery"></i>
                                            <span>Car Battery: 12V</span>
                                        </div>
                                        <div class="example-item" data-voltage="120">
                                            <i class="fas fa-plug"></i>
                                            <span>Wall Outlet: 120V</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="voltage-simulator">
                                    <div class="battery-demo">
                                        <div class="battery-visual" id="batteryVisual">
                                            <div class="positive-terminal">+</div>
                                            <div class="negative-terminal">-</div>
                                            <div class="voltage-display" id="voltageDisplay">1.5V</div>
                                        </div>
                                        <div class="electric-field">
                                            <div class="field-lines" id="fieldLines"></div>
                                        </div>
                                    </div>
                                    <div class="voltage-controls">
                                        <label>Adjust Voltage:</label>
                                        <input type="range" id="voltageSlider" min="0" max="24" value="1.5" step="0.1">
                                        <div class="voltage-readout" id="voltageReadout">1.5V</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 4: Current Flow Animation -->
            <div class="slide" data-slide="4">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-water"></i> Electric Current Flow</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Current: The Flow of Electrons</h3>
                                <div class="current-facts">
                                    <div class="fact-item">
                                        <i class="fas fa-info-circle"></i>
                                        <span>Current flows from positive to negative (conventional current)</span>
                                    </div>
                                    <div class="fact-item">
                                        <i class="fas fa-info-circle"></i>
                                        <span>Electrons actually move from negative to positive</span>
                                    </div>
                                    <div class="fact-item">
                                        <i class="fas fa-info-circle"></i>
                                        <span>Measured in Amperes (A)</span>
                                    </div>
                                </div>
                                <div class="current-types">
                                    <h4>Types of Current:</h4>
                                    <div class="type-selector">
                                        <button type="button" class="type-btn active" data-type="dc">DC Current</button>
                                        <button type="button" class="type-btn" data-type="ac">AC Current</button>
                                    </div>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="current-animation">
                                    <div class="wire-container">
                                        <div class="wire" id="currentWire">
                                            <div class="electron" style="animation-delay: 0s;"></div>
                                            <div class="electron" style="animation-delay: 0.2s;"></div>
                                            <div class="electron" style="animation-delay: 0.4s;"></div>
                                            <div class="electron" style="animation-delay: 0.6s;"></div>
                                            <div class="electron" style="animation-delay: 0.8s;"></div>
                                        </div>
                                        <div class="current-direction">
                                            <i class="fas fa-arrow-right"></i>
                                            <span>Conventional Current</span>
                                        </div>
                                        <div class="electron-direction">
                                            <i class="fas fa-arrow-left"></i>
                                            <span>Electron Flow</span>
                                        </div>
                                    </div>
                                    <div class="current-meter">
                                        <div class="meter-display">
                                            <div class="meter-needle" id="currentNeedle"></div>
                                            <div class="meter-scale">
                                                <span>0</span>
                                                <span>5</span>
                                                <span>10</span>
                                            </div>
                                        </div>
                                        <div class="meter-label">Current (mA)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 5: Ohm's Law Introduction -->
            <div class="slide" data-slide="5">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-calculator"></i> Ohm's Law</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>The Most Important Law in Electronics</h3>
                                <div class="ohms-law-explanation">
                                    <p>Ohm's Law describes the relationship between voltage, current, and resistance in electrical circuits.</p>
                                    <div class="formula-container">
                                        <div class="main-formula">V = I × R</div>
                                        <div class="formula-variations">
                                            <div class="variation">I = V / R</div>
                                            <div class="variation">R = V / I</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="ohms-triangle-interactive">
                                    <div class="triangle-container">
                                        <div class="ohms-triangle" id="ohmsTriangle">
                                            <div class="triangle-section voltage-section" data-variable="V">
                                                <span class="variable">V</span>
                                                <span class="unit">Volts</span>
                                            </div>
                                            <div class="triangle-section current-section" data-variable="I">
                                                <span class="variable">I</span>
                                                <span class="unit">Amps</span>
                                            </div>
                                            <div class="triangle-section resistance-section" data-variable="R">
                                                <span class="variable">R</span>
                                                <span class="unit">Ohms</span>
                                            </div>
                                        </div>
                                        <div class="formula-display" id="formulaDisplay">
                                            Click on any variable to see its formula
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 6: Interactive Ohm's Law Calculator -->
            <div class="slide" data-slide="6">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-calculator"></i> Interactive Ohm's Law Calculator</h2>
                    </div>
                    <div class="slide-body">
                        <div class="calculator-container">
                            <div class="calculator-inputs">
                                <div class="input-group">
                                    <label for="calcVoltage">Voltage (V):</label>
                                    <input type="number" id="calcVoltage" placeholder="Enter voltage">
                                    <span class="unit">V</span>
                                </div>
                                <div class="input-group">
                                    <label for="calcCurrent">Current (I):</label>
                                    <input type="number" id="calcCurrent" placeholder="Enter current">
                                    <span class="unit">A</span>
                                </div>
                                <div class="input-group">
                                    <label for="calcResistance">Resistance (R):</label>
                                    <input type="number" id="calcResistance" placeholder="Enter resistance">
                                    <span class="unit">Ω</span>
                                </div>
                            </div>
                            <div class="calculator-results">
                                <div class="result-display">
                                    <div class="result-item">
                                        <span class="label">Voltage:</span>
                                        <span class="value" id="resultV">--</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="label">Current:</span>
                                        <span class="value" id="resultI">--</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="label">Resistance:</span>
                                        <span class="value" id="resultR">--</span>
                                    </div>
                                    <div class="result-item">
                                        <span class="label">Power:</span>
                                        <span class="value" id="resultP">--</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 7: Passive Components Introduction -->
            <div class="slide" data-slide="7">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-microchip"></i> Passive Components</h2>
                    </div>
                    <div class="slide-body">
                        <div class="components-showcase">
                            <div class="component-grid">
                                <div class="component-card resistor-card" data-component="resistor">
                                    <div class="component-icon">
                                        <i class="fas fa-minus"></i>
                                    </div>
                                    <h3>Resistors</h3>
                                    <p>Control current flow</p>
                                    <div class="component-animation">
                                        <div class="resistor-visual">
                                            <div class="resistor-body">
                                                <div class="color-band brown"></div>
                                                <div class="color-band black"></div>
                                                <div class="color-band red"></div>
                                                <div class="color-band gold"></div>
                                            </div>
                                        </div>
                                        <div class="value-display">1kΩ ±5%</div>
                                    </div>
                                </div>
                                <div class="component-card capacitor-card" data-component="capacitor">
                                    <div class="component-icon">
                                        <i class="fas fa-battery-half"></i>
                                    </div>
                                    <h3>Capacitors</h3>
                                    <p>Store electrical energy</p>
                                    <div class="component-animation">
                                        <div class="capacitor-visual">
                                            <div class="capacitor-plates">
                                                <div class="plate positive"></div>
                                                <div class="plate negative"></div>
                                            </div>
                                            <div class="electric-field-lines"></div>
                                        </div>
                                        <div class="value-display">100μF</div>
                                    </div>
                                </div>
                                <div class="component-card inductor-card" data-component="inductor">
                                    <div class="component-icon">
                                        <i class="fas fa-circle-notch"></i>
                                    </div>
                                    <h3>Inductors</h3>
                                    <p>Store magnetic energy</p>
                                    <div class="component-animation">
                                        <div class="inductor-visual">
                                            <div class="coil"></div>
                                            <div class="magnetic-field"></div>
                                        </div>
                                        <div class="value-display">10mH</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 8: Resistance Deep Dive -->
            <div class="slide" data-slide="8">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-shield-alt"></i> Understanding Resistance</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Resistance: Opposition to Current Flow</h3>
                                <p>Resistance is the property of a material that opposes the flow of electric current. It converts electrical energy into heat.</p>
                                <div class="resistance-factors">
                                    <h4>Factors Affecting Resistance:</h4>
                                    <ul class="animated-list">
                                        <li data-delay="0.2s">
                                            <i class="fas fa-ruler"></i>
                                            <strong>Length:</strong> Longer conductors have higher resistance
                                        </li>
                                        <li data-delay="0.4s">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                            <strong>Cross-sectional Area:</strong> Larger area = lower resistance
                                        </li>
                                        <li data-delay="0.6s">
                                            <i class="fas fa-atom"></i>
                                            <strong>Material:</strong> Different materials have different resistivities
                                        </li>
                                        <li data-delay="0.8s">
                                            <i class="fas fa-thermometer-half"></i>
                                            <strong>Temperature:</strong> Usually increases resistance in metals
                                        </li>
                                    </ul>
                                </div>
                                <div class="resistance-formula">
                                    <h4>Resistance Formula:</h4>
                                    <div class="formula">R = ρL/A</div>
                                    <p>Where: ρ = resistivity, L = length, A = area</p>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="resistance-demo">
                                    <h4>Interactive Resistance Demo</h4>
                                    <div class="wire-demo">
                                        <div class="wire-container-demo">
                                            <div class="wire-cross-section">
                                                <div class="wire-material" id="wireMaterial"></div>
                                                <div class="current-particles" id="currentParticles"></div>
                                            </div>
                                            <div class="wire-controls">
                                                <div class="control-group">
                                                    <label>Length:</label>
                                                    <input type="range" id="lengthSlider" min="1" max="5" value="2" step="0.5">
                                                    <span id="lengthValue">2m</span>
                                                </div>
                                                <div class="control-group">
                                                    <label>Area:</label>
                                                    <input type="range" id="areaSlider" min="1" max="5" value="2" step="0.5">
                                                    <span id="areaValue">2mm²</span>
                                                </div>
                                                <div class="control-group">
                                                    <label>Material:</label>
                                                    <select id="materialSelect">
                                                        <option value="copper">Copper (ρ = 1.7×10⁻⁸)</option>
                                                        <option value="aluminum">Aluminum (ρ = 2.8×10⁻⁸)</option>
                                                        <option value="iron">Iron (ρ = 10×10⁻⁸)</option>
                                                        <option value="carbon">Carbon (ρ = 3500×10⁻⁸)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="resistance-result">
                                                <h5>Calculated Resistance:</h5>
                                                <div class="result-value" id="resistanceResult">1.7 mΩ</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 9: Power in Electrical Circuits -->
            <div class="slide" data-slide="9">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-bolt"></i> Electrical Power</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Power: Rate of Energy Transfer</h3>
                                <p>Electrical power is the rate at which electrical energy is transferred or converted to other forms of energy.</p>
                                <div class="power-formulas">
                                    <h4>Power Formulas:</h4>
                                    <div class="formula-grid">
                                        <div class="formula-card">
                                            <h5>Basic Power</h5>
                                            <div class="formula">P = V × I</div>
                                            <p>Power = Voltage × Current</p>
                                        </div>
                                        <div class="formula-card">
                                            <h5>Using Resistance</h5>
                                            <div class="formula">P = I²R</div>
                                            <p>Power = Current² × Resistance</p>
                                        </div>
                                        <div class="formula-card">
                                            <h5>Using Voltage</h5>
                                            <div class="formula">P = V²/R</div>
                                            <p>Power = Voltage² / Resistance</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="power-applications">
                                    <h4>Medical Device Power Considerations:</h4>
                                    <ul class="animated-list">
                                        <li data-delay="0.2s">
                                            <i class="fas fa-battery-half"></i>
                                            <strong>Battery Life:</strong> Lower power = longer operation
                                        </li>
                                        <li data-delay="0.4s">
                                            <i class="fas fa-thermometer-half"></i>
                                            <strong>Heat Generation:</strong> Power dissipated as heat
                                        </li>
                                        <li data-delay="0.6s">
                                            <i class="fas fa-shield-alt"></i>
                                            <strong>Safety:</strong> Power limits for patient contact
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="power-calculator">
                                    <h4>Interactive Power Calculator</h4>
                                    <div class="power-inputs">
                                        <div class="input-group">
                                            <label for="powerVoltage">Voltage (V):</label>
                                            <input type="number" id="powerVoltage" placeholder="Enter voltage">
                                            <span class="unit">V</span>
                                        </div>
                                        <div class="input-group">
                                            <label for="powerCurrent">Current (I):</label>
                                            <input type="number" id="powerCurrent" placeholder="Enter current">
                                            <span class="unit">A</span>
                                        </div>
                                        <div class="input-group">
                                            <label for="powerResistance">Resistance (R):</label>
                                            <input type="number" id="powerResistance" placeholder="Enter resistance">
                                            <span class="unit">Ω</span>
                                        </div>
                                    </div>
                                    <div class="power-results">
                                        <div class="result-display">
                                            <div class="result-item">
                                                <span class="label">Power (P = V×I):</span>
                                                <span class="value" id="powerVI">--</span>
                                            </div>
                                            <div class="result-item">
                                                <span class="label">Power (P = I²R):</span>
                                                <span class="value" id="powerI2R">--</span>
                                            </div>
                                            <div class="result-item">
                                                <span class="label">Power (P = V²/R):</span>
                                                <span class="value" id="powerV2R">--</span>
                                            </div>
                                            <div class="result-item">
                                                <span class="label">Energy per hour:</span>
                                                <span class="value" id="energyPerHour">--</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 10: Solved Example - Pacemaker Circuit -->
            <div class="slide" data-slide="10">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-heartbeat"></i> Solved Example: Pacemaker Circuit Design</h2>
                    </div>
                    <div class="slide-body">
                        <div class="example-container">
                            <div class="problem-statement">
                                <h3>Problem Statement</h3>
                                <div class="problem-box">
                                    <p><strong>Design Challenge:</strong> A cardiac pacemaker needs to deliver precise electrical pulses to stimulate heart muscle. The device must:</p>
                                    <ul>
                                        <li>Deliver 2mA current pulses</li>
                                        <li>Operate with a 3.6V lithium battery</li>
                                        <li>Account for tissue resistance of 500Ω</li>
                                        <li>Include safety current limiting</li>
                                        <li>Minimize power consumption for long battery life</li>
                                    </ul>
                                    <p><strong>Find:</strong> Required series resistance, power consumption, and battery life estimation.</p>
                                </div>
                            </div>
                            <div class="solution-steps">
                                <h3>Step-by-Step Solution</h3>
                                <div class="steps-container">
                                    <div class="step" data-step="1">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Calculate Total Circuit Resistance</h4>
                                            <p>Using Ohm's Law: R_total = V / I</p>
                                            <div class="calculation">
                                                <div class="calc-line">R_total = 3.6V / 0.002A = 1,800Ω</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="step" data-step="2">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Find Required Series Resistance</h4>
                                            <p>R_series = R_total - R_tissue</p>
                                            <div class="calculation">
                                                <div class="calc-line">R_series = 1,800Ω - 500Ω = 1,300Ω</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="step" data-step="3">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Calculate Power Consumption</h4>
                                            <p>P = V × I</p>
                                            <div class="calculation">
                                                <div class="calc-line">P = 3.6V × 0.002A = 7.2mW</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="step" data-step="4">
                                        <div class="step-number">4</div>
                                        <div class="step-content">
                                            <h4>Estimate Battery Life</h4>
                                            <p>Assuming 1% duty cycle (pacing 1% of time)</p>
                                            <div class="calculation">
                                                <div class="calc-line">Average Power = 7.2mW × 0.01 = 0.072mW</div>
                                                <div class="calc-line">Battery: 2.5Ah × 3.6V = 9Wh</div>
                                                <div class="calc-line">Life = 9Wh / 0.072mW = 125,000 hours ≈ 14 years</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="circuit-diagram">
                                <h3>Circuit Diagram</h3>
                                <div class="pacemaker-circuit-visual">
                                    <div class="circuit-components">
                                        <div class="battery-component">
                                            <div class="battery-symbol">3.6V</div>
                                            <span>Li Battery</span>
                                        </div>
                                        <div class="resistor-component">
                                            <div class="resistor-symbol">1.3kΩ</div>
                                            <span>Current Limiting</span>
                                        </div>
                                        <div class="tissue-component">
                                            <div class="tissue-symbol">500Ω</div>
                                            <span>Heart Tissue</span>
                                        </div>
                                        <div class="current-indicator">I = 2mA</div>
                                    </div>
                                    <div class="safety-notes">
                                        <h4>Safety Considerations:</h4>
                                        <ul>
                                            <li>Current limiting prevents tissue damage</li>
                                            <li>Pulse duration typically 0.5ms</li>
                                            <li>Backup circuits for fail-safe operation</li>
                                            <li>Biocompatible materials and encapsulation</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 11: Resistor Color Code Deep Dive -->
            <div class="slide" data-slide="11">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-palette"></i> Resistor Color Code System</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Reading Resistor Values</h3>
                                <p>Resistors use color bands to indicate their resistance value and tolerance. This system allows for compact marking on small components.</p>
                                <div class="color-code-table">
                                    <h4>Color Code Reference:</h4>
                                    <div class="color-table">
                                        <div class="color-row">
                                            <div class="color-sample black"></div>
                                            <span class="color-name">Black</span>
                                            <span class="color-value">0</span>
                                        </div>
                                        <div class="color-row">
                                            <div class="color-sample brown"></div>
                                            <span class="color-name">Brown</span>
                                            <span class="color-value">1</span>
                                        </div>
                                        <div class="color-row">
                                            <div class="color-sample red"></div>
                                            <span class="color-name">Red</span>
                                            <span class="color-value">2</span>
                                        </div>
                                        <div class="color-row">
                                            <div class="color-sample orange"></div>
                                            <span class="color-name">Orange</span>
                                            <span class="color-value">3</span>
                                        </div>
                                        <div class="color-row">
                                            <div class="color-sample yellow"></div>
                                            <span class="color-name">Yellow</span>
                                            <span class="color-value">4</span>
                                        </div>
                                        <div class="color-row">
                                            <div class="color-sample green"></div>
                                            <span class="color-name">Green</span>
                                            <span class="color-value">5</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="interactive-resistor">
                                    <h4>Interactive Resistor Reader</h4>
                                    <div class="resistor-builder">
                                        <div class="resistor-visual-large">
                                            <div class="resistor-body-large">
                                                <div class="color-band-large band1" id="band1Large"></div>
                                                <div class="color-band-large band2" id="band2Large"></div>
                                                <div class="color-band-large band3" id="band3Large"></div>
                                                <div class="color-band-large band4" id="band4Large"></div>
                                            </div>
                                            <div class="resistor-leads">
                                                <div class="lead left"></div>
                                                <div class="lead right"></div>
                                            </div>
                                        </div>
                                        <div class="band-selectors">
                                            <div class="band-selector">
                                                <label>1st Digit:</label>
                                                <select id="digit1Select" onchange="updateResistorValue()">
                                                    <option value="1" data-color="brown">1 - Brown</option>
                                                    <option value="2" data-color="red">2 - Red</option>
                                                    <option value="3" data-color="orange">3 - Orange</option>
                                                    <option value="4" data-color="yellow">4 - Yellow</option>
                                                    <option value="5" data-color="green">5 - Green</option>
                                                </select>
                                            </div>
                                            <div class="band-selector">
                                                <label>2nd Digit:</label>
                                                <select id="digit2Select" onchange="updateResistorValue()">
                                                    <option value="0" data-color="black">0 - Black</option>
                                                    <option value="1" data-color="brown">1 - Brown</option>
                                                    <option value="2" data-color="red">2 - Red</option>
                                                    <option value="3" data-color="orange">3 - Orange</option>
                                                    <option value="4" data-color="yellow">4 - Yellow</option>
                                                </select>
                                            </div>
                                            <div class="band-selector">
                                                <label>Multiplier:</label>
                                                <select id="multiplierSelect" onchange="updateResistorValue()">
                                                    <option value="1" data-color="black">×1 - Black</option>
                                                    <option value="10" data-color="brown">×10 - Brown</option>
                                                    <option value="100" data-color="red">×100 - Red</option>
                                                    <option value="1000" data-color="orange">×1K - Orange</option>
                                                </select>
                                            </div>
                                            <div class="band-selector">
                                                <label>Tolerance:</label>
                                                <select id="toleranceSelect" onchange="updateResistorValue()">
                                                    <option value="5" data-color="gold">±5% - Gold</option>
                                                    <option value="10" data-color="silver">±10% - Silver</option>
                                                    <option value="1" data-color="brown">±1% - Brown</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="resistor-value-display">
                                            <h5>Calculated Value:</h5>
                                            <div class="value-result" id="resistorValueResult">1.0kΩ ±5%</div>
                                            <div class="value-range" id="resistorRange">950Ω - 1050Ω</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 12: Capacitor Fundamentals -->
            <div class="slide" data-slide="12">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-battery-half"></i> Capacitor Fundamentals</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Capacitors: Energy Storage Devices</h3>
                                <p>Capacitors store electrical energy in an electric field between two conductive plates separated by an insulating material (dielectric).</p>
                                <div class="capacitor-properties">
                                    <h4>Key Properties:</h4>
                                    <ul class="animated-list">
                                        <li data-delay="0.2s">
                                            <i class="fas fa-bolt"></i>
                                            <strong>Capacitance (C):</strong> Ability to store charge (Farads)
                                        </li>
                                        <li data-delay="0.4s">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <strong>Voltage Rating:</strong> Maximum safe operating voltage
                                        </li>
                                        <li data-delay="0.6s">
                                            <i class="fas fa-thermometer-half"></i>
                                            <strong>Temperature Coefficient:</strong> Capacitance vs temperature
                                        </li>
                                        <li data-delay="0.8s">
                                            <i class="fas fa-wave-square"></i>
                                            <strong>ESR:</strong> Equivalent Series Resistance
                                        </li>
                                    </ul>
                                </div>
                                <div class="capacitor-equations">
                                    <h4>Important Equations:</h4>
                                    <div class="equation-grid">
                                        <div class="equation-item">
                                            <div class="equation">Q = CV</div>
                                            <p>Charge = Capacitance × Voltage</p>
                                        </div>
                                        <div class="equation-item">
                                            <div class="equation">E = ½CV²</div>
                                            <p>Energy stored in capacitor</p>
                                        </div>
                                        <div class="equation-item">
                                            <div class="equation">I = C(dV/dt)</div>
                                            <p>Current through capacitor</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="capacitor-demo">
                                    <h4>Capacitor Charging Demo</h4>
                                    <div class="charging-circuit">
                                        <div class="circuit-elements">
                                            <div class="voltage-source-cap">5V</div>
                                            <div class="switch-element" id="chargeSwitch">
                                                <div class="switch-body"></div>
                                                <div class="switch-label">Switch</div>
                                            </div>
                                            <div class="resistor-element">1kΩ</div>
                                            <div class="capacitor-element">
                                                <div class="cap-plates">
                                                    <div class="plate positive-plate"></div>
                                                    <div class="plate negative-plate"></div>
                                                </div>
                                                <div class="cap-label">100μF</div>
                                            </div>
                                        </div>
                                        <div class="charging-graph">
                                            <h5>Voltage vs Time</h5>
                                            <canvas id="chargingGraph" width="300" height="150"></canvas>
                                            <div class="time-constant">
                                                <p>τ = RC = 1kΩ × 100μF = 0.1s</p>
                                                <p>63% charge at t = τ</p>
                                            </div>
                                        </div>
                                        <div class="charging-controls">
                                            <button type="button" class="demo-btn" id="startCharging">Start Charging</button>
                                            <button type="button" class="demo-btn" id="discharge">Discharge</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 13: Inductor Fundamentals -->
            <div class="slide" data-slide="13">
                <div class="slide-content">
                    <div class="slide-header">
                        <h2><i class="fas fa-circle-notch"></i> Inductor Fundamentals</h2>
                    </div>
                    <div class="slide-body">
                        <div class="content-grid">
                            <div class="text-content">
                                <h3>Inductors: Magnetic Energy Storage</h3>
                                <p>Inductors store energy in a magnetic field created by current flowing through a coil of wire. They oppose changes in current.</p>
                                <div class="inductor-properties">
                                    <h4>Key Characteristics:</h4>
                                    <ul class="animated-list">
                                        <li data-delay="0.2s">
                                            <i class="fas fa-magnet"></i>
                                            <strong>Inductance (L):</strong> Measured in Henries (H)
                                        </li>
                                        <li data-delay="0.4s">
                                            <i class="fas fa-bolt"></i>
                                            <strong>Current Rating:</strong> Maximum continuous current
                                        </li>
                                        <li data-delay="0.6s">
                                            <i class="fas fa-wave-square"></i>
                                            <strong>Self-Resonant Frequency:</strong> Frequency where L = C
                                        </li>
                                        <li data-delay="0.8s">
                                            <i class="fas fa-thermometer-half"></i>
                                            <strong>Saturation Current:</strong> Current causing core saturation
                                        </li>
                                    </ul>
                                </div>
                                <div class="inductor-equations">
                                    <h4>Inductor Equations:</h4>
                                    <div class="equation-grid">
                                        <div class="equation-item">
                                            <div class="equation">V = L(dI/dt)</div>
                                            <p>Voltage across inductor</p>
                                        </div>
                                        <div class="equation-item">
                                            <div class="equation">E = ½LI²</div>
                                            <p>Energy stored in magnetic field</p>
                                        </div>
                                        <div class="equation-item">
                                            <div class="equation">XL = 2πfL</div>
                                            <p>Inductive reactance</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="medical-applications">
                                    <h4>Medical Applications:</h4>
                                    <ul>
                                        <li>MRI gradient coils for spatial encoding</li>
                                        <li>Switching power supplies in medical equipment</li>
                                        <li>RF coils for wireless power transfer</li>
                                        <li>EMI filters in sensitive instruments</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="visual-content">
                                <div class="inductor-demo">
                                    <h4>Magnetic Field Visualization</h4>
                                    <div class="magnetic-field-demo">
                                        <div class="coil-visualization">
                                            <div class="coil-turns">
                                                <div class="turn"></div>
                                                <div class="turn"></div>
                                                <div class="turn"></div>
                                                <div class="turn"></div>
                                                <div class="turn"></div>
                                            </div>
                                            <div class="magnetic-field-lines">
                                                <div class="field-line"></div>
                                                <div class="field-line"></div>
                                                <div class="field-line"></div>
                                                <div class="field-line"></div>
                                            </div>
                                            <div class="current-direction">
                                                <i class="fas fa-arrow-right"></i>
                                                <span>Current Flow</span>
                                            </div>
                                        </div>
                                        <div class="inductance-calculator">
                                            <h5>Inductance Calculator</h5>
                                            <div class="calc-inputs">
                                                <div class="input-group">
                                                    <label>Turns (N):</label>
                                                    <input type="number" id="turnsInput" value="100" min="1">
                                                </div>
                                                <div class="input-group">
                                                    <label>Area (cm²):</label>
                                                    <input type="number" id="areaInput" value="1" min="0.1" step="0.1">
                                                </div>
                                                <div class="input-group">
                                                    <label>Length (cm):</label>
                                                    <input type="number" id="lengthInput" value="5" min="0.1" step="0.1">
                                                </div>
                                            </div>
                                            <div class="calc-result">
                                                <p>L = μ₀N²A/l</p>
                                                <div class="inductance-value" id="inductanceValue">25.1 μH</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide Thumbnails -->
        <div class="slide-thumbnails" id="slideThumbnails">
            <!-- Thumbnails will be generated dynamically -->
        </div>
    </div>

    <!-- Simulation Workbench Modal -->
    <div class="simulation-modal" id="simulationModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-microchip"></i> Circuit Simulation Workbench</h2>
                <button type="button" class="close-btn" id="closeSimulation">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="workbench-container">
                    <div class="workbench-toolbar">
                        <div class="tool-group">
                            <button type="button" class="tool-btn active" data-tool="select">
                                <i class="fas fa-mouse-pointer"></i>
                                Select
                            </button>
                            <button type="button" class="tool-btn" data-tool="wire">
                                <i class="fas fa-minus"></i>
                                Wire
                            </button>
                            <button type="button" class="tool-btn" data-tool="delete">
                                <i class="fas fa-trash"></i>
                                Delete
                            </button>
                        </div>
                        <div class="view-group">
                            <button type="button" class="view-btn active" data-view="schematic">
                                <i class="fas fa-project-diagram"></i>
                                Schematic
                            </button>
                            <button type="button" class="view-btn" data-view="pcb">
                                <i class="fas fa-microchip"></i>
                                PCB Layout
                            </button>
                            <button type="button" class="view-btn" data-view="block">
                                <i class="fas fa-cubes"></i>
                                Block Diagram
                            </button>
                        </div>
                        <div class="action-group">
                            <button type="button" class="action-btn" id="simulateBtn">
                                <i class="fas fa-play"></i>
                                Simulate
                            </button>
                            <button type="button" class="action-btn" id="clearBtn">
                                <i class="fas fa-eraser"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                    <div class="workbench-main">
                        <div class="component-library">
                            <h3>Component Library</h3>
                            <div class="component-categories">
                                <div class="category active" data-category="basic">
                                    <h4><i class="fas fa-bolt"></i> Basic</h4>
                                    <div class="components-list">
                                        <div class="component-item" data-component="resistor">
                                            <i class="fas fa-minus"></i>
                                            <span>Resistor</span>
                                        </div>
                                        <div class="component-item" data-component="capacitor">
                                            <i class="fas fa-battery-half"></i>
                                            <span>Capacitor</span>
                                        </div>
                                        <div class="component-item" data-component="inductor">
                                            <i class="fas fa-circle-notch"></i>
                                            <span>Inductor</span>
                                        </div>
                                        <div class="component-item" data-component="voltage-source">
                                            <i class="fas fa-battery-full"></i>
                                            <span>Voltage Source</span>
                                        </div>
                                        <div class="component-item" data-component="ground">
                                            <i class="fas fa-arrow-down"></i>
                                            <span>Ground</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="design-canvas">
                            <canvas id="designCanvas" width="800" height="600"></canvas>
                            <div class="canvas-overlay">
                                <div class="grid-overlay" id="gridOverlay"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../js/presentation.js"></script>
</body>
</html>
