import React, { useState, useEffect, useRef, useCallback } from 'react';

// Main App component
const App = () => {
    // State to manage the currently active section
    const [activeSection, setActiveSection] = useState('overview');

    // Content for each section, formatted as Markdown
    const sectionsContent = {
        overview: {
            title: 'Overview of a Phototransistor',
            content: `
A **phototransistor** is a semiconductor device that is sensitive to light. It functions similarly to a regular bipolar junction transistor (BJT) but with one key difference: its base current is generated by incident light rather than an electrical input.

When light strikes the phototransistor's sensitive area (typically the base-collector junction), it generates electron-hole pairs. These charge carriers create a base current, which is then amplified by the transistor's current gain ($\beta$). This amplification makes phototransistors much more sensitive to light than photodiodes.

They are widely used in applications requiring light detection, such as optical sensors, light-activated switches, and remote controls.
            `
        },
        structure: {
            title: 'Phototransistor Structure',
            content: `
A phototransistor typically has three terminals: emitter (E), collector (C), and sometimes a base (B). Unlike a conventional transistor, the base terminal is often left open or connected to a high-impedance path, as the base current is primarily controlled by light.

Internally, it's a BJT (usually NPN) encapsulated in a transparent case or with a lens to allow light to reach the base-collector junction.

\`\`\`
      Collector (C)
            |
            |
      +-----|-----+
      |     |     |
      |  N  |  P  |  N  <-- NPN structure
      |     |     |
      +-----|-----+
            |
            |
      Emitter (E)

      Light enters through the transparent casing,
      striking the base-collector junction.
\`\`\`

The key components are:
* **Collector (N-type):** Collects electrons.
* **Base (P-type):** The light-sensitive region where electron-hole pairs are generated.
* **Emitter (N-type):** Emits electrons.

The larger area of the base-collector junction compared to a photodiode contributes to its higher sensitivity.
            `
        },
        workingModes: {
            title: 'Working Modes of a Phototransistor',
            content: `
Phototransistors operate in similar modes to conventional BJTs, but their transition between modes is controlled by light intensity.

1.  **Cutoff Region (No Light):**
    * When there is no light (or very low light) incident on the phototransistor, the base current is negligible.
    * The transistor is effectively "off," and only a small leakage current (dark current) flows through the collector-emitter path.
    * The phototransistor acts as a very high resistance.

2.  **Active Region (Varying Light):**
    * As light intensity increases, more electron-hole pairs are generated in the base, leading to an increase in base current.
    * This base current is amplified, resulting in a proportional increase in collector current ($I_C = \beta \times I_B$).
    * In this region, the phototransistor acts as a current source, where the output current is proportional to the incident light intensity. This is typically used for analog light sensing.

3.  **Saturation Region (High Light):**
    * When the light intensity is very high, the phototransistor conducts maximally.
    * The collector current reaches its maximum possible value, limited by the external circuit's resistance and supply voltage.
    * Further increases in light intensity will not significantly increase the collector current.
    * In this region, the phototransistor acts like a closed switch, often used in digital light-activated switching applications.
            `
        },
        types: {
            title: 'Different Types of Phototransistors',
            content: `
Phototransistors come in various configurations, primarily based on their internal structure and sensitivity.

1.  **Bipolar Phototransistors (NPN/PNP):**
    * Most common type. Similar to standard BJTs, but light-sensitive.
    * NPN phototransistors are more prevalent.
    * The base terminal may or may not be externally accessible. If accessible, it can be used to bias the transistor or adjust sensitivity.

2.  **Darlington Phototransistors:**
    * Consist of two cascaded transistors within a single package.
    * The collector of the first transistor is connected to the base of the second.
    * This configuration provides a very high current gain (product of individual gains), making them extremely sensitive to even very low light levels.
    * Ideal for applications requiring high sensitivity, but they tend to have a slower response time.

3.  **Photo-FETs (Photofet):**
    * Similar to a JFET, where the gate current is controlled by incident light.
    * Offer high input impedance and are less common than bipolar phototransistors.

The choice of phototransistor type depends on the specific application's requirements for sensitivity, response time, and output current.
            `
        },
        advantages: {
            title: 'Advantages Offered by Phototransistors',
            content: `
Phototransistors offer several key advantages that make them suitable for a wide range of light-sensing applications:

* **High Sensitivity:** Due to their internal current amplification (transistor gain, $\beta$), phototransistors are significantly more sensitive to light than photodiodes. A small amount of light can produce a relatively large output current.
* **High Current Output:** They can directly drive small loads (like LEDs or relays) without requiring additional amplification stages, simplifying circuit design.
* **Cost-Effective:** Generally, phototransistors are more economical than photodiodes for applications where high sensitivity and direct current output are desired.
* **Simple Circuitry:** Their ability to provide amplified current often leads to simpler external circuitry compared to photodiodes, which typically require external amplifiers.
* **Compact Size:** They are available in small packages, making them suitable for space-constrained applications.
            `
        },
        circuitSetup: {
            title: 'Phototransistor Circuit Setup',
            content: `
A basic phototransistor circuit is relatively simple. Here's a common configuration:

\`\`\`
      VCC (+5V or +12V)
        |
        R_L (Load Resistor, e.g., 1kΩ)
        |
      Collector (C)
      /
     /
    | Light  <-- Incident Light
    | Sensor
     \\
      \\
      Emitter (E)
        |
       GND

* **VCC:** Power supply voltage.
* **R_L (Load Resistor):** Connected in series with the collector. The voltage drop across this resistor will vary with the phototransistor's current, providing an output signal.
* **Phototransistor:** Connected with its collector to the load resistor and its emitter to ground (for NPN).
* **Output:** The voltage across R_L or the voltage at the collector can be used as the output signal.

**Interactive Demo:**
In this simulation, you can adjust the "Light Intensity" and observe how the "Output Voltage" (simulating the voltage across a load resistor) changes. A higher light intensity will cause the phototransistor to conduct more, leading to a lower voltage at the collector (if the load resistor is between VCC and collector) or a higher voltage across the load resistor (if the load resistor is between emitter and ground).
            `
        },
        circuitTips: {
            title: 'Tips for Building Phototransistor Circuits',
            content: `
When designing and building circuits with phototransistors, consider these tips for optimal performance:

1.  **Load Resistor Selection:** The value of the load resistor ($R_L$) is crucial.
    * A higher $R_L$ will result in a larger voltage change for a given change in light intensity, increasing sensitivity.
    * However, too high an $R_L$ can limit the maximum current and potentially cause the phototransistor to enter saturation too quickly.
    * Choose $R_L$ based on the desired output voltage swing and the maximum current the phototransistor can handle.

2.  **Dark Current Consideration:** Phototransistors have a small leakage current (dark current) even in the absence of light. This current can affect the circuit's "off" state. For precise applications, compensation or calibration might be necessary.

3.  **Ambient Light Rejection:** In applications where ambient light is a concern, consider using:
    * **Optical filters:** To block unwanted wavelengths.
    * **Modulated light sources:** Transmit a pulsed light signal and design the receiver to only respond to that specific frequency, ignoring constant ambient light.

4.  **Temperature Stability:** The characteristics of phototransistors (especially gain and dark current) can vary significantly with temperature. For critical applications, temperature compensation or stable operating environments might be required.

5.  **Base Connection (if available):** If the phototransistor has an accessible base terminal, it can be connected to a resistor to ground or a voltage divider to set a bias point, which can adjust the sensitivity or threshold. Often, it's left open.

6.  **Response Time:** Phototransistors are generally slower than photodiodes due to their internal capacitance and gain mechanism. Consider this for high-speed applications.
            `
        },
        applications: {
            title: 'Applications of Phototransistors',
            content: `
Phototransistors are versatile components used in a wide array of applications due to their light-sensing capabilities and current amplification:

1.  **Optical Sensors:**
    * **Light-activated switches:** Turning on/off lights, alarms, or other devices when light levels cross a threshold.
    * **Automatic lighting control:** Dimming or brightening lights based on ambient light.
    * **Proximity sensors:** Detecting the presence or absence of an object by sensing reflected light.

2.  **Counting and Sorting:**
    * Used in conveyor belt systems to count items passing by breaking a light beam.
    * In sorting systems, they can differentiate objects based on their optical properties.

3.  **Remote Controls:**
    * Infrared (IR) phototransistors are commonly used in IR remote control receivers (e.g., for TVs, air conditioners) to detect the IR signals emitted by the remote.

4.  **Optical Encoders:**
    * Used in rotary or linear encoders to convert mechanical motion into electrical signals, often found in robotics and motor control.

5.  **Security Systems:**
    * In intrusion detection systems, they can sense when a light beam is broken.

6.  **Medical Devices:**
    * **Pulse oximeters:** Used to measure blood oxygen saturation by detecting changes in light absorption through the skin.
    * **Infusion pumps:** For detecting fluid levels or blockages.
    * **Diagnostic equipment:** In various optical detection modules.
            `
        },
        advantagesDisadvantages: {
            title: 'Advantages and Disadvantages of Using Phototransistors',
            content: `
Like any electronic component, phototransistors come with their own set of pros and cons:

**Advantages:**
* **High Sensitivity:** Can detect very low light levels due to internal current gain.
* **High Current Output:** Can directly drive small loads without additional amplification.
* **Simple Circuitry:** Often leads to simpler and more compact designs.
* **Cost-Effective:** Generally inexpensive.

**Disadvantages:**
* **Slower Response Time:** Slower than photodiodes, making them less suitable for high-speed data transmission or very fast switching applications.
* **Temperature Dependence:** Characteristics can vary significantly with temperature, requiring compensation in some applications.
* **Higher Dark Current:** Compared to photodiodes, they have a larger leakage current when no light is present, which can affect precision.
* **Non-Linearity:** The relationship between light intensity and collector current can be less linear than that of photodiodes over a wide range.
            `
        },
        comparison: {
            title: 'Comparison: Phototransistor vs Photodiode',
            content: `
Both phototransistors and photodiodes are light-sensitive semiconductor devices, but they differ significantly in their operation and typical applications.

| Feature             | Phototransistor                                  | Photodiode                                        |
| :------------------ | :----------------------------------------------- | :------------------------------------------------ |
| **Basic Operation** | Light generates base current, which is amplified. | Light generates current directly (photovoltaic/photoconductive). |
| **Sensitivity** | High (due to internal gain, $\beta$)             | Low (no internal gain)                            |
| **Output Current** | High (can drive small loads directly)            | Low (typically requires external amplification)   |
| **Response Time** | Slower                                           | Faster                                            |
| **Dark Current** | Higher                                           | Lower                                             |
| **Linearity** | Less linear over wide range                      | More linear                                       |
| **Cost** | Generally lower                                  | Can be higher (especially for high-speed types)   |
| **Typical Use** | Light-activated switches, simple sensors, relays | High-speed optical communication, precise light measurement, medical imaging |

**Key Takeaway:** Choose a phototransistor for high sensitivity and direct load driving, and a photodiode for high speed, precision, and linearity.
            `
        },
        conclusion: {
            title: 'Conclusion',
            content: `
Phototransistors are essential components in the field of optoelectronics, offering a unique combination of light sensitivity and current amplification. Their ability to convert light energy into a significant electrical current makes them invaluable in various applications, from simple light-activated switches to complex medical diagnostic equipment.

Understanding their structure, working principles, and practical considerations is crucial for anyone involved in designing and maintaining electronic circuits, especially in sensitive areas like medical devices. While they offer high sensitivity and simplified circuit design, it's important to consider their limitations, such as slower response times and temperature dependence, to select the most appropriate component for a given task.
            `
        },
        interactiveSimulator: {
            title: 'Interactive Simulator',
            content: `
Explore the behavior of a phototransistor with this interactive simulator. You can adjust the light intensity, wavelength, and apply color filters to see how the output current changes. You can also drag the light source to affect the intensity.
            `
        }
    };

    // Component for rendering Markdown content
    const MarkdownRenderer = ({ content }) => {
        // Simple Markdown parsing for bold, italics, and code blocks
        const renderContent = (text) => {
            // Code blocks (simple, multi-line)
            text = text.replace(/```([\s\S]*?)```/g, (match, p1) => {
                return `<pre class="bg-gray-800 text-white p-3 rounded-md my-4 overflow-x-auto"><code class="language-text">${p1.trim()}</code></pre>`;
            });
            // Inline code
            text = text.replace(/`([^`]+)`/g, (match, p1) => {
                return `<code class="bg-gray-200 text-red-700 px-1 py-0.5 rounded">${p1}</code>`;
            });
            // Bold
            text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
            // Italic
            text = text.replace(/\*([^*]+)\*/g, '<em>$1</em>');
            // Headers (simple # for now)
            text = text.replace(/^#\s(.+)$/gm, '<h1 class="text-2xl font-bold my-4">$1</h1>');
            text = text.replace(/^##\s(.+)$/gm, '<h2 class="text-xl font-bold my-3">$1</h2>');
            text = text.replace(/^###\s(.+)$/gm, '<h3 class="text-lg font-bold my-2">$1</h3>');
            // Lists
            text = text.replace(/^\*\s(.+)$/gm, '<li>$1</li>');
            text = text.replace(/^(?<=\n|^)(\d+\.\s.*(?:\n\s{2,}\d+\.\s.*)*)/gm, (match) => {
                return `<ol>${match.replace(/^\d+\.\s(.+)$/gm, '<li>$1</li>')}</ol>`;
            });
            // Tables (very basic, assumes correct markdown table structure)
            text = text.replace(/^\|(.+)\|$\n^\|[ :\-]+\|(.+)\|$\n((?:^\|.*\|$\n?)+)/gm, (match, headerRow, separatorRow, dataRows) => {
                const headers = headerRow.split('|').filter(h => h.trim() !== '').map(h => `<th>${h.trim()}</th>`).join('');
                const rows = dataRows.split('\n').filter(r => r.trim() !== '').map(row => {
                    const cells = row.split('|').filter(c => c.trim() !== '').map(c => `<td>${c.trim()}</td>`).join('');
                    return `<tr>${cells}</tr>`;
                }).join('');
                return `<table class="min-w-full bg-white border border-gray-300 rounded-lg shadow-sm my-4">
                            <thead><tr>${headers}</tr></thead>
                            <tbody>${rows}</tbody>
                        </table>`;
            });

            // Replace LaTeX delimiters with a span for styling or future MathJax integration
            text = text.replace(/\$\$([\s\S]*?)\$\$/g, '<span class="latex-block block text-center my-4 text-lg font-mono">$1</span>');
            text = text.replace(/\$([^\$]+)\$/g, '<span class="latex-inline font-mono">$1</span>');

            return <div dangerouslySetInnerHTML={{ __html: text }} />;
        };

        return (
            <div className="markdown-body p-4 text-gray-800 leading-relaxed">
                {renderContent(content)}
            </div>
        );
    };

    // Full Interactive Simulator Component (replaces the simpler one)
    const FullSimulator = () => {
        const simulatorRef = useRef(null);
        const lightSourceRef = useRef(null);

        const [intensity, setIntensity] = useState(0);
        const [wavelength, setWavelength] = useState(600);
        const [filter, setFilter] = useState('none');
        const [currentOutput, setCurrentOutput] = useState(0);
        const [luxValue, setLuxValue] = useState(0);
        const [lightSourcePos, setLightSourcePos] = useState({ x: 100, y: 30 }); // Initial position

        const updateSimulation = useCallback(() => {
            let effectiveness = 1;

            // Adjust effectiveness based on wavelength (optimum around 850-950nm)
            if (wavelength >= 800 && wavelength <= 950) {
                effectiveness = 1.0;
            } else if (wavelength < 700 || wavelength > 1000) {
                effectiveness = 0.2;
            } else {
                effectiveness = 0.6;
            }

            // Apply filter effect
            if (filter === 'red' && wavelength > 630) effectiveness *= 0.8;
            if (filter === 'blue' && wavelength < 490) effectiveness *= 0.8;
            if (filter === 'infrared' && wavelength < 700) effectiveness *= 0.3;

            // Calculate output values
            const outputCurrent = Math.round(150 * (intensity / 100) * effectiveness);
            const calculatedLuxValue = Math.round(5000 * (intensity / 100));

            setCurrentOutput(outputCurrent);
            setLuxValue(calculatedLuxValue);

        }, [intensity, wavelength, filter]);

        useEffect(() => {
            updateSimulation();
        }, [intensity, wavelength, filter, updateSimulation]);

        // Draggable light source logic
        useEffect(() => {
            const simulator = simulatorRef.current;
            const lightSource = lightSourceRef.current;
            let isDragging = false;

            const handleMouseDown = () => {
                isDragging = true;
            };

            const handleMouseMove = (e) => {
                if (isDragging && simulator && lightSource) {
                    const rect = simulator.getBoundingClientRect();
                    const x = e.clientX - rect.left - lightSource.offsetWidth / 2;
                    const y = e.clientY - rect.top - lightSource.offsetHeight / 2;

                    // Keep within bounds of the simulator
                    const clampedX = Math.max(0, Math.min(x, rect.width - lightSource.offsetWidth));
                    const clampedY = Math.max(0, Math.min(y, rect.height - lightSource.offsetHeight));

                    setLightSourcePos({ x: clampedX, y: clampedY });

                    // Update simulation based on distance to device (approximate center of device-box)
                    const deviceBoxCenter = {
                        x: rect.width - 100 - (180 / 2), // right: 100px, width: 180px
                        y: rect.height - 50 - (120 / 2)  // bottom: 50px, height: 120px
                    };

                    const lightSourceCenter = {
                        x: clampedX + lightSource.offsetWidth / 2,
                        y: clampedY + lightSource.offsetHeight / 2
                    };

                    const distance = Math.sqrt(
                        Math.pow(deviceBoxCenter.x - lightSourceCenter.x, 2) +
                        Math.pow(deviceBoxCenter.y - lightSourceCenter.y, 2)
                    );

                    const maxDistance = Math.sqrt(Math.pow(rect.width, 2) + Math.pow(rect.height, 2)) / 1.5; // Adjusted max distance
                    const distanceEffect = Math.max(0, 1 - (distance / maxDistance));

                    // Adjust intensity based on distance, but don't override manual slider
                    // This is a simplified interaction. For a more robust one, we'd need to
                    // separate manual intensity from distance-based intensity.
                    // For now, let's just make dragging affect the intensity slider.
                    const newIntensity = Math.min(100, Math.max(0, Math.round(distanceEffect * 100)));
                    setIntensity(newIntensity);
                }
            };

            const handleMouseUp = () => {
                isDragging = false;
            };

            if (lightSource) {
                lightSource.addEventListener('mousedown', handleMouseDown);
            }
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            return () => {
                if (lightSource) {
                    lightSource.removeEventListener('mousedown', handleMouseDown);
                }
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };
        }, []); // Empty dependency array to run once on mount

        // Dynamic styles for the light source and device box
        const lightSourceStyle = {
            left: `${lightSourcePos.x}px`,
            top: `${lightSourcePos.y}px`,
            transform: `scale(${1 + (intensity / 100) * 0.3})`,
            background: `radial-gradient(circle, #ffeb3b, #ff9800)`,
            boxShadow: `0 0 ${25 * (intensity / 100)}px #ff9800`,
        };

        const deviceBoxBorderColor = currentOutput > 100 ? 'border-green-500' : (currentOutput > 50 ? 'border-orange-500' : 'border-blue-800');
        const deviceBoxShadow = currentOutput > 100 ? 'shadow-[0_0_15px_#4caf50]' : (currentOutput > 50 ? 'shadow-[0_0_10px_#ff9800]' : 'shadow-none');


        return (
            <div className="flex flex-col flex-1 p-6 bg-blue-50 rounded-xl shadow-inner-lg">
                <h2 className="text-2xl font-bold text-blue-800 mb-4">Phototransistor Simulation</h2>
                <div ref={simulatorRef} className="relative w-full h-[280px] bg-white rounded-lg shadow-md mb-5 overflow-hidden">
                    <div
                        ref={lightSourceRef}
                        className="absolute w-[120px] h-[120px] rounded-full cursor-grab flex items-center justify-center text-white font-bold transition-all duration-400 ease-out"
                        style={lightSourceStyle}
                    >
                        LIGHT SOURCE
                    </div>
                    <div className={`absolute w-[180px] h-[120px] bg-gray-50 border-2 ${deviceBoxBorderColor} rounded-lg bottom-[50px] right-[100px] flex flex-col items-center justify-center transition-all duration-300 ease-in-out ${deviceBoxShadow}`}>
                        <i className="fas fa-sun text-blue-700 text-6xl mb-2"></i>
                        <h3 className="text-base font-semibold text-gray-700">PHOTOTRANSISTOR</h3>
                    </div>
                    <div className="absolute bottom-5 left-5 bg-white bg-opacity-90 p-4 rounded-lg min-w-[200px] shadow-md">
                        <strong className="text-gray-800">Current Output:</strong> <span className="text-red-600 font-bold" id="currentOutput">{currentOutput} μA</span><br/>
                        <strong className="text-gray-800">Light Intensity:</strong> <span className="text-blue-600 font-bold" id="lightIntensity">{luxValue} lx</span>
                    </div>
                </div>

                <div className="flex flex-wrap justify-center gap-5 w-full">
                    <div className="bg-white p-4 rounded-lg min-w-[200px] shadow-md">
                        <label htmlFor="intensityControl" className="block mb-2 font-medium text-blue-800">Light Intensity</label>
                        <input
                            type="range"
                            min="0"
                            max="100"
                            value={intensity}
                            onChange={(e) => setIntensity(Number(e.target.value))}
                            className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="font-bold text-pink-600 text-center mt-1">{intensity}%</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg min-w-[200px] shadow-md">
                        <label htmlFor="wavelengthControl" className="block mb-2 font-medium text-blue-800">Light Wavelength</label>
                        <input
                            type="range"
                            min="400"
                            max="1000"
                            value={wavelength}
                            onChange={(e) => setWavelength(Number(e.target.value))}
                            className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                        />
                        <div className="font-bold text-pink-600 text-center mt-1">{wavelength} nm</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg min-w-[200px] shadow-md">
                        <label htmlFor="colorFilter" className="block mb-2 font-medium text-blue-800">Color Filter</label>
                        <select
                            id="colorFilter"
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md bg-white text-gray-700 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="none">No filter</option>
                            <option value="red">Red Filter</option>
                            <option value="blue">Blue Filter</option>
                            <option value="infrared">Infrared Filter</option>
                        </select>
                    </div>
                </div>
            </div>
        );
    };


    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 font-inter text-gray-900 flex flex-col">
            {/* Header */}
            <header className="bg-white shadow-md py-4 px-6 md:px-10 flex items-center justify-between">
                <h1 className="text-2xl md:text-3xl font-extrabold text-indigo-700">
                    Biomedical Device Electronics: Phototransistors
                </h1>
                <span className="text-sm text-gray-500 text-right">
                    Dr. Mohammed Yagoub Esmail <br/> Sudan University of Science and Technology, College of Engineering
                </span>
            </header>

            {/* Main Content Area */}
            <div className="flex flex-1 flex-col md:flex-row">
                {/* Sidebar Navigation */}
                <nav className="w-full md:w-64 bg-white p-4 shadow-lg md:shadow-none flex-shrink-0">
                    <div className="bg-blue-700 text-white p-3 rounded-lg my-4 text-center font-semibold text-lg shadow-md">
                        Learning Modules
                    </div>
                    <ul className="space-y-2">
                        {Object.keys(sectionsContent).map((key) => (
                            <li key={key}>
                                <button
                                    onClick={() => setActiveSection(key)}
                                    className={`w-full text-left py-2 px-3 rounded-lg transition-all duration-200 flex items-center ${
                                        activeSection === key
                                            ? 'bg-indigo-600 text-white shadow-md font-semibold'
                                            : 'text-gray-700 hover:bg-gray-100 hover:text-indigo-700'
                                    }`}
                                >
                                    {/* Dynamic icons based on section key - using Font Awesome */}
                                    {key === 'overview' && <i className="fas fa-book-open mr-2 w-6 text-center"></i>}
                                    {key === 'structure' && <i className="fas fa-microscope mr-2 w-6 text-center"></i>}
                                    {key === 'workingModes' && <i className="fas fa-lightbulb mr-2 w-6 text-center"></i>}
                                    {key === 'types' && <i className="fas fa-shapes mr-2 w-6 text-center"></i>}
                                    {key === 'advantages' && <i className="fas fa-plus-circle mr-2 w-6 text-center"></i>}
                                    {key === 'circuitSetup' && <i className="fas fa-bolt mr-2 w-6 text-center"></i>}
                                    {key === 'circuitTips' && <i className="fas fa-tools mr-2 w-6 text-center"></i>}
                                    {key === 'applications' && <i className="fas fa-heartbeat mr-2 w-6 text-center"></i>}
                                    {key === 'advantagesDisadvantages' && <i className="fas fa-balance-scale mr-2 w-6 text-center"></i>}
                                    {key === 'comparison' && <i className="fas fa-exchange-alt mr-2 w-6 text-center"></i>}
                                    {key === 'interactiveSimulator' && <i className="fas fa-vial mr-2 w-6 text-center"></i>}
                                    {key === 'conclusion' && <i className="fas fa-check-circle mr-2 w-6 text-center"></i>}
                                    {sectionsContent[key].title}
                                </button>
                            </li>
                        ))}
                    </ul>
                </nav>

                {/* Content Display Area */}
                <main className="flex-1 p-6 md:p-10 overflow-y-auto">
                    <div className="bg-white rounded-xl shadow-lg p-6 md:p-8">
                        {activeSection === 'overview' && (
                            <div className="bg-blue-50 rounded-xl p-6 mb-8 shadow-md">
                                <h2 className="text-3xl md:text-4xl font-extrabold text-indigo-800 mb-6 border-b-2 border-indigo-200 pb-3">
                                    {sectionsContent.overview.title}
                                </h2>
                                <MarkdownRenderer content={sectionsContent.overview.content} />
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mt-8">
                                    <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-transform duration-300 hover:-translate-y-1">
                                        <i className="fas fa-heartbeat text-blue-700 text-4xl mb-3"></i>
                                        <h3 className="text-lg font-semibold text-gray-800">Pulse Oximeter</h3>
                                    </div>
                                    <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-transform duration-300 hover:-translate-y-1">
                                        <i className="fas fa-syringe text-blue-700 text-4xl mb-3"></i>
                                        <h3 className="text-lg font-semibold text-gray-800">Blood Analyzer</h3>
                                    </div>
                                    <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-transform duration-300 hover:-translate-y-1">
                                        <i className="fas fa-procedures text-blue-700 text-4xl mb-3"></i>
                                        <h3 className="text-lg font-semibold text-gray-800">Patient Monitor</h3>
                                    </div>
                                    <div className="bg-white rounded-xl p-5 text-center shadow-md hover:shadow-lg transition-transform duration-300 hover:-translate-y-1">
                                        <i className="fas fa-laser text-blue-700 text-4xl mb-3"></i>
                                        <h3 className="text-lg font-semibold text-gray-800">Laser Therapy</h3>
                                    </div>
                                </div>
                                <p className="leading-relaxed mt-6 text-gray-700">
                                    Phototransistors offer significant advantages in medical device design, providing high sensitivity to light variations while being cost-effective and simple to integrate into electronic circuits. Their ability to detect low light levels makes them ideal for non-invasive medical diagnostics.
                                </p>
                            </div>
                        )}

                        {activeSection !== 'overview' && activeSection !== 'interactiveSimulator' && (
                            <>
                                <h2 className="text-3xl md:text-4xl font-extrabold text-indigo-800 mb-6 border-b-2 border-indigo-200 pb-3">
                                    {sectionsContent[activeSection].title}
                                </h2>
                                <MarkdownRenderer content={sectionsContent[activeSection].content} />
                            </>
                        )}

                        {activeSection === 'interactiveSimulator' && (
                            <FullSimulator />
                        )}
                    </div>
                </main>
            </div>

            {/* Footer */}
            <footer className="bg-blue-800 text-white text-center p-6 mt-8 border-t-4 border-yellow-400">
                <p className="text-lg mb-2">Biomedical Engineering Department - © 2023 | Sudan University of Science and Technology</p>
                <p className="text-md opacity-90">Design Principles: Safety, Reliability, Sensitivity, Biocompatibility, and Ease of Sterilization</p>
            </footer>
        </div>
    );
};

export default App;
