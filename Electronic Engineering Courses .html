<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElectroLearn - Electronic Engineering Courses</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: #f0f5ff;
            background: linear-gradient(135deg, #050820 0%, #1d1e43 100%);
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #4263eb 0%, #7947d1 100%);
            color: white;
            padding: 10px 25px;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(70, 54, 225, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(70, 54, 225, 0.4);
        }

        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(5, 8, 32, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }

        .logo {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.8rem;
            font-weight: 700;
            color: #9ec5ff;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logo-icon {
            color: #7947d1;
            margin-right: 10px;
        }

        .nav-links {
            display: flex;
            list-style: none;
        }

        .nav-links li {
            margin-left: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #c0c0c0;
            font-size: 1rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #7947d1;
        }

        .hamburger {
            display: none;
            cursor: pointer;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background: #7947d1;
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            display: flex;
            align-items: center;
            padding-top: 80px;
        }

        .hero-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .hero-text {
            width: 45%;
        }

        .hero-title {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(90deg, #7947d1 0%, #4263eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: #b0b0b0;
            margin-bottom: 30px;
        }

        .hero-cta {
            display: flex;
            gap: 15px;
        }

        .hero-svg {
            width: 45%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
        }

        .svg-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* Course Section */
        .section {
            padding: 100px 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            color: #9ec5ff;
            margin-bottom: 15px;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: #b0b0b0;
            max-width: 700px;
            margin: 0 auto;
        }

        .courses {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .course-card {
            background: #161b36;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(70, 54, 225, 0.3);
        }

        .course-img {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .course-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .course-card:hover .course-img img {
            transform: scale(1.1);
        }

        .course-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(90deg, #4263eb 0%, #7947d1 100%);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .course-content {
            padding: 25px;
        }

        .course-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            color: white;
        }

        .course-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            color: #b0b0b0;
            font-size: 0.9rem;
        }

        /* Newsletter */
        .newsletter {
            background: linear-gradient(135deg, #161b36 0%, #0f1222 100%);
            padding: 80px 0;
            text-align: center;
        }

        .newsletter-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .newsletter-title {
            margin-bottom: 15px;
            font-size: 2rem;
        }

        .newsletter-text {
            margin-bottom: 25px;
            color: #b0b0b0;
        }

        .newsletter-form {
            display: flex;
            justify-content: center;
            max-width: 500px;
            margin: 0 auto;
        }

        .newsletter-form input {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 50px 0 0 50px;
            font-size: 1rem;
            outline: none;
        }

        .newsletter-form button {
            background: linear-gradient(90deg, #4263eb 0%, #7947d1 100%);
            border: none;
            border-radius: 0 50px 50px 0;
            color: white;
            padding: 12px 30px;
            cursor: pointer;
            font-weight: 500;
            transition: background 0.3s ease;
        }

        .newsletter-form button:hover {
            background: linear-gradient(90deg, #3a54d2 0%, #6a42d0 100%);
        }

        /* Footer */
        .footer {
            background: #0f1222;
            padding: 60px 0 30px;
            backdrop-filter: blur(10px);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-logo {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.5rem;
            color: #9ec5ff;
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .footer-links h3 {
            color: #9ec5ff;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .footer-links ul {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: #b0b0b0;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #7947d1;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
        }

        .social-icons a:hover {
            background: #7947d1;
            transform: translateY(-5px);
        }

        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #707070;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .hero-content {
                flex-direction: column;
                text-align: center;
                gap: 40px;
            }
            
            .hero-text {
                width: 100%;
            }
            
            .hero-svg {
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hamburger {
                display: block;
            }
            
            .courses {
                grid-template-columns: 1fr;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
        }

        /* Circuit SVG */
        .circuit-holes {
            position: absolute;
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            grid-template-rows: repeat(6, 1fr);
            top: 0;
            right: 0;
            z-index: -1;
        }

        .hole {
            border-radius: 50%;
        }

        .hole:nth-child(1) {
            background: #4e57d4;
            grid-column: 1;
            grid-row: 5;
        }

        .hole:nth-child(2) {
            background: #3a47d2;
            grid-column: 2;
            grid-row: 2;
        }

        .hole:nth-child(3) {
            background: #4e57d4;
            grid-column: 3;
            grid-row: 1;
        }

        .hole:nth-child(4) {
            background: #3a47d2;
            grid-column: 4;
            grid-row: 3;
        }

        .hole:nth-child(5) {
            background: #4e57d4;
            grid-column: 5;
            grid-row: 5;
        }

        .hole:nth-child(6) {
            background: #3a47d2;
            grid-column: 6;
            grid-row: 4;
        }

        .vertical-lines li {
            display: inline-block;
            padding: 0 40px;
            color: #4e57d4;
            font-size: 0.9rem;
            margin: 0;
        }

        .horizontal-lines {
            height: 10px;
            background: linear-gradient(90deg, #4e57d4 0%, #7947d1 50%, #4263eb 100%);
            border-radius: 4px;
            opacity: 0.8;
            margin: 40px 0;
        }

        .electrons {
            position: absolute;
            font-size: 20px;
            color: #4e57d4;
            animation: electron 3s infinite linear;
        }

        .electrons:nth-child(1) {
            top: 30%;
            left: 50px;
            animation-delay: 0s;
        }

        .electrons:nth-child(2) {
            top: 70%;
            right: 150px;
            animation-delay: 1s;
        }

        .electrons:nth-child(3) {
            bottom: 40%;
            left: 300px;
            animation-delay: 2s;
        }

        .electrons:nth-child(4) {
            top: 20%;
            right: 200px;
            animation-delay: 0.5s;
        }

        .electrons:nth-child(5) {
            bottom: 10%;
            left: 280px;
            animation-delay: 1.5s;
        }

        @keyframes electron {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="#" class="logo">
                    <i class="fas fa-microchip logo-icon"></i>
                    ElectroLearn
                </a>
                <ul class="nav-links">
                    <li><a href="#">Home</a></li>
                    <li><a href="#">Courses</a></li>
                    <li><a href="#">Tutorials</a></li>
                    <li><a href="#">About Us</a></li>
                    <li><a href="#">Contact</a></li>
                </ul>
                <div class="hamburger">
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Master Electronic Engineering</h1>
                <p class="hero-subtitle">Learn from experts and build real-world projects with our comprehensive courses on Arduino, IoT, and more.</p>
                <div class="hero-cta">
                    <a href="#" class="btn">Explore Courses</a>
                    <a href="#" class="btn" style="background: rgba(255,255,255,0.2);">Learn More</a>
                </div>
            </div>
            <div class="hero-svg">
                <div class="svg-container">
                    <div class="circuit-holes"></div>
                    <ul class="vertical-lines">
                        <li>BEGINNER</li>
                        <li>INTERMEDIATE</li>
                        <li>ADVANCED</li>
                    </ul>
                    <div class="electrons">e</div>
                    <div class="electrons">e</div>
                    <div class="electrons">e</div>
                    <div class="electrons">e</div>
                    <div class="electrons">e</div>
                    <div class="horizontal-lines"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Courses</h2>
                <p class="section-subtitle">Browse our most popular courses to enhance your electronic engineering skills and knowledge</p>
            </div>
            <div class="courses">
                <!-- Course 1 -->
                <div class="course-card">
                    <div class="course-badge">Bestseller</div>
                    <div class="course-img">
                        <img src="https://images.unsplash.com/photo-1581092580497-e2a0d8431631?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Arduino Course">
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Arduino Programming Masterclass</h3>
                        <div class="course-info">
                            <span>12 hours</span>
                            <span>96 Lessons</span>
                        </div>
                        <p>Learn to build interactive projects and create real-world applications with Arduino.</p>
                        <a href="#" class="btn" style="margin-top: 15px;">View Course</a>
                    </div>
                </div>
                
                <!-- Course 2 -->
                <div class="course-card">
                    <div class="course-img">
                        <img src="https://images.unsplash.com/photo-1590283602690-4b4a6cfdcab1?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="Circuit Course">
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Circuit Design & PCB Layout</h3>
                        <div class="course-info">
                            <span>16 hours</span>
                            <span>80 Lessons</span>
                        </div>
                        <p>Master the art of designing circuits and creating professional PCB layouts.</p>
                        <a href="#" class="btn" style="margin-top: 15px;">View Course</a>
                    </div>
                </div>
                
                <!-- Course 3 -->
                <div class="course-card">
                    <div class="course-badge">New</div>
                    <div class="course-img">
                        <img src="https://images.unsplash.com/photo-1627163437117-4a0843c988bc?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80" alt="IoT Course">
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">IOT & Smart Devices</h3>
                        <div class="course-info">
                            <span>20 hours</span>
                            <span>110 Lessons</span>
                        </div>
                        <p>Learn to build connected devices and secure IoT systems with practical projects.</p>
                        <a href="#" class="btn" style="margin-top: 15px;">View Course</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter -->
    <section class="newsletter">
        <div class="container">
            <div class="newsletter-content">
                <h2 class="newsletter-title">Stay Updated with Latest Engineering Trends</h2>
                <p class="newsletter-text">Subscribe to our newsletter and get the latest course updates, tutorials, and industry insights delivered to your inbox.</p>
                <form class="newsletter-form">
                    <input type="email" placeholder="Your email address">
                    <button type="submit">Subscribe</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-about">
                    <div class="footer-logo">
                        <i class="fas fa-microchip logo-icon"></i>ElectroLearn
                    </div>
                    <p>Providing quality electronic engineering education since 2015.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#">Home</a></li>
                        <li><a href="#">Courses</a></li>
                        <li><a href="#">Pricing</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="#">Arduino</a></li>
                        <li><a href="#">Raspberry Pi</a></li>
                        <li><a href="#">IoT Devices</a></li>
                        <li><a href="#">Circuit Design</a></li>
                        <li><a href="#">Programming</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Support</h3>
                    <ul>
                        <li><a href="#">Help Center</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2023 ElectroLearn. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.nav-links');
        
        hamburger.addEventListener('click', () => {
            navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
        });

        // Course slide simulation (example)
        document.addEventListener('DOMContentLoaded', function() {
            const courses = document.querySelectorAll('.course-card');
            
            courses.forEach(course => {
                const viewBtn = course.querySelector('a.btn');
                viewBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // In a real implementation, you would navigate to the course page
                    // For demonstration, we'll just alert which course is clicked
                    const courseTitle = course.querySelector('.course-title').textContent;
                    alert(`You've clicked on the course: ${courseTitle}\n\nIn a real application, this would take you to the course page.`);
                });
            });
            
            // Header scroll effect
            window.addEventListener('scroll', function() {
                const header = document.querySelector('.header');
                if (window.scrollY > 50) {
                    header.style.background = 'rgba(8, 13, 43, 0.95)';
                } else {
                    header.style.background = 'rgba(5, 8, 32, 0.9)';
                }
            });
            
            // Circuit animation
            const electrons = document.querySelectorAll('.electrons');
            let electronCount = 0;
            setInterval(() => {
                electrons.forEach(electron => {
                    const randomY = Math.floor(Math.random() * 20) - 10;
                    electron.style.transform = `translate(0, ${randomY}px)`;
                });
            }, 100);
        });
    </script>
</body>
</html>
