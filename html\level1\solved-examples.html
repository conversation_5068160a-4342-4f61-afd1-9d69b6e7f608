<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solved Examples - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/solved-examples.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="module-title">Solved Examples</div>
            <div class="progress-indicator">
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
                <span class="progress-text">Progress: 0%</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="examples-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Comprehensive Solved Examples</h1>
                <p>Master electronics fundamentals through detailed step-by-step solutions of real-world biomedical engineering problems.</p>
                <div class="hero-objectives">
                    <h3>What You'll Learn</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Circuit analysis techniques with detailed solutions</li>
                        <li><i class="fas fa-check"></i> Component selection for biomedical applications</li>
                        <li><i class="fas fa-check"></i> Power calculations and safety considerations</li>
                        <li><i class="fas fa-check"></i> Filter design for medical signal processing</li>
                        <li><i class="fas fa-check"></i> Troubleshooting methodologies</li>
                    </ul>
                </div>
            </div>
            <div class="hero-visual">
                <div class="examples-showcase">
                    <div class="example-categories">
                        <div class="category-item basic">
                            <div class="category-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <h4>Basic Circuits</h4>
                            <p>15 Examples</p>
                        </div>
                        <div class="category-item intermediate">
                            <div class="category-icon">
                                <i class="fas fa-microchip"></i>
                            </div>
                            <h4>Medical Devices</h4>
                            <p>12 Examples</p>
                        </div>
                        <div class="category-item advanced">
                            <div class="category-icon">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <h4>Signal Processing</h4>
                            <p>8 Examples</p>
                        </div>
                    </div>
                    <div class="featured-example">
                        <h4>Featured Example</h4>
                        <div class="example-preview">
                            <div class="circuit-preview">
                                <div class="ecg-amplifier">ECG Amplifier Design</div>
                                <div class="gain-calculation">Gain = 1000x</div>
                                <div class="bandwidth">BW = 0.5-100Hz</div>
                            </div>
                            <p>Complete design of a 3-lead ECG amplifier with noise filtering</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Examples Categories -->
    <section class="examples-categories">
        <div class="container">
            <h2><i class="fas fa-book-open"></i> Example Categories</h2>
            <div class="categories-grid">
                <!-- Basic Circuit Analysis -->
                <div class="category-card" data-category="basic">
                    <div class="card-header">
                        <div class="card-icon basic-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h3>Basic Circuit Analysis</h3>
                        <span class="example-count">15 Examples</span>
                    </div>
                    <div class="card-content">
                        <p>Fundamental circuit analysis techniques including Ohm's Law, Kirchhoff's Laws, and basic component calculations.</p>
                        <div class="example-list">
                            <div class="example-item">
                                <span class="example-number">1</span>
                                <span class="example-title">Series Circuit with Multiple Resistors</span>
                                <span class="difficulty easy">Easy</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">2</span>
                                <span class="example-title">Parallel Resistance Calculation</span>
                                <span class="difficulty easy">Easy</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">3</span>
                                <span class="example-title">Voltage Divider Design</span>
                                <span class="difficulty medium">Medium</span>
                            </div>
                        </div>
                        <button type="button" class="explore-btn" onclick="openCategory('basic')">
                            Explore Examples <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Medical Device Circuits -->
                <div class="category-card" data-category="medical">
                    <div class="card-header">
                        <div class="card-icon medical-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h3>Medical Device Circuits</h3>
                        <span class="example-count">12 Examples</span>
                    </div>
                    <div class="card-content">
                        <p>Real-world biomedical engineering applications including ECG, pulse oximetry, and patient monitoring systems.</p>
                        <div class="example-list">
                            <div class="example-item">
                                <span class="example-number">1</span>
                                <span class="example-title">ECG Amplifier Design</span>
                                <span class="difficulty hard">Hard</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">2</span>
                                <span class="example-title">Pulse Oximeter LED Driver</span>
                                <span class="difficulty medium">Medium</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">3</span>
                                <span class="example-title">Defibrillator Energy Calculation</span>
                                <span class="difficulty hard">Hard</span>
                            </div>
                        </div>
                        <button type="button" class="explore-btn" onclick="openCategory('medical')">
                            Explore Examples <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Signal Processing -->
                <div class="category-card" data-category="signal">
                    <div class="card-header">
                        <div class="card-icon signal-icon">
                            <i class="fas fa-wave-square"></i>
                        </div>
                        <h3>Signal Processing</h3>
                        <span class="example-count">8 Examples</span>
                    </div>
                    <div class="card-content">
                        <p>Filter design, frequency response analysis, and signal conditioning for biomedical applications.</p>
                        <div class="example-list">
                            <div class="example-item">
                                <span class="example-number">1</span>
                                <span class="example-title">Low-Pass Filter for ECG</span>
                                <span class="difficulty medium">Medium</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">2</span>
                                <span class="example-title">Notch Filter for 60Hz Noise</span>
                                <span class="difficulty hard">Hard</span>
                            </div>
                            <div class="example-item">
                                <span class="example-number">3</span>
                                <span class="example-title">Amplifier Frequency Response</span>
                                <span class="difficulty medium">Medium</span>
                            </div>
                        </div>
                        <button type="button" class="explore-btn" onclick="openCategory('signal')">
                            Explore Examples <i class="fas fa-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Example Detail -->
    <section class="featured-example-detail">
        <div class="container">
            <h2><i class="fas fa-star"></i> Featured Example: ECG Amplifier Design</h2>
            <div class="example-content">
                <div class="example-text">
                    <div class="problem-statement">
                        <h3>Problem Statement</h3>
                        <p>Design a 3-lead ECG amplifier system that can amplify the small cardiac electrical signals (0.5-4mV) to a level suitable for digital processing (0-5V range) while filtering out noise and interference.</p>
                        <div class="requirements">
                            <h4>Design Requirements:</h4>
                            <ul>
                                <li>Input voltage range: 0.5mV to 4mV</li>
                                <li>Output voltage range: 0V to 5V</li>
                                <li>Frequency response: 0.5Hz to 100Hz</li>
                                <li>Input impedance: >10MΩ</li>
                                <li>Common-mode rejection: >80dB</li>
                                <li>Power supply: ±15V</li>
                            </ul>
                        </div>
                    </div>
                    <div class="solution-approach">
                        <h3>Solution Approach</h3>
                        <ol>
                            <li><strong>Calculate required gain:</strong> Gain = Vout_max / Vin_max = 5V / 4mV = 1250</li>
                            <li><strong>Design two-stage amplifier:</strong> Stage 1 (×50) + Stage 2 (×25) = ×1250</li>
                            <li><strong>Select op-amp:</strong> Choose low-noise, high-input impedance op-amp</li>
                            <li><strong>Design filters:</strong> High-pass (0.5Hz) and low-pass (100Hz)</li>
                            <li><strong>Calculate component values</strong></li>
                        </ol>
                    </div>
                </div>
                <div class="example-visual">
                    <div class="circuit-diagram">
                        <h4>Complete ECG Amplifier Circuit</h4>
                        <div class="amplifier-stages">
                            <div class="stage1">
                                <h5>Stage 1: Instrumentation Amplifier</h5>
                                <div class="stage-details">
                                    <p>Gain = 1 + (2R2/R1) = 50</p>
                                    <p>R1 = 1kΩ, R2 = 24.5kΩ</p>
                                    <p>Input Impedance > 10MΩ</p>
                                </div>
                            </div>
                            <div class="stage2">
                                <h5>Stage 2: Non-inverting Amplifier</h5>
                                <div class="stage-details">
                                    <p>Gain = 1 + (Rf/Ri) = 25</p>
                                    <p>Ri = 1kΩ, Rf = 24kΩ</p>
                                    <p>Total Gain = 50 × 25 = 1250</p>
                                </div>
                            </div>
                            <div class="filters">
                                <h5>Filters</h5>
                                <div class="filter-details">
                                    <p>High-pass: fc = 0.5Hz</p>
                                    <p>Low-pass: fc = 100Hz</p>
                                    <p>Notch: 60Hz rejection</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="performance-specs">
                        <h4>Performance Verification</h4>
                        <div class="spec-grid">
                            <div class="spec-item">
                                <span class="spec-label">Total Gain:</span>
                                <span class="spec-value">1250 (62dB)</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">Bandwidth:</span>
                                <span class="spec-value">0.5Hz - 100Hz</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">Input Impedance:</span>
                                <span class="spec-value">12MΩ</span>
                            </div>
                            <div class="spec-item">
                                <span class="spec-label">CMRR:</span>
                                <span class="spec-value">85dB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Problem Solver -->
    <section class="problem-solver">
        <div class="container">
            <h2><i class="fas fa-tools"></i> Interactive Problem Solver</h2>
            <div class="solver-content">
                <div class="problem-selector">
                    <h3>Choose a Problem Type</h3>
                    <div class="problem-types">
                        <button type="button" class="problem-btn active" data-type="ohms-law">
                            <i class="fas fa-calculator"></i>
                            Ohm's Law
                        </button>
                        <button type="button" class="problem-btn" data-type="power">
                            <i class="fas fa-bolt"></i>
                            Power Calculations
                        </button>
                        <button type="button" class="problem-btn" data-type="divider">
                            <i class="fas fa-divide"></i>
                            Voltage Divider
                        </button>
                        <button type="button" class="problem-btn" data-type="filter">
                            <i class="fas fa-filter"></i>
                            Filter Design
                        </button>
                    </div>
                </div>
                <div class="solver-interface">
                    <div class="problem-generator">
                        <h3>Generated Problem</h3>
                        <div class="problem-display" id="problemDisplay">
                            <p>A medical device requires a current-limiting resistor. If the LED operates at 2.1V and requires 20mA, and the supply voltage is 5V, what resistance value is needed?</p>
                            <div class="problem-circuit">
                                <div class="circuit-elements">
                                    <span class="voltage-source">5V</span>
                                    <span class="resistor">R = ?</span>
                                    <span class="led">LED (2.1V, 20mA)</span>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="generate-btn" onclick="generateNewProblem()">
                            Generate New Problem
                        </button>
                    </div>
                    <div class="solution-workspace">
                        <h3>Your Solution</h3>
                        <div class="solution-steps">
                            <div class="step-input">
                                <label>Step 1: Identify known values</label>
                                <textarea placeholder="List the given values and what you need to find..."></textarea>
                            </div>
                            <div class="step-input">
                                <label>Step 2: Choose appropriate formula</label>
                                <textarea placeholder="Write the formula you'll use..."></textarea>
                            </div>
                            <div class="step-input">
                                <label>Step 3: Substitute and calculate</label>
                                <textarea placeholder="Show your calculations..."></textarea>
                            </div>
                            <div class="step-input">
                                <label>Final Answer:</label>
                                <input type="text" placeholder="Enter your final answer with units">
                            </div>
                        </div>
                        <div class="solution-actions">
                            <button type="button" class="check-btn" onclick="checkSolution()">
                                Check Solution
                            </button>
                            <button type="button" class="hint-btn" onclick="showHint()">
                                Get Hint
                            </button>
                            <button type="button" class="solution-btn" onclick="showSolution()">
                                Show Solution
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../js/solved-examples.js"></script>
</body>
</html>
