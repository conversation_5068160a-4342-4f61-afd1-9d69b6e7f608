<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دائرة الإنذار ذاتي الإغلاق</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden; /* Prevent scroll on body */
        }
        .slide-container {
            background-color: #ffffff;
            border-radius: 1.5rem; /* Rounded corners */
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
            width: 95vw; /* Fluid width */
            max-width: 1200px; /* Max width for desktop */
            height: 85vh; /* Fluid height */
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
        }
        .slide {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            box-sizing: border-box;
            position: absolute;
            top: 0;
            left: 0;
            transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
            opacity: 0;
            transform: translateX(100%);
        }
        .slide.active {
            opacity: 1;
            transform: translateX(0);
            position: relative; /* Bring active slide to flow */
        }
        .slide.prev {
            transform: translateX(-100%);
        }
        .navigation-buttons {
            position: absolute;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 10;
        }
        .nav-button {
            background-color: #4f46e5;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
        }
        .nav-button:hover {
            background-color: #4338ca;
            transform: translateY(-2px);
        }
        .nav-button:disabled {
            background-color: #a7a7a7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Specific slide styling */
        .slide h1, .slide h2 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 1rem;
        }
        .slide p, .slide ul {
            color: #374151;
            font-size: 1.125rem;
            line-height: 1.75;
            max-width: 800px;
            text-align: justify;
        }
        .slide ul {
            list-style: disc;
            padding-right: 1.5rem;
            margin-top: 1rem;
        }
        .slide li {
            margin-bottom: 0.5rem;
        }
        .slide-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            text-align: center;
        }
        .slide-content.flex-row {
            flex-direction: row;
            gap: 2rem;
            justify-content: space-around;
            align-items: flex-start;
        }
        .slide-content.flex-row > div {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        .slide-content.flex-row > div p {
            text-align: center;
        }

        /* Block Diagram specific styles */
        .block-diagram {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, auto);
            gap: 1.5rem;
            align-items: center;
            justify-items: center;
            margin-top: 2rem;
            width: 80%;
            max-width: 900px;
        }
        .block {
            background-color: #d1e7dd;
            border: 2px solid #28a745;
            border-radius: 0.75rem;
            padding: 1rem;
            text-align: center;
            font-weight: 600;
            color: #155724;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            min-width: 150px;
        }
        .block.highlight {
            transform: scale(1.05);
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
        }
        .arrow {
            font-size: 2rem;
            color: #4f46e5;
            transition: color 0.3s ease;
            animation: none; /* Default state */
        }
        .arrow.animated {
            animation: pulse-arrow 1s infinite alternate;
        }
        @keyframes pulse-arrow {
            from { color: #4f46e5; transform: scale(1); }
            to { color: #8b5cf6; transform: scale(1.1); }
        }

        /* Circuit Diagram SVG styling */
        .circuit-diagram-svg {
            width: 90%;
            max-width: 800px;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 0.75rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }
        .circuit-diagram-svg path,
        .circuit-diagram-svg line {
            stroke: #333;
            stroke-width: 2;
            fill: none;
        }
        .circuit-diagram-svg rect,
        .circuit-diagram-svg circle {
            fill: #eee;
            stroke: #333;
            stroke-width: 2;
        }
        .circuit-diagram-svg text {
            font-family: 'Inter', sans-serif;
            font-size: 14px;
            fill: #333;
            text-anchor: middle;
        }
        .circuit-diagram-svg .component-label {
            font-weight: bold;
            font-size: 16px;
        }
        .circuit-diagram-svg .highlight-path {
            stroke: #ef4444; /* Red for active path */
            stroke-width: 4;
            animation: draw-path 1s forwards;
        }
        @keyframes draw-path {
            from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
            }
            to {
                stroke-dashoffset: 0;
            }
        }
        .circuit-diagram-svg .trigger-pulse {
            stroke: #22c55e; /* Green for trigger */
            stroke-width: 4;
            animation: pulse-line 0.5s forwards;
        }
        @keyframes pulse-line {
            0% { stroke-opacity: 0; }
            50% { stroke-opacity: 1; }
            100% { stroke-opacity: 0; }
        }
        .circuit-diagram-svg .reset-effect {
            stroke: #3b82f6; /* Blue for reset */
            stroke-width: 4;
            animation: fade-out 0.5s forwards;
        }
        @keyframes fade-out {
            from { stroke-opacity: 1; }
            to { stroke-opacity: 0; }
        }
        .circuit-diagram-svg .scr-gate-text {
            fill: #22c55e;
            font-weight: bold;
        }
        .circuit-diagram-svg .scr-anode-text {
            fill: #ef4444;
            font-weight: bold;
        }
        .circuit-diagram-svg .scr-cathode-text {
            fill: #3b82f6;
            font-weight: bold;
        }

        /* Icons */
        .icon-large {
            font-size: 4rem;
            color: #4f46e5;
            margin-bottom: 1rem;
        }
        .icon-medium {
            font-size: 2.5rem;
            color: #4f46e5;
            margin-bottom: 0.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .slide-container {
                width: 98vw;
                height: 90vh;
                padding: 1rem;
            }
            .slide {
                padding: 1rem;
            }
            .slide h1 {
                font-size: 1.75rem;
            }
            .slide h2 {
                font-size: 1.5rem;
            }
            .slide p, .slide ul {
                font-size: 0.95rem;
                line-height: 1.5;
            }
            .slide-content.flex-row {
                flex-direction: column;
                gap: 1rem;
                align-items: center;
            }
            .block-diagram {
                grid-template-columns: 1fr; /* Stack blocks on small screens */
                grid-template-rows: auto;
                gap: 1rem;
                width: 95%;
            }
            .arrow {
                font-size: 1.5rem;
                transform: rotate(90deg); /* Rotate arrows for vertical flow */
            }
            .circuit-diagram-svg {
                width: 100%;
            }
            .nav-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="slide active" id="slide-1">
            <div class="slide-content">
                <i class="fas fa-heartbeat icon-large mb-4"></i>
                <h1 class="text-4xl font-bold mb-4">أساسيات تصميم وصيانة الدوائر الإلكترونية في الأجهزة الطبية</h1>
                <h2 class="text-2xl font-semibold text-gray-700">المستوى الأول الحلقة 4: دائرة "إغلاق ذاتي للإنذار" (Self-Latching Alarm Circuit)</h2>
                <p class="text-lg mt-6 text-gray-600">تقديم: د. محمد يعقوب إسماعيل</p>
                <p class="text-md text-gray-500">جامعة السودان للعلوم والتكنولوجيا، كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
            </div>
        </div>

        <div class="slide" id="slide-2">
            <div class="slide-content">
                <i class="fas fa-bell icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">مقدمة ومفاهيم أساسية</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">ما هي دائرة "إغلاق ذاتي للإنذار"؟</h3>
                        <p class="text-right">الفكرة الأساسية هي أنه بمجرد تفعيل الإنذار بسبب حدوث حالة معينة (مثل تجاوز درجة حرارة، انخفاض ضغط، انفصال قطب، إلخ)، يظل الإنذار فعالاً (صوتياً أو بصرياً) حتى لو زالت الحالة المسببة للإنذار. ولا يتم إيقاف الإنذار إلا بتدخل يدوي (مثل الضغط على زر إعادة تعيين "Reset").</p>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">أهميتها في الأجهزة الطبية:</h3>
                        <ul class="list-disc pr-6 text-right">
                            <li>ضمان الانتباه للحالات الحرجة، حتى لو كانت مؤقتة.</li>
                            <li>منع التجاهل التلقائي للإنذارات.</li>
                            <li>تسجيل الأحداث (بشكل غير مباشر).</li>
                            <li>السلامة أولاً: لا يمكن التهاون مع أي إنذار يتعلق بسلامة المريض.</li>
                        </ul>
                    </div>
                </div>
                <h3 class="text-2xl font-semibold mt-6 text-right">المفاهيم الأساسية التي سنغطيها:</h3>
                <ul class="list-disc pr-6 text-right w-full md:w-3/4">
                    <li>مبدأ الإغلاق الذاتي (Latching).</li>
                    <li>استخدام الثايرستور (SCR) كعنصر إغلاق ذاتي.</li>
                    <li>استخدام زوج من الترانزستورات BJT لإنشاء وظيفة إغلاق ذاتي.</li>
                    <li>تصميم دائرة إعادة تعيين (Reset) للإنذار.</li>
                    <li>دمج الدائرة مع مدخلات من حساسات أو دوائر أخرى.</li>
                </ul>
            </div>
        </div>

        <div class="slide" id="slide-3">
            <div class="slide-content">
                <i class="fas fa-bolt icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">النظرية الرئيسية: الثايرستور (SCR)</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">النظرية:</h3>
                        <p class="text-right">الـ SCR (Silicon Controlled Rectifier) هو مكون شبه موصل رباعي الطبقات (PNPN) له ثلاثة أطراف: المصعد (Anode - A)، المهبط (Cathode - K)، والبوابة (Gate - G).</p>
                        <img src="https://placehold.co/200x150/ADD8E6/000000?text=SCR+Symbol" alt="SCR Symbol" class="mx-auto my-4 rounded-lg shadow-md">
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">آلية العمل:</h3>
                        <ul class="list-disc pr-6 text-right">
                            <li><strong class="text-blue-600">حالة القطع (OFF):</strong> لا يمر تيار بين المصعد والمهبط.</li>
                            <li><strong class="text-green-600">التحفيز (Trigger):</strong> نبضة جهد موجبة صغيرة على البوابة تحول الـ SCR إلى حالة التوصيل (ON).</li>
                            <li><strong class="text-red-600">الإغلاق الذاتي (Latching):</strong> بمجرد التوصيل، يظل الـ SCR في حالة ON حتى لو تمت إزالة نبضة البوابة، طالما أن التيار المار عبره أعلى من "تيار الإمساك" (Holding Current - Ih).</li>
                            <li><strong class="text-purple-600">إعادة التعيين (Reset):</strong> لإيقاف الـ SCR، يجب أن ينخفض التيار المار عبره إلى ما دون تيار الإمساك (عادة بقطع التغذية مؤقتاً).</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-4">
            <div class="slide-content">
                <i class="fas fa-microchip icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">النظرية الرئيسية: زوج من الترانزستورات BJT (بديل)</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">النظرية:</h3>
                        <p class="text-right">يمكن توصيل ترانزستورين (عادة NPN و PNP) بطريقة تخلق حالتين مستقرتين (ON أو OFF) بسبب التغذية الراجعة الإيجابية بينهما، مما يحاكي وظيفة الإغلاق الذاتي.</p>
                        <img src="https://placehold.co/200x150/ADD8E6/000000?text=BJT+Pair+Concept" alt="BJT Pair Concept" class="mx-auto my-4 rounded-lg shadow-md">
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2 text-right">آلية العمل:</h3>
                        <ul class="list-disc pr-6 text-right">
                            <li><strong class="text-green-600">التحفيز (Trigger):</strong> نبضة قصيرة على قاعدة أحد الترانزستورات تجعله يبدأ في التوصيل.</li>
                            <li><strong class="text-red-600">الإغلاق الذاتي:</strong> حالة توصيل أحد الترانزستورات تعزز حالة الآخر، مما يجعل الدائرة "تستقر" في حالة الإنذار النشط حتى لو زال التحفيز الأولي.</li>
                            <li><strong class="text-purple-600">إعادة التعيين (Reset):</strong> يتم عادةً بقطع التغذية الراجعة مؤقتاً أو إجبار أحد الترانزستورات على الدخول في حالة القطع.</li>
                        </ul>
                        <p class="text-right mt-4 text-gray-700">سنركز في المخطط الكهربائي على دائرة الـ SCR لبساطتها وفعاليتها في هذا التطبيق.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-5">
            <div class="slide-content">
                <h2 class="text-3xl font-bold mb-8">المخطط الصندوقي: دائرة إنذار ذاتي الإغلاق</h2>
                <div class="block-diagram">
                    <div class="block" id="sensor-input">مدخل من الحساس</div>
                    <i class="fas fa-arrow-left arrow" id="arrow-1"></i>
                    <div class="block" id="latch-circuit">دائرة الإغلاق الذاتي</div>

                    <div class="block" id="power-source">مصدر تغذية</div>
                    <i class="fas fa-arrow-up arrow" id="arrow-2"></i>
                    <i class="fas fa-arrow-down arrow" id="arrow-3"></i>

                    <div class="block" id="reset-input">مفتاح إعادة التعيين</div>
                    <i class="fas fa-arrow-left arrow" id="arrow-4"></i>
                    <div class="block" id="alarm-output">خرج الإنذار</div>
                </div>
                <button class="nav-button mt-8" onclick="animateBlockDiagram()">تشغيل تدفق الدائرة</button>
            </div>
        </div>

        <div class="slide" id="slide-6">
            <div class="slide-content">
                <h2 class="text-3xl font-bold mb-8">المخطط الكهربائي: دائرة إنذار ذاتي الإغلاق باستخدام SCR</h2>
                <svg class="circuit-diagram-svg" viewBox="0 0 800 500">
                    <text x="100" y="50" class="component-label">+Vs</text>
                    <line x1="100" y1="60" x2="100" y2="100" stroke="#333" stroke-width="2"/>
                    <text x="100" y="450" class="component-label">GND</text>
                    <line x1="100" y1="440" x2="100" y2="400" stroke="#333" stroke-width="2"/>
                    <line x1="80" y1="450" x2="120" y2="450" stroke="#333" stroke-width="2"/>
                    <line x1="90" y1="460" x2="110" y2="460" stroke="#333" stroke-width="2"/>

                    <rect x="150" y="90" width="50" height="30" rx="5" ry="5" fill="#f8d7da" stroke="#dc3545" stroke-width="2"/>
                    <line x1="175" y1="90" x2="175" y2="120" stroke="#dc3545" stroke-width="2"/>
                    <line x1="160" y1="95" x2="190" y2="115" stroke="#dc3545" stroke-width="2"/>
                    <text x="175" y="140" class="component-label">SW_Reset (NC)</text>
                    <line x1="100" y1="100" x2="150" y2="100" stroke="#333" stroke-width="2" id="path-vcc-reset"/>
                    <line x1="200" y1="100" x2="250" y2="100" stroke="#333" stroke-width="2" id="path-reset-scr-anode"/>

                    <path d="M250 100 L250 250 L300 200 L250 150 Z" stroke="#333" stroke-width="2" fill="#e0f7fa"/>
                    <line x1="250" y1="200" x2="300" y2="200" stroke="#333" stroke-width="2"/> <text x="275" y="170" class="component-label">SCR</text>
                    <text x="230" y="110" class="scr-anode-text">A</text>
                    <text x="230" y="240" class="scr-cathode-text">K</text>
                    <text x="310" y="205" class="scr-gate-text">G</text>

                    <rect x="350" y="190" width="50" height="20" rx="5" ry="5" fill="#f0f0f0" stroke="#333" stroke-width="2"/>
                    <text x="375" y="230" class="component-label">R_gate (1kΩ)</text>
                    <line x1="300" y1="200" x2="350" y2="200" stroke="#333" stroke-width="2" id="path-scr-gate-resistor"/>
                    <line x1="400" y1="200" x2="450" y2="200" stroke="#333" stroke-width="2" id="path-resistor-trigger"/>

                    <rect x="450" y="190" width="50" height="30" rx="5" ry="5" fill="#d4edda" stroke="#28a745" stroke-width="2"/>
                    <line x1="475" y1="190" x2="475" y2="220" stroke="#28a745" stroke-width="2"/>
                    <line x1="460" y1="195" x2="490" y2="215" stroke="#28a745" stroke-width="2"/>
                    <text x="475" y="240" class="component-label">SW_Trigger (NO)</text>
                    <line x1="500" y1="200" x2="550" y2="200" stroke="#333" stroke-width="2" id="path-trigger-vcc"/>
                    <line x1="550" y1="200" x2="550" y2="100" stroke="#333" stroke-width="2"/>
                    <line x1="550" y1="100" x2="100" y2="100" stroke="#333" stroke-width="2"/>

                    <circle cx="275" cy="300" r="10" fill="#ef4444" stroke="#ef4444" stroke-width="2"/>
                    <line x1="275" y1="290" x2="275" y2="310" stroke="#ef4444" stroke-width="2"/>
                    <line x1="265" y1="300" x2="285" y2="300" stroke="#ef4444" stroke-width="2"/>
                    <text x="275" y="330" class="component-label">Alarm LED</text>
                    <line x1="250" y1="250" x2="275" y2="250" stroke="#333" stroke-width="2" id="path-scr-cathode-led"/>
                    <line x1="275" y1="250" x2="275" y2="290" stroke="#333" stroke-width="2"/>

                    <rect x="250" y="350" width="50" height="20" rx="5" ry="5" fill="#f0f0f0" stroke="#333" stroke-width="2"/>
                    <text x="275" y="390" class="component-label">R_LED (470Ω)</text>
                    <line x1="275" y1="310" x2="275" y2="350" stroke="#333" stroke-width="2" id="path-led-resistor"/>
                    <line x1="275" y1="370" x2="275" y2="400" stroke="#333" stroke-width="2" id="path-resistor-gnd"/>
                    <line x1="275" y1="400" x2="100" y2="400" stroke="#333" stroke-width="2"/>

                    <rect x="450" y="190" width="50" height="30" rx="5" ry="5" fill-opacity="0" onclick="triggerCircuit()" style="cursor: pointer;"/>
                    <rect x="150" y="90" width="50" height="30" rx="5" ry="5" fill-opacity="0" onclick="resetCircuit()" style="cursor: pointer;"/>

                    <path id="active-path-1" class="hidden" d="" stroke="#ef4444" stroke-width="4" fill="none"/>
                    <path id="active-path-2" class="hidden" d="" stroke="#ef4444" stroke-width="4" fill="none"/>
                    <path id="active-path-3" class="hidden" d="" stroke="#ef4444" stroke-width="4" fill="none"/>
                    <path id="trigger-path" class="hidden" d="" stroke="#22c55e" stroke-width="4" fill="none"/>
                    <path id="reset-path" class="hidden" d="" stroke="#3b82f6" stroke-width="4" fill="none"/>
                </svg>
                <div class="flex gap-4 mt-8">
                    <button class="nav-button" onclick="triggerCircuit()">تحفيز الإنذار</button>
                    <button class="nav-button" onclick="resetCircuit()">إعادة تعيين</button>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-7">
            <div class="slide-content">
                <i class="fas fa-cogs icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">مواصفات المكونات الرئيسية: الثايرستور (SCR)</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start text-right">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">المواصفات الهامة:</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">أقصى جهد عكسي (Vdrm, Vrrm):</strong> يجب أن يكون أعلى بكثير من جهد المصدر.</li>
                            <li><strong class="text-green-600">متوسط تيار الحالة الموصلة (It(avg)):</strong> يجب أن يكون أعلى من تيار حمل الإنذار.</li>
                            <li><strong class="text-purple-600">تيار البوابة اللازم للقدح (Igt):</strong> يحدد حساسية البوابة (أقل تيار = أكثر حساسية).</li>
                            <li><strong class="text-indigo-600">جهد البوابة اللازم للقدح (Vgt):</strong> الجهد اللازم على البوابة لقدح الـ SCR.</li>
                            <li><strong class="text-red-600">تيار الإمساك (Holding Current - Ih):</strong> أقل تيار يجب أن يمر عبر المصعد-المهبط ليبقي الـ SCR في حالة توصيل.</li>
                        </ul>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">اعتبارات للأجهزة الطبية:</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-gray-700">الموثوقية:</strong> اختيار SCRs ذات مواصفات تتجاوز ظروف التشغيل المتوقعة كعامل أمان.</li>
                            <li><strong class="text-gray-700">التوفر:</strong> سهولة الحصول على المكونات.</li>
                            <li><strong class="text-gray-700">الاستقرار:</strong> مقاومة للضوضاء والتغيرات البيئية.</li>
                        </ul>
                        <img src="https://placehold.co/250x180/E0F7FA/000000?text=SCR+Characteristics" alt="SCR Characteristics Graph" class="mx-auto my-4 rounded-lg shadow-md">
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-8">
            <div class="slide-content">
                <i class="fas fa-toggle-on icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">مواصفات المكونات الرئيسية: المفاتيح والحمل</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start text-right">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">مفتاح إعادة التعيين (Reset Switch):</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">النوع:</strong> ضاغط لحظي، مغلق عادة (Normally Closed - NC).</li>
                            <li><strong class="text-green-600">التحمل الكهربائي:</strong> يجب أن يتحمل تيار وجهد الدائرة.</li>
                            <li><strong class="text-gray-700">اعتبارات طبية:</strong> المتانة، سهولة الوصول، لكن ليس سهلاً جداً للضغط عليه بالخطأ.</li>
                        </ul>
                        <h3 class="text-2xl font-semibold mb-2 mt-4">مفتاح/إشارة التحفيز (Trigger):</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">إذا كان مفتاحاً:</strong> ضاغط لحظي، مفتوح عادة (Normally Open - NO).</li>
                            <li><strong class="text-green-600">إذا كانت إشارة من حساس:</strong> يجب أن توفر الجهد والتيار اللازمين لقدح الـ SCR.</li>
                        </ul>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">مقاومة البوابة (Gate Resistor):</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">الوظيفة:</strong> تحدد تيار البوابة، تحميها من التيار الزائد، وتمنع القدح الخاطئ.</li>
                            <li><strong class="text-green-600">القيمة:</strong> عادة في نطاق بضع مئات من الأومات إلى بضعة كيلوأومات (مثلاً 1kΩ).</li>
                        </ul>
                        <h3 class="text-2xl font-semibold mb-2 mt-4">حمل الإنذار (Alarm Load - LED, Buzzer):</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">LED ومقاومته:</strong> كما في الحلقات السابقة.</li>
                            <li><strong class="text-green-600">الجرس (Buzzer):</strong> يجب أن يتوافق جهد التشغيل واستهلاك التيار مع الدائرة.</li>
                            <li><strong class="text-gray-700">اعتبارات طبية:</strong> وضوح الإنذار (صوتياً وبصرياً)، عدم إزعاج المرضى الآخرين.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-9">
            <div class="slide-content">
                <i class="fas fa-lightbulb icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">الخلاصة والخبرات العملية</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start text-right">
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">الخلاصة والمهارات المكتسبة:</h3>
                        <p>دائرة الإنذار ذاتي الإغلاق هي أداة قوية لضمان عدم تجاهل الأحداث الهامة في الأجهزة الطبية. باستخدام مكون بسيط مثل الـ SCR، يمكننا إنشاء آلية "إمساك" فعالة تتطلب تدخلاً يدوياً لإعادة التعيين، مما يعزز السلامة.</p>
                        <ul class="list-disc pr-6 mt-4">
                            <li>فهم مبدأ عمل الثايرستور (SCR).</li>
                            <li>تصميم دائرة إنذار بسيطة تستخدم SCR.</li>
                            <li>فهم آلية التحفيز (Triggering) وإعادة التعيين (Resetting) للـ SCR.</li>
                            <li>اختيار SCR مناسب بناءً على مواصفات الدائرة والحمل.</li>
                            <li>القدرة على دمج مدخلات من حساسات.</li>
                        </ul>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-2xl font-semibold mb-2">خبرات عملية سابقة ومشاكل مشابهة:</h3>
                        <ul class="list-disc pr-6">
                            <li><strong class="text-red-600">مشكلة "القدح الخاطئ للـ SCR" (Spurious Triggering):</strong>
                                <p class="text-sm mt-1">كان الـ SCR يقدح عشوائياً بسبب الضوضاء الكهربائية (EMI/RFI) في البيئة الصناعية.</p>
                                <p class="text-sm font-semibold text-green-700 mt-1">الحلول:</p>
                                <ul class="list-circle pr-6 text-sm">
                                    <li>إضافة مكثف صغير (0.01µF - 0.1µF) بين بوابة الـ SCR والمهبط لترشيح الضوضاء.</li>
                                    <li>استخدام مقاومة بوابة (R_gate) أقل قليلاً.</li>
                                    <li>تحسين توجيه الأسلاك وتدريعها.</li>
                                </ul>
                                <p class="text-sm font-bold text-gray-600 mt-2">الدرس المستفاد: بوابة الـ SCR حساسة، ويجب حمايتها من الضوضاء.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="slide" id="slide-10">
            <div class="slide-content">
                <i class="fas fa-tools icon-large mb-4"></i>
                <h2 class="text-3xl font-bold mb-4">الخلاصة والخبرات العملية (تابع)</h2>
                <div class="flex flex-col md:flex-row gap-8 items-start text-right">
                    <div class="flex-1">
                        <ul class="list-disc pr-6">
                            <li><strong class="text-red-600">مشكلة "الـ SCR لا ينطفئ":</strong>
                                <p class="text-sm mt-1">الـ SCR يقدح ولكن لا يمكن إعادة تعيينه لأن تيار الحمل كان أقل من تيار الإمساك (Ih).</p>
                                <p class="text-sm font-semibold text-green-700 mt-1">الحلول:</p>
                                <ul class="list-circle pr-6 text-sm">
                                    <li>استخدام SCR ذي تيار إمساك أقل.</li>
                                    <li>إضافة "مقاومة نزف" (Bleeder resistor) بالتوازي مع الحمل لزيادة التيار الإجمالي.</li>
                                </ul>
                                <p class="text-sm font-bold text-gray-600 mt-2">الدرس المستفاد: تيار الإمساك مواصفة مهمة، خاصة مع الأحمال منخفضة التيار.</p>
                            </li>
                            <li class="mt-4"><strong class="text-red-600">اختيار مفتاح إعادة التعيين الخاطئ:</strong>
                                <p class="text-sm mt-1">استخدام مفتاح (Normally Open - NO) بدلاً من (Normally Closed - NC) بالخطأ.</p>
                                <p class="text-sm font-bold text-gray-600 mt-2">الدرس المستفاد: الانتباه الشديد لنوع المفاتيح (NO/NC) أمر بالغ الأهمية.</p>
                            </li>
                        </ul>
                    </div>
                    <div class="flex-1">
                        <ul class="list-disc pr-6">
                            <li><strong class="text-blue-600">استخدام دائرة ترانزستورين كبديل للـ SCR:</strong>
                                <p class="text-sm mt-1">في تطبيقات تتطلب جهود تشغيل منخفضة جداً وحساسية عالية، تم تصميم دائرة إغلاق ذاتي باستخدام زوج من الترانزستورات BJT.</p>
                                <p class="text-sm font-bold text-gray-600 mt-2">الدرس المستفاد: لكل مكون نقاط قوة وضعف. يمكن تحقيق نفس الوظيفة بطرق مختلفة إذا كانت الطريقة التقليدية غير مناسبة.</p>
                            </li>
                            <li class="mt-4"><strong class="text-green-600">نصيحة من خبير:</strong>
                                <p class="text-sm mt-1">عند استخدام الـ SCRs، تأكد دائماً من وجود آلية موثوقة لإعادة التعيين. في التطبيقات الحرجة، قد ترغب في إضافة مؤشر منفصل يوضح أن "الإنذار قد تم تحفيزه" حتى لو تم إسكاته مؤقتاً.</p>
                                <p class="text-sm font-bold text-gray-600 mt-2">تذكر أن الهدف من الإنذار الممسك هو عدم السماح بتجاهل المشكلة.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation-buttons">
            <button id="prevBtn" class="nav-button" disabled><i class="fas fa-arrow-right mr-2"></i> السابق</button>
            <button id="nextBtn" class="nav-button">التالي <i class="fas fa-arrow-left ml-2"></i></button>
        </div>
    </div>

    <script>
        const slides = document.querySelectorAll('.slide');
        let currentSlideIndex = 0;

        // Function to show a specific slide
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                }
            });
            updateNavButtons();
        }

        // Function to go to the next slide
        function nextSlide() {
            if (currentSlideIndex < slides.length - 1) {
                currentSlideIndex++;
                showSlide(currentSlideIndex);
            }
        }

        // Function to go to the previous slide
        function prevSlide() {
            if (currentSlideIndex > 0) {
                currentSlideIndex--;
                showSlide(currentSlideIndex);
            }
        }

        // Update navigation button states (disabled/enabled)
        function updateNavButtons() {
            document.getElementById('prevBtn').disabled = currentSlideIndex === 0;
            document.getElementById('nextBtn').disabled = currentSlideIndex === slides.length - 1;
        }

        // Event listeners for navigation buttons
        document.getElementById('nextBtn').addEventListener('click', nextSlide);
        document.getElementById('prevBtn').addEventListener('click', prevSlide);

        // Initialize the first slide
        showSlide(currentSlideIndex);

        // --- Block Diagram Animation ---
        let blockDiagramAnimationInterval;
        const blockElements = {
            sensorInput: document.getElementById('sensor-input'),
            latchCircuit: document.getElementById('latch-circuit'),
            alarmOutput: document.getElementById('alarm-output'),
            resetInput: document.getElementById('reset-input'),
            powerSource: document.getElementById('power-source')
        };
        const arrowElements = {
            arrow1: document.getElementById('arrow-1'),
            arrow2: document.getElementById('arrow-2'),
            arrow3: document.getElementById('arrow-3'),
            arrow4: document.getElementById('arrow-4')
        };

        function animateBlockDiagram() {
            // Reset previous animations
            for (const key in blockElements) {
                blockElements[key].classList.remove('highlight');
            }
            for (const key in arrowElements) {
                arrowElements[key].classList.remove('animated');
            }

            let step = 0;
            const steps = [
                () => { // Initial state
                    blockElements.sensorInput.classList.add('highlight');
                    arrowElements.arrow1.classList.add('animated');
                },
                () => { // Sensor to Latch
                    blockElements.sensorInput.classList.remove('highlight');
                    arrowElements.arrow1.classList.remove('animated');
                    blockElements.latchCircuit.classList.add('highlight');
                    arrowElements.arrow3.classList.add('animated'); // Latch to Alarm
                },
                () => { // Latch to Alarm
                    blockElements.latchCircuit.classList.remove('highlight');
                    arrowElements.arrow3.classList.remove('animated');
                    blockElements.alarmOutput.classList.add('highlight');
                    // Simulate continuous alarm
                },
                () => { // Reset
                    blockElements.alarmOutput.classList.remove('highlight');
                    blockElements.resetInput.classList.add('highlight');
                    arrowElements.arrow4.classList.add('animated');
                },
                () => { // Reset to Latch (turn off)
                    blockElements.resetInput.classList.remove('highlight');
                    arrowElements.arrow4.classList.remove('animated');
                    blockElements.latchCircuit.classList.add('highlight'); // Briefly highlight latch as it resets
                },
                () => { // Back to idle
                    blockElements.latchCircuit.classList.remove('highlight');
                    // All off
                }
            ];

            clearInterval(blockDiagramAnimationInterval);
            blockDiagramAnimationInterval = setInterval(() => {
                steps[step]();
                step = (step + 1) % steps.length;
                if (step === 0) { // Stop after one full cycle
                    clearInterval(blockDiagramAnimationInterval);
                    // Optionally, reset all highlights after animation
                    setTimeout(() => {
                        for (const key in blockElements) {
                            blockElements[key].classList.remove('highlight');
                        }
                        for (const key in arrowElements) {
                            arrowElements[key].classList.remove('animated');
                        }
                    }, 1000);
                }
            }, 1500); // Adjust speed
        }

        // --- Circuit Diagram Animation ---
        let isAlarmActive = false;

        function getPathD(id) {
            const element = document.getElementById(id);
            if (element && element.tagName === 'line') {
                return `M${element.getAttribute('x1')} ${element.getAttribute('y1')} L${element.getAttribute('x2')} ${element.getAttribute('y2')}`;
            } else if (element && element.tagName === 'path') {
                return element.getAttribute('d');
            }
            return '';
        }

        function triggerCircuit() {
            if (isAlarmActive) return; // Prevent re-triggering if already active

            // Reset any previous animations
            document.getElementById('active-path-1').classList.add('hidden');
            document.getElementById('active-path-2').classList.add('hidden');
            document.getElementById('active-path-3').classList.add('hidden');
            document.getElementById('trigger-path').classList.add('hidden');
            document.getElementById('reset-path').classList.add('hidden');

            // Animate trigger pulse
            const triggerPath = document.getElementById('trigger-path');
            triggerPath.setAttribute('d', getPathD('path-scr-gate-resistor') + ' ' + getPathD('path-resistor-trigger'));
            triggerPath.classList.remove('hidden');
            triggerPath.classList.add('trigger-pulse');

            setTimeout(() => {
                triggerPath.classList.remove('trigger-pulse');
                triggerPath.classList.add('hidden');

                // Animate current flow through SCR and LED
                const path1 = document.getElementById('active-path-1');
                path1.setAttribute('d', getPathD('path-reset-scr-anode'));
                path1.classList.remove('hidden');
                path1.classList.add('highlight-path');

                const path2 = document.getElementById('active-path-2');
                path2.setAttribute('d', getPathD('path-scr-cathode-led') + ' ' + getPathD('path-led-resistor'));
                path2.classList.remove('hidden');
                path2.classList.add('highlight-path');

                const path3 = document.getElementById('active-path-3');
                path3.setAttribute('d', getPathD('path-resistor-gnd'));
                path3.classList.remove('hidden');
                path3.classList.add('highlight-path');

                isAlarmActive = true;
            }, 600); // After trigger pulse fades
        }

        function resetCircuit() {
            if (!isAlarmActive) return; // Only reset if alarm is active

            // Animate reset effect (e.g., current interruption)
            const resetPath = document.getElementById('reset-path');
            resetPath.setAttribute('d', getPathD('path-vcc-reset'));
            resetPath.classList.remove('hidden');
            resetPath.classList.add('reset-effect');

            // Fade out active paths
            document.getElementById('active-path-1').classList.remove('highlight-path');
            document.getElementById('active-path-2').classList.remove('highlight-path');
            document.getElementById('active-path-3').classList.remove('highlight-path');

            setTimeout(() => {
                document.getElementById('active-path-1').classList.add('hidden');
                document.getElementById('active-path-2').classList.add('hidden');
                document.getElementById('active-path-3').classList.add('hidden');
                resetPath.classList.remove('reset-effect');
                resetPath.classList.add('hidden');
                isAlarmActive = false;
            }, 600); // After reset effect fades
        }

    </script>
</body>
</html>
