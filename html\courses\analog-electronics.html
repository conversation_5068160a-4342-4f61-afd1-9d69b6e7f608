<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analog Electronics - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/course-page.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="course-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-home-btn">
                <i class="fas fa-home"></i>
                <span>Back to Home</span>
            </a>
            <div class="course-title">Analog Electronics</div>
            <div class="nav-actions">
                <button class="search-btn" onclick="toggleSearch()">
                    <i class="fas fa-search"></i>
                </button>
                <button class="theme-toggle" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="progress-indicator">
                    <div class="progress-bar">
                        <div class="progress-fill" id="courseProgress"></div>
                    </div>
                    <span class="progress-text">Progress: 0%</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="course-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Analog Electronics</h1>
                <p>Master analog circuit design, operational amplifiers, and signal processing with advanced simulations and biomedical applications.</p>
                <div class="course-stats">
                    <div class="stat">
                        <i class="fas fa-wave-square"></i>
                        <span>7 Advanced Modules</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-clock"></i>
                        <span>5-6 Hours</span>
                    </div>
                    <div class="stat">
                        <i class="fas fa-certificate"></i>
                        <span>Professional Certificate</span>
                    </div>
                </div>
                <div class="hero-buttons">
                    <button class="start-course-btn" onclick="startCourse()">
                        <i class="fas fa-play"></i>
                        Start Course
                    </button>
                    <button class="preview-btn" onclick="openPreview()">
                        <i class="fas fa-eye"></i>
                        Preview Course
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="course-preview">
                    <div class="preview-screen">
                        <div class="screen-header">
                            <div class="screen-controls">
                                <span class="control red"></span>
                                <span class="control yellow"></span>
                                <span class="control green"></span>
                            </div>
                            <span class="screen-title">Op-Amp Simulator</span>
                        </div>
                        <div class="screen-content">
                            <div class="demo-circuit">
                                <div class="opamp-symbol">
                                    <div class="opamp-inputs">
                                        <div class="input-pin positive">+</div>
                                        <div class="input-pin negative">-</div>
                                    </div>
                                    <div class="opamp-body">741</div>
                                    <div class="opamp-output">Vout</div>
                                </div>
                            </div>
                            <div class="demo-formula">
                                <span class="formula">Vout = A(V+ - V-)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Content -->
    <section class="course-content">
        <div class="container">
            <div class="content-grid">
                <!-- Course Modules -->
                <div class="modules-section">
                    <h2><i class="fas fa-wave-square"></i> Course Modules</h2>
                    <div class="modules-list">
                        <!-- Module 1: Semiconductor Devices -->
                        <div class="module-card" data-module="1">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 1: Semiconductor Devices</h3>
                                    <p>Diodes, BJTs, FETs, and their characteristics</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 50 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator not-started" id="status-1">
                                        <i class="fas fa-play"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('diode-characteristics')">
                                        <i class="fas fa-arrow-right"></i>
                                        <span>Diode Characteristics</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('bjt-analysis')">
                                        <i class="fas fa-sitemap"></i>
                                        <span>BJT Analysis</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('fet-characteristics')">
                                        <i class="fas fa-stream"></i>
                                        <span>FET Characteristics</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('device-modeling')">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Device Modeling</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn" onclick="startModule(1)">
                                        <i class="fas fa-play"></i>
                                        Start Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 2: Operational Amplifiers -->
                        <div class="module-card" data-module="2">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-triangle"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 2: Operational Amplifiers</h3>
                                    <p>Op-amp fundamentals and circuit configurations</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 60 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Intermediate</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-2">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('opamp-basics')">
                                        <i class="fas fa-triangle"></i>
                                        <span>Op-Amp Basics</span>
                                        <div class="topic-badge">Interactive</div>
                                    </div>
                                    <div class="topic" onclick="openTopic('inverting-amplifier')">
                                        <i class="fas fa-minus"></i>
                                        <span>Inverting Amplifier</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('non-inverting-amplifier')">
                                        <i class="fas fa-plus"></i>
                                        <span>Non-Inverting Amplifier</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('opamp-applications')">
                                        <i class="fas fa-cogs"></i>
                                        <span>Op-Amp Applications</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(2)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 3: Amplifier Circuits -->
                        <div class="module-card" data-module="3">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-volume-up"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 3: Amplifier Circuits</h3>
                                    <p>Single and multi-stage amplifier design</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 70 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-3">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('single-stage-amplifiers')">
                                        <i class="fas fa-layer-group"></i>
                                        <span>Single-Stage Amplifiers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('multi-stage-amplifiers')">
                                        <i class="fas fa-layers"></i>
                                        <span>Multi-Stage Amplifiers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('frequency-response')">
                                        <i class="fas fa-chart-area"></i>
                                        <span>Frequency Response</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('amplifier-design')">
                                        <i class="fas fa-drafting-compass"></i>
                                        <span>Amplifier Design</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(3)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 4: Filters and Frequency Response -->
                        <div class="module-card" data-module="4">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-filter"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 4: Filters & Frequency Response</h3>
                                    <p>Active and passive filter design and analysis</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 65 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-4">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('passive-filters')">
                                        <i class="fas fa-wave-square"></i>
                                        <span>Passive Filters</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('active-filters')">
                                        <i class="fas fa-triangle"></i>
                                        <span>Active Filters</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('bode-plots')">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Bode Plots</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('filter-design')">
                                        <i class="fas fa-drafting-compass"></i>
                                        <span>Filter Design Tools</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(4)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 5: Signal Conditioning -->
                        <div class="module-card" data-module="5">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-signal"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 5: Signal Conditioning</h3>
                                    <p>Signal processing for sensor interfaces</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 55 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-5">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('instrumentation-amplifiers')">
                                        <i class="fas fa-microscope"></i>
                                        <span>Instrumentation Amplifiers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('isolation-amplifiers')">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Isolation Amplifiers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('adc-interfaces')">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>ADC Interfaces</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('noise-reduction')">
                                        <i class="fas fa-volume-mute"></i>
                                        <span>Noise Reduction</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(5)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 6: Power Electronics -->
                        <div class="module-card" data-module="6">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-battery-full"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 6: Power Electronics</h3>
                                    <p>Power supplies and voltage regulation</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 60 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Advanced</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-6">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('linear-regulators')">
                                        <i class="fas fa-ruler-horizontal"></i>
                                        <span>Linear Regulators</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('switching-regulators')">
                                        <i class="fas fa-toggle-on"></i>
                                        <span>Switching Regulators</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('dc-dc-converters')">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>DC-DC Converters</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('power-management')">
                                        <i class="fas fa-cogs"></i>
                                        <span>Power Management</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(6)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Module 7: Biomedical Applications -->
                        <div class="module-card" data-module="7">
                            <div class="module-header">
                                <div class="module-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="module-info">
                                    <h3>Module 7: Biomedical Applications</h3>
                                    <p>Analog circuits in medical devices and systems</p>
                                    <div class="module-meta">
                                        <span class="duration"><i class="fas fa-clock"></i> 75 min</span>
                                        <span class="difficulty"><i class="fas fa-signal"></i> Expert</span>
                                    </div>
                                </div>
                                <div class="module-status">
                                    <div class="status-indicator locked" id="status-7">
                                        <i class="fas fa-lock"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="module-content">
                                <div class="topics-list">
                                    <div class="topic" onclick="openTopic('ecg-amplifiers')">
                                        <i class="fas fa-heartbeat"></i>
                                        <span>ECG Amplifiers</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('eeg-systems')">
                                        <i class="fas fa-brain"></i>
                                        <span>EEG Systems</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('medical-imaging')">
                                        <i class="fas fa-x-ray"></i>
                                        <span>Medical Imaging Circuits</span>
                                    </div>
                                    <div class="topic" onclick="openTopic('therapeutic-devices')">
                                        <i class="fas fa-medical"></i>
                                        <span>Therapeutic Devices</span>
                                    </div>
                                </div>
                                <div class="module-actions">
                                    <button class="start-module-btn locked" onclick="startModule(7)" disabled>
                                        <i class="fas fa-lock"></i>
                                        Complete Previous Module
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Sidebar -->
                <div class="course-sidebar">
                    <!-- Course Progress -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-chart-pie"></i> Your Progress</h3>
                        <div class="progress-circle">
                            <div class="circle">
                                <div class="progress-value">0%</div>
                            </div>
                        </div>
                        <div class="progress-stats">
                            <div class="stat">
                                <span class="label">Completed:</span>
                                <span class="value">0/7 modules</span>
                            </div>
                            <div class="stat">
                                <span class="label">Time Spent:</span>
                                <span class="value">0 hours</span>
                            </div>
                            <div class="stat">
                                <span class="label">Next Module:</span>
                                <span class="value">Semiconductor Devices</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Tools -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-tools"></i> Analog Tools</h3>
                        <div class="tools-list">
                            <button class="tool-btn" onclick="openTool('opamp-calculator')">
                                <i class="fas fa-triangle"></i>
                                <span>Op-Amp Calculator</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('filter-designer')">
                                <i class="fas fa-filter"></i>
                                <span>Filter Designer</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('bode-plotter')">
                                <i class="fas fa-chart-line"></i>
                                <span>Bode Plot Analyzer</span>
                            </button>
                            <button class="tool-btn" onclick="openTool('spice-simulator')">
                                <i class="fas fa-microchip"></i>
                                <span>SPICE Simulator</span>
                            </button>
                        </div>
                    </div>

                    <!-- Prerequisites -->
                    <div class="sidebar-card">
                        <h3><i class="fas fa-graduation-cap"></i> Prerequisites</h3>
                        <div class="related-courses">
                            <div class="related-course" onclick="openCourse('fundamentals')">
                                <div class="course-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Electronics Fundamentals</h4>
                                    <p>Required foundation</p>
                                </div>
                            </div>
                            <div class="related-course" onclick="openCourse('digital-electronics')">
                                <div class="course-icon">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="course-info">
                                    <h4>Digital Electronics</h4>
                                    <p>Recommended background</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search Overlay -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-container">
            <input type="text" class="search-input" placeholder="Search analog electronics content..." id="searchInput">
            <button class="search-close" onclick="toggleSearch()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="search-results" id="searchResults">
            <!-- Search results will be populated here -->
        </div>
    </div>

    <script src="../../js/course-page.js"></script>
    <script>
        // Override course-specific settings
        document.addEventListener('DOMContentLoaded', function() {
            // Update course-specific variables
            window.courseModuleCount = 7;
            window.courseName = 'analog-electronics';
            
            // Update progress stats
            const nextModuleStat = document.querySelector('.progress-stats .stat:nth-child(3) .value');
            if (nextModuleStat) {
                const moduleNames = ['Semiconductor Devices', 'Operational Amplifiers', 'Amplifier Circuits', 'Filters & Frequency', 'Signal Conditioning', 'Power Electronics', 'Biomedical Applications'];
                nextModuleStat.textContent = moduleNames[0];
            }
        });
    </script>
</body>
</html>
