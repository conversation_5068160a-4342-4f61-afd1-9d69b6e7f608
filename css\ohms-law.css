/* Ohm's Law Module Styles */

/* Hero Section */
.ohms-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.ohms-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-text p {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
}

.hero-objectives {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-objectives h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: #f0f8ff;
}

.hero-objectives ul {
    list-style: none;
    padding: 0;
}

.hero-objectives li {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 1rem;
}

.hero-objectives li i {
    color: #4ade80;
    margin-right: 10px;
    font-size: 1.1rem;
}

/* Ohm's Triangle Animation */
.ohms-triangle-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.triangle-container {
    text-align: center;
}

.ohms-triangle {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255,255,255,0.3);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.triangle-section {
    background: rgba(255,255,255,0.15);
    border-radius: 10px;
    padding: 15px;
    margin: 5px;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.triangle-section:hover {
    background: rgba(255,255,255,0.25);
    transform: scale(1.05);
}

.triangle-section span {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #fff;
}

.triangle-section small {
    display: block;
    font-size: 0.9rem;
    color: #f0f8ff;
    margin-top: 5px;
}

.voltage-section {
    background: linear-gradient(45deg, #ef4444, #f87171);
}

.current-section {
    background: linear-gradient(45deg, #3b82f6, #60a5fa);
}

.resistance-section {
    background: linear-gradient(45deg, #10b981, #34d399);
}

.formula-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #fff;
    background: rgba(0,0,0,0.2);
    padding: 15px 25px;
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.3);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Slide Presentation */
.slide-presentation {
    padding: 60px 0;
    background: #f8fafc;
}

.presentation-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 40px;
    padding: 0 20px;
}

.presentation-header h2 {
    font-size: 2.5rem;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 15px;
}

.presentation-header i {
    color: #667eea;
}

.slide-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.slide-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.slide-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.slide-btn:disabled {
    background: #94a3b8;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.slide-counter {
    font-size: 1.1rem;
    font-weight: 600;
    color: #475569;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
}

/* Slides Container */
.slides-container {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.slide {
    display: none;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
}

.slide.active {
    display: block;
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-content h3 {
    font-size: 2rem;
    color: #1e293b;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.slide-content h3 i {
    color: #667eea;
}

.slide-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

.slide-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #475569;
}

.slide-text h4 {
    color: #1e293b;
    font-size: 1.3rem;
    margin: 25px 0 15px 0;
}

.slide-text ul {
    margin: 15px 0;
    padding-left: 20px;
}

.slide-text li {
    margin-bottom: 8px;
}

.key-points, .historical-note, .voltage-relationships, .medical-example {
    background: #f1f5f9;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    border-left: 4px solid #667eea;
}

/* Formula Grid */
.formula-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
}

.formula-card {
    background: linear-gradient(45deg, #f8fafc, #f1f5f9);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.formula-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.formula-card h5 {
    color: #1e293b;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.formula {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    margin: 15px 0;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.formula-card p {
    color: #64748b;
    font-size: 0.95rem;
    margin: 0;
}

/* Interactive Triangle */
.interactive-triangle {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.triangle-calculator {
    text-align: center;
}

.triangle-shape {
    position: relative;
    margin-bottom: 30px;
}

.triangle-top, .triangle-left, .triangle-right {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 5px;
    border: 2px solid rgba(255,255,255,0.3);
}

.triangle-top:hover, .triangle-left:hover, .triangle-right:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.triangle-bottom {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.variable {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
}

.unit {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}

.triangle-instruction {
    background: #f8fafc;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.selected-formula {
    font-size: 1.3rem;
    font-weight: bold;
    color: #667eea;
    margin-top: 10px;
    font-family: 'Courier New', monospace;
}

/* Calculator Section */
.ohms-calculator {
    padding: 60px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.ohms-calculator h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.calculator-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.calculator-inputs, .calculator-results {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.input-group {
    margin-bottom: 25px;
}

.input-group label {
    display: block;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 12px;
    font-size: 1.2rem;
}

.input-group input {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid var(--border-light);
    border-radius: var(--radius-xl);
    font-size: 1.1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--bg-secondary);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    background: var(--bg-primary);
    transform: translateY(-1px);
}

.unit {
    display: inline-block;
    margin-left: 10px;
    color: #6b7280;
    font-weight: 500;
}

.calculator-results h3 {
    color: #1e293b;
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.result-item.power {
    border-left-color: #ef4444;
    background: linear-gradient(45deg, #fef2f2, #f9fafb);
}

.result-item .formula {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #374151;
}

.result-item .result {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1rem;
}

.result-item.power .result {
    color: #ef4444;
}

/* Quiz Section */
.quiz-section {
    padding: 100px 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.quiz-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.quiz-section h2 {
    text-align: center;
    font-size: 3rem;
    color: var(--text-primary);
    margin-bottom: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    font-weight: 800;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 1;
}

.quiz-section h2 i {
    color: var(--secondary-color);
    font-size: 2.5rem;
}

.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .slide-layout {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .calculator-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .presentation-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .slide-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}
