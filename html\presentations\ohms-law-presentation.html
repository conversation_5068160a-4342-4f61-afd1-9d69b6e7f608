<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ohm's Law - Interactive Presentation</title>
    <link rel="stylesheet" href="../../css/presentation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="presentation-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="presentation-title">Ohm's Law</div>
            <div class="nav-controls">
                <button class="nav-btn" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" onclick="toggleAutoPlay()">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Presentation Container -->
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide active" data-slide="0">
            <div class="slide-content">
                <div class="title-slide">
                    <div class="slide-header">
                        <h1>Ohm's Law</h1>
                        <h2>The Foundation of Circuit Analysis</h2>
                        <p>Understanding the relationship between Voltage, Current, and Resistance</p>
                    </div>
                    <div class="slide-visual">
                        <div class="ohms-law-triangle">
                            <div class="triangle-container">
                                <div class="triangle">
                                    <div class="triangle-section top">
                                        <span class="variable">V</span>
                                        <span class="label">Voltage</span>
                                    </div>
                                    <div class="triangle-section bottom-left">
                                        <span class="variable">I</span>
                                        <span class="label">Current</span>
                                    </div>
                                    <div class="triangle-section bottom-right">
                                        <span class="variable">R</span>
                                        <span class="label">Resistance</span>
                                    </div>
                                </div>
                                <div class="formula-display">
                                    <div class="formula">V = I × R</div>
                                    <div class="formula">I = V / R</div>
                                    <div class="formula">R = V / I</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: What is Ohm's Law? -->
        <div class="slide" data-slide="1">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-balance-scale"></i> What is Ohm's Law?</h1>
                        <p>The fundamental relationship in electrical circuits</p>
                    </div>
                    <div class="slide-body">
                        <div class="concept-explanation">
                            <div class="explanation-text">
                                <h3>Definition</h3>
                                <p>Ohm's Law states that the current through a conductor between two points is directly proportional to the voltage across the two points, and inversely proportional to the resistance.</p>
                                
                                <h3>Mathematical Expression</h3>
                                <div class="law-formula">
                                    <div class="main-formula">
                                        <h2>V = I × R</h2>
                                        <p>Voltage = Current × Resistance</p>
                                    </div>
                                </div>

                                <h3>Key Insights</h3>
                                <ul>
                                    <li><strong>Linear Relationship:</strong> Voltage and current are proportional</li>
                                    <li><strong>Resistance Effect:</strong> Higher resistance reduces current</li>
                                    <li><strong>Universal Application:</strong> Works for most materials</li>
                                    <li><strong>Circuit Analysis:</strong> Foundation for complex circuits</li>
                                </ul>
                            </div>
                            <div class="explanation-visual">
                                <div class="ohms-law-demo">
                                    <div class="interactive-circuit">
                                        <div class="circuit-elements">
                                            <div class="voltage-source">
                                                <div class="battery">
                                                    <span class="voltage-value" id="demoVoltage">12V</span>
                                                </div>
                                            </div>
                                            <div class="circuit-resistor">
                                                <div class="resistor">
                                                    <span class="resistance-value" id="demoResistance">6Ω</span>
                                                </div>
                                            </div>
                                            <div class="current-display">
                                                <div class="ammeter">
                                                    <span class="current-value" id="demoCurrent">2A</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="circuit-wires">
                                            <div class="wire wire-top"></div>
                                            <div class="wire wire-bottom"></div>
                                            <div class="current-flow">
                                                <div class="electron"></div>
                                                <div class="electron"></div>
                                                <div class="electron"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="demo-controls">
                                        <div class="control-group">
                                            <label>Voltage (V):</label>
                                            <input type="range" id="voltageSlider" min="1" max="24" value="12" step="1">
                                            <span id="voltageDisplay">12V</span>
                                        </div>
                                        <div class="control-group">
                                            <label>Resistance (Ω):</label>
                                            <input type="range" id="resistanceSlider" min="1" max="20" value="6" step="1">
                                            <span id="resistanceDisplay">6Ω</span>
                                        </div>
                                        <div class="result-display">
                                            <span>Current: </span>
                                            <span id="currentDisplay">2.00A</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 3: The Magic Triangle -->
        <div class="slide" data-slide="2">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-play"></i> The Magic Triangle</h1>
                        <p>A visual tool for remembering Ohm's Law formulas</p>
                    </div>
                    <div class="slide-body">
                        <div class="triangle-explanation">
                            <div class="triangle-visual">
                                <div class="interactive-triangle">
                                    <div class="triangle-large">
                                        <div class="triangle-section-large top" data-variable="V">
                                            <span class="variable-large">V</span>
                                            <span class="unit">(Volts)</span>
                                        </div>
                                        <div class="triangle-section-large bottom-left" data-variable="I">
                                            <span class="variable-large">I</span>
                                            <span class="unit">(Amps)</span>
                                        </div>
                                        <div class="triangle-section-large bottom-right" data-variable="R">
                                            <span class="variable-large">R</span>
                                            <span class="unit">(Ohms)</span>
                                        </div>
                                        <div class="division-line horizontal"></div>
                                        <div class="division-line vertical"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="triangle-instructions">
                                <h3>How to Use the Triangle</h3>
                                <div class="instruction-steps">
                                    <div class="step">
                                        <div class="step-number">1</div>
                                        <div class="step-content">
                                            <h4>Cover the Unknown</h4>
                                            <p>Cover the variable you want to find</p>
                                        </div>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">2</div>
                                        <div class="step-content">
                                            <h4>Read the Formula</h4>
                                            <p>The remaining variables show the formula</p>
                                        </div>
                                    </div>
                                    <div class="step">
                                        <div class="step-number">3</div>
                                        <div class="step-content">
                                            <h4>Calculate</h4>
                                            <p>Use the formula to solve for the unknown</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="formula-examples">
                                    <div class="formula-example" data-formula="V">
                                        <h4>Finding Voltage</h4>
                                        <div class="formula-display">V = I × R</div>
                                        <p>Multiply current by resistance</p>
                                    </div>
                                    <div class="formula-example" data-formula="I">
                                        <h4>Finding Current</h4>
                                        <div class="formula-display">I = V ÷ R</div>
                                        <p>Divide voltage by resistance</p>
                                    </div>
                                    <div class="formula-example" data-formula="R">
                                        <h4>Finding Resistance</h4>
                                        <div class="formula-display">R = V ÷ I</div>
                                        <p>Divide voltage by current</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Practical Examples -->
        <div class="slide" data-slide="3">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-lightbulb"></i> Practical Examples</h1>
                        <p>Real-world applications of Ohm's Law</p>
                    </div>
                    <div class="slide-body">
                        <div class="examples-grid">
                            <div class="example-card">
                                <div class="example-header">
                                    <h3><i class="fas fa-lightbulb"></i> LED Circuit</h3>
                                </div>
                                <div class="example-content">
                                    <div class="example-circuit">
                                        <div class="circuit-diagram">
                                            <div class="battery-symbol">9V</div>
                                            <div class="resistor-symbol">R = ?</div>
                                            <div class="led-symbol">LED</div>
                                        </div>
                                    </div>
                                    <div class="example-problem">
                                        <h4>Problem:</h4>
                                        <p>An LED needs 20mA current and has a 2V forward voltage drop. What resistor is needed with a 9V battery?</p>
                                        
                                        <h4>Solution:</h4>
                                        <div class="solution-steps">
                                            <div class="step">Voltage across resistor: 9V - 2V = 7V</div>
                                            <div class="step">Current needed: 20mA = 0.02A</div>
                                            <div class="step">R = V/I = 7V / 0.02A = 350Ω</div>
                                        </div>
                                        
                                        <div class="answer">
                                            <strong>Answer: 350Ω resistor</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="example-card">
                                <div class="example-header">
                                    <h3><i class="fas fa-heartbeat"></i> Medical Device</h3>
                                </div>
                                <div class="example-content">
                                    <div class="example-circuit">
                                        <div class="circuit-diagram">
                                            <div class="battery-symbol">3V</div>
                                            <div class="resistor-symbol">1kΩ</div>
                                            <div class="sensor-symbol">Sensor</div>
                                        </div>
                                    </div>
                                    <div class="example-problem">
                                        <h4>Problem:</h4>
                                        <p>A pulse oximeter sensor circuit has a 1kΩ resistor and 3V supply. What current flows through the circuit?</p>
                                        
                                        <h4>Solution:</h4>
                                        <div class="solution-steps">
                                            <div class="step">Given: V = 3V, R = 1kΩ = 1000Ω</div>
                                            <div class="step">Using I = V/R</div>
                                            <div class="step">I = 3V / 1000Ω = 0.003A = 3mA</div>
                                        </div>
                                        
                                        <div class="answer">
                                            <strong>Answer: 3mA current</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="example-card">
                                <div class="example-header">
                                    <h3><i class="fas fa-mobile-alt"></i> Phone Charger</h3>
                                </div>
                                <div class="example-content">
                                    <div class="example-circuit">
                                        <div class="circuit-diagram">
                                            <div class="battery-symbol">5V</div>
                                            <div class="resistor-symbol">R = ?</div>
                                            <div class="load-symbol">Phone</div>
                                        </div>
                                    </div>
                                    <div class="example-problem">
                                        <h4>Problem:</h4>
                                        <p>A phone charger outputs 5V and 2A. What is the equivalent resistance of the phone?</p>
                                        
                                        <h4>Solution:</h4>
                                        <div class="solution-steps">
                                            <div class="step">Given: V = 5V, I = 2A</div>
                                            <div class="step">Using R = V/I</div>
                                            <div class="step">R = 5V / 2A = 2.5Ω</div>
                                        </div>
                                        
                                        <div class="answer">
                                            <strong>Answer: 2.5Ω equivalent resistance</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: Interactive Calculator -->
        <div class="slide" data-slide="4">
            <div class="slide-content">
                <div class="content-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-calculator"></i> Interactive Calculator</h1>
                        <p>Practice Ohm's Law calculations</p>
                    </div>
                    <div class="slide-body">
                        <div class="calculator-container">
                            <div class="calculator-visual">
                                <div class="calculator-triangle">
                                    <div class="calc-triangle">
                                        <div class="calc-section top" onclick="selectVariable('V')">
                                            <span class="calc-variable">V</span>
                                            <span class="calc-value" id="voltageResult">?</span>
                                        </div>
                                        <div class="calc-section bottom-left" onclick="selectVariable('I')">
                                            <span class="calc-variable">I</span>
                                            <span class="calc-value" id="currentResult">?</span>
                                        </div>
                                        <div class="calc-section bottom-right" onclick="selectVariable('R')">
                                            <span class="calc-variable">R</span>
                                            <span class="calc-value" id="resistanceResult">?</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="calculator-inputs">
                                <h3>Enter Known Values</h3>
                                <div class="input-grid">
                                    <div class="input-group">
                                        <label for="calcVoltage">Voltage (V):</label>
                                        <input type="number" id="calcVoltage" placeholder="Enter voltage" step="0.01">
                                        <span class="unit">V</span>
                                    </div>
                                    <div class="input-group">
                                        <label for="calcCurrent">Current (A):</label>
                                        <input type="number" id="calcCurrent" placeholder="Enter current" step="0.001">
                                        <span class="unit">A</span>
                                    </div>
                                    <div class="input-group">
                                        <label for="calcResistance">Resistance (Ω):</label>
                                        <input type="number" id="calcResistance" placeholder="Enter resistance" step="0.01">
                                        <span class="unit">Ω</span>
                                    </div>
                                </div>
                                <div class="calculator-buttons">
                                    <button class="calc-btn" onclick="calculateOhmsLaw()">
                                        <i class="fas fa-calculator"></i>
                                        Calculate
                                    </button>
                                    <button class="calc-btn secondary" onclick="clearCalculator()">
                                        <i class="fas fa-eraser"></i>
                                        Clear
                                    </button>
                                </div>
                                <div class="calculation-result" id="calculationResult">
                                    <div class="result-formula" id="resultFormula"></div>
                                    <div class="result-value" id="resultValue"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 6: Summary -->
        <div class="slide" data-slide="5">
            <div class="slide-content">
                <div class="summary-slide">
                    <div class="slide-header">
                        <h1><i class="fas fa-check-circle"></i> Summary</h1>
                        <p>Key takeaways from Ohm's Law</p>
                    </div>
                    <div class="summary-content">
                        <div class="key-points">
                            <div class="point">
                                <div class="point-icon">
                                    <i class="fas fa-equals"></i>
                                </div>
                                <div class="point-content">
                                    <h3>Fundamental Relationship</h3>
                                    <p>V = I × R is the foundation of circuit analysis</p>
                                </div>
                            </div>
                            <div class="point">
                                <div class="point-icon">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="point-content">
                                    <h3>Magic Triangle</h3>
                                    <p>Visual tool for remembering all three formulas</p>
                                </div>
                            </div>
                            <div class="point">
                                <div class="point-icon">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="point-content">
                                    <h3>Practical Applications</h3>
                                    <p>Used in LED circuits, medical devices, and electronics</p>
                                </div>
                            </div>
                        </div>
                        <div class="formula-summary">
                            <h3>The Three Formulas</h3>
                            <div class="formulas-grid">
                                <div class="formula-card">
                                    <h4>Find Voltage</h4>
                                    <div class="formula">V = I × R</div>
                                </div>
                                <div class="formula-card">
                                    <h4>Find Current</h4>
                                    <div class="formula">I = V ÷ R</div>
                                </div>
                                <div class="formula-card">
                                    <h4>Find Resistance</h4>
                                    <div class="formula">R = V ÷ I</div>
                                </div>
                            </div>
                        </div>
                        <div class="next-steps">
                            <h3>Continue Learning</h3>
                            <div class="next-buttons">
                                <button class="next-btn" onclick="openPassiveComponentsPresentation()">
                                    <i class="fas fa-cog"></i>
                                    Passive Components
                                </button>
                                <button class="next-btn" onclick="openPowerPresentation()">
                                    <i class="fas fa-fire"></i>
                                    Power Calculations
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide Navigation -->
    <div class="slide-navigation">
        <button class="nav-btn prev" onclick="previousSlide()">
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="slide-indicators">
            <span class="indicator active" data-slide="0"></span>
            <span class="indicator" data-slide="1"></span>
            <span class="indicator" data-slide="2"></span>
            <span class="indicator" data-slide="3"></span>
            <span class="indicator" data-slide="4"></span>
            <span class="indicator" data-slide="5"></span>
        </div>
        <button class="nav-btn next" onclick="nextSlide()">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <script src="../../js/presentation.js"></script>
    <script>
        // Initialize interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            initializeOhmsLawDemo();
            initializeCalculator();
        });

        function initializeOhmsLawDemo() {
            const voltageSlider = document.getElementById('voltageSlider');
            const resistanceSlider = document.getElementById('resistanceSlider');
            
            function updateDemo() {
                const voltage = parseFloat(voltageSlider.value);
                const resistance = parseFloat(resistanceSlider.value);
                const current = voltage / resistance;
                
                document.getElementById('voltageDisplay').textContent = voltage + 'V';
                document.getElementById('resistanceDisplay').textContent = resistance + 'Ω';
                document.getElementById('currentDisplay').textContent = current.toFixed(2) + 'A';
                
                document.getElementById('demoVoltage').textContent = voltage + 'V';
                document.getElementById('demoResistance').textContent = resistance + 'Ω';
                document.getElementById('demoCurrent').textContent = current.toFixed(2) + 'A';
            }
            
            voltageSlider.addEventListener('input', updateDemo);
            resistanceSlider.addEventListener('input', updateDemo);
            updateDemo();
        }

        function initializeCalculator() {
            const inputs = ['calcVoltage', 'calcCurrent', 'calcResistance'];
            inputs.forEach(id => {
                const input = document.getElementById(id);
                if (input) {
                    input.addEventListener('input', calculateOhmsLaw);
                }
            });
        }

        function calculateOhmsLaw() {
            const voltage = parseFloat(document.getElementById('calcVoltage').value) || null;
            const current = parseFloat(document.getElementById('calcCurrent').value) || null;
            const resistance = parseFloat(document.getElementById('calcResistance').value) || null;
            
            const resultFormula = document.getElementById('resultFormula');
            const resultValue = document.getElementById('resultValue');
            const calculationResult = document.getElementById('calculationResult');
            
            let result = null;
            let formula = '';
            let value = '';
            
            if (voltage !== null && current !== null && resistance === null) {
                result = voltage / current;
                formula = 'R = V ÷ I';
                value = `R = ${voltage}V ÷ ${current}A = ${result.toFixed(3)}Ω`;
                document.getElementById('resistanceResult').textContent = result.toFixed(3) + 'Ω';
            } else if (voltage !== null && resistance !== null && current === null) {
                result = voltage / resistance;
                formula = 'I = V ÷ R';
                value = `I = ${voltage}V ÷ ${resistance}Ω = ${result.toFixed(3)}A`;
                document.getElementById('currentResult').textContent = result.toFixed(3) + 'A';
            } else if (current !== null && resistance !== null && voltage === null) {
                result = current * resistance;
                formula = 'V = I × R';
                value = `V = ${current}A × ${resistance}Ω = ${result.toFixed(3)}V`;
                document.getElementById('voltageResult').textContent = result.toFixed(3) + 'V';
            }
            
            if (result !== null) {
                resultFormula.textContent = formula;
                resultValue.textContent = value;
                calculationResult.style.display = 'block';
            } else {
                calculationResult.style.display = 'none';
            }
        }

        function clearCalculator() {
            document.getElementById('calcVoltage').value = '';
            document.getElementById('calcCurrent').value = '';
            document.getElementById('calcResistance').value = '';
            document.getElementById('voltageResult').textContent = '?';
            document.getElementById('currentResult').textContent = '?';
            document.getElementById('resistanceResult').textContent = '?';
            document.getElementById('calculationResult').style.display = 'none';
        }

        function selectVariable(variable) {
            // Visual feedback for triangle interaction
            const sections = document.querySelectorAll('.calc-section');
            sections.forEach(section => section.classList.remove('selected'));
            event.currentTarget.classList.add('selected');
        }

        function openPassiveComponentsPresentation() {
            window.open('passive-components-presentation.html', '_blank');
        }

        function openPowerPresentation() {
            window.open('power-presentation.html', '_blank');
        }
    </script>
</body>
</html>
