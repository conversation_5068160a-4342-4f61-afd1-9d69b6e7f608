
import React, { useState, useRef, useEffect } from 'react';
import { Icons } from './icons/IconComponents';

interface NavSubItem {
  label: string;
  href: string;
}

interface NavDropdownSection {
  title: string;
  icon: React.FC<React.SVGProps<SVGSVGElement> & { strokeWidth?: number }>;
  items: NavSubItem[];
}

interface NavItemBase {
  label: string;
  icon?: React.FC<React.SVGProps<SVGSVGElement> & { strokeWidth?: number }>;
}
interface NavItemWithDropdown extends NavItemBase {
  dropdownSections: NavDropdownSection[];
  href?: undefined;
}

interface NavItemLink extends NavItemBase {
  href: string;
  dropdownSections?: undefined;
}

type NavItem = NavItemWithDropdown | NavItemLink;

const navItems: NavItem[] = [
  { label: "Home", href: "#"},
  {
    label: "Courses",
    icon: Icons.AcademicCapIcon,
    dropdownSections: [
      { 
        title: "Fundamentals", 
        icon: Icons.PlayIcon, 
        items: [
          { label: "Complete Course", href: "#" },
          { label: "Interactive Presentation", href: "#" },
          { label: "Basic Concepts", href: "#basic-concepts" },
          { label: "Ohm's Law", href: "#ohms-law" },
          { label: "Passive Components", href: "#passive-components" },
        ] 
      },
      { 
        title: "Digital Electronics", 
        icon: Icons.ChipIcon, 
        items: [
          { label: "Digital Fundamentals", href: "#" },
          { label: "Logic Gates", href: "#" },
          { label: "Boolean Algebra", href: "#" },
          { label: "Combinational Circuits", href: "#" },
          { label: "Sequential Circuits", href: "#" },
        ] 
      },
      { 
        title: "Analog Electronics", 
        icon: Icons.SignalIcon, 
        items: [
          { label: "Analog Fundamentals", href: "#" },
          { label: "Semiconductor Devices", href: "#" },
          { label: "Op-Amps", href: "#op-amps" },
          { label: "Filters & Frequency", href: "#" },
          { label: "Signal Conditioning", href: "#" },
        ] 
      },
      { 
        title: "Biomedical Applications", 
        icon: Icons.HeartIcon, 
        items: [
          { label: "Bio-signal Processing", href: "#" },
          { label: "Medical Devices", href: "#" },
          { label: "Patient Monitoring", href: "#" },
        ] 
      },
    ]
  },
  {
    label: "Tools",
    icon: Icons.WrenchScrewdriverIcon,
    dropdownSections: [
      { 
        title: "Calculators", 
        icon: Icons.CalculatorIcon, 
        items: [
          { label: "Ohm's Law Calculator", href: "#ohms-law" },
          { label: "Power Calculator", href: "#" },
          { label: "Resistor Color Code", href: "#" },
          { label: "Capacitor Calculator", href: "#" },
          { label: "Frequency Calculator", href: "#" },
        ] 
      },
      { 
        title: "Simulators", 
        icon: Icons.CircuitBoardIcon, // Using CircuitBoard as a generic sim icon
        items: [
          { label: "Circuit Simulator", href: "#" },
          { label: "Logic Gate Simulator", href: "#" },
          { label: "Filter Designer", href: "#" },
          { label: "Waveform Generator", href: "#" },
          { label: "PCB Designer", href: "#" },
        ] 
      },
      { 
        title: "Analysis Tools", 
        icon: Icons.ChartBarIcon, 
        items: [
          { label: "Bode Plot Analyzer", href: "#" },
          { label: "FFT Analyzer", href: "#" },
          { label: "Noise Analyzer", href: "#" },
          { label: "Stability Analyzer", href: "#" },
          { label: "Distortion Analyzer", href: "#" },
        ] 
      },
    ]
  }
];

const Header: React.FC = () => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const timeoutRef = useRef<number | null>(null);

  const handleMouseEnter = (label: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setOpenDropdown(label);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = window.setTimeout(() => {
      setOpenDropdown(null);
    }, 150); // Small delay to allow moving cursor into dropdown
  };
  
  const handleDropdownMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  };


  return (
    <header className="bg-sky-700 text-white shadow-md sticky top-0 z-50 print:hidden">
      <div className="container mx-auto flex items-center justify-between p-4">
        <a href="#" className="flex items-center">
          <Icons.BookOpenIcon className="w-8 h-8 mr-3 text-sky-300" />
          <h1 className="text-xl sm:text-2xl font-bold">Biomedical Electronics LMS</h1>
        </a>

        <nav>
          <ul className="flex space-x-2 sm:space-x-4 items-center">
            {navItems.map(item => (
              <li 
                key={item.label} 
                className="relative group"
                onMouseEnter={() => item.dropdownSections && handleMouseEnter(item.label)}
                onMouseLeave={() => item.dropdownSections && handleMouseLeave()}
              >
                <a 
                  href={item.href || "#"} 
                  className="px-2 py-2 sm:px-3 sm:py-2 rounded-md text-sm font-medium hover:bg-sky-600 transition-colors flex items-center"
                  onClick={(e) => { if (!item.href) e.preventDefault();}} // Prevent click if it's a dropdown trigger only
                  aria-haspopup={!!item.dropdownSections}
                  aria-expanded={openDropdown === item.label}
                >
                  {item.icon && <item.icon className="w-5 h-5 mr-1.5 hidden sm:inline-block" strokeWidth={2} />}
                  {item.label}
                  {item.dropdownSections && <Icons.ChevronDownIcon className="w-4 h-4 ml-1.5" strokeWidth={2.5} />}
                </a>
                
                {item.dropdownSections && openDropdown === item.label && (
                  <div 
                    className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 p-6 bg-white text-slate-700 shadow-2xl rounded-lg min-w-[280px] sm:min-w-[480px] md:min-w-[560px] max-w-2xl z-20"
                    onMouseEnter={handleDropdownMouseEnter}
                    onMouseLeave={handleMouseLeave} // Close if mouse leaves dropdown too
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-5">
                      {item.dropdownSections.map(section => (
                        <div key={section.title}>
                          <h4 className="text-base font-semibold text-sky-700 mb-2.5 flex items-center border-b border-slate-200 pb-1.5">
                            <section.icon className="w-5 h-5 mr-2 text-sky-600" strokeWidth={2} />
                            {section.title}
                          </h4>
                          <ul className="space-y-1.5">
                            {section.items.map(subItem => (
                              <li key={subItem.label}>
                                <a 
                                  href={subItem.href} 
                                  className="text-sm text-slate-600 hover:text-emerald-600 hover:font-medium block py-1 transition-all"
                                  onClick={() => setOpenDropdown(null)} // Close dropdown on item click
                                >
                                  {subItem.label}
                                </a>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </header>
  );
};

export default Header;
