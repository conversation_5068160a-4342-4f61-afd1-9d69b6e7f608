
import { Module } from './types';

export const MODULES_DATA: Module[] = [
  {
    id: 'basic-concepts',
    title: 'Basic Electrical Concepts',
    description: 'Understand fundamental electrical quantities like voltage, current, resistance, and power.',
    icon: 'LightbulbIcon',
    lessons: [
      {
        id: 'l1-intro',
        title: 'Introduction to Biomedical Electronics',
        content: [
          { type: 'text', value: 'Welcome to the world of biomedical electronics! This field combines principles of electrical engineering with biology and medicine to design and build devices that improve human health. Understanding basic electronics is crucial for anyone working with medical devices, from simple sensors to complex imaging systems.' },
          { type: 'image', src: 'https://picsum.photos/seed/biomedical_intro/600/300', alt: 'Biomedical Engineering Concept' },
        ],
      },
      {
        id: 'l1-voltage',
        title: 'Voltage (Electric Potential Difference)',
        content: [
          { type: 'heading', level: 3, value: 'What is Voltage?' },
          { type: 'text', value: 'Voltage, also known as electric potential difference or electromotive force (EMF), is the "pressure" from an electrical circuit\'s power source that pushes charged electrons (current) through a conducting loop. Think of it like water pressure in a pipe: higher pressure means more force to move water.' },
          { type: 'image', src: 'https://picsum.photos/seed/voltage_analogy/600/300', alt: 'Voltage Water Analogy', caption: 'Analogy: Voltage is like water pressure.' },
          { type: 'info', title: 'Symbol and Unit', value: 'Symbol: V or E. Unit: Volt (V).' },
          { type: 'text', value: 'In biomedical applications, precise voltage control is critical. For example, pacemakers deliver carefully timed voltage pulses to regulate heart rhythm. EEG (Electroencephalogram) devices measure tiny voltage fluctuations in the brain.' },
        ],
      },
      {
        id: 'l1-current',
        title: 'Current (Electric Flow)',
        content: [
          { type: 'heading', level: 3, value: 'What is Current?' },
          { type: 'text', value: 'Electric current is the rate of flow of electric charge (usually electrons) past a point or region. Imagine it as the amount of water flowing through a pipe per second.' },
          { type: 'image', src: 'https://picsum.photos/seed/current_analogy/600/300', alt: 'Current Water Analogy', caption: 'Analogy: Current is like water flow rate.' },
          { type: 'info', title: 'Symbol and Unit', value: 'Symbol: I. Unit: Ampere (A), often called "amps".' },
          { type: 'text', value: 'Biomedical devices often deal with very small currents (microamperes or nanoamperes), especially in biosensing. Electrical stimulation therapies also rely on controlled current delivery.' },
        ],
      },
      {
        id: 'l1-resistance',
        title: 'Resistance (Opposition to Flow)',
        content: [
          { type: 'heading', level: 3, value: 'What is Resistance?' },
          { type: 'text', value: 'Electrical resistance is a measure of the opposition to current flow in an electrical circuit. It\'s like the friction or narrowness of a pipe that restricts water flow.' },
          { type: 'image', src: 'https://picsum.photos/seed/resistance_analogy/600/300', alt: 'Resistance Water Analogy', caption: 'Analogy: Resistance is like a narrow pipe restricting flow.' },
          { type: 'info', title: 'Symbol and Unit', value: 'Symbol: R. Unit: Ohm (Ω).' },
          { type: 'text', value: 'All materials have some resistance. Conductors (like copper wire) have low resistance, while insulators (like rubber) have very high resistance. In biomedical sensors, changes in resistance are often used to measure physiological parameters (e.g., thermistors for temperature, strain gauges for pressure).' },
        ],
      },
      {
        id: 'l1-power',
        title: 'Electrical Power',
        content: [
          { type: 'heading', level: 3, value: 'What is Power?' },
          { type: 'text', value: 'Electrical power is the rate at which electrical energy is transferred by an electric circuit. It is the product of voltage and current.' },
          { type: 'formula', value: 'P = V * I' },
          { type: 'info', title: 'Symbol and Unit', value: 'Symbol: P. Unit: Watt (W).' },
          { type: 'text', value: 'Power consumption and heat dissipation are important considerations in medical device design, especially for implantable or wearable devices. Efficient power usage prolongs battery life and prevents tissue damage from overheating.' },
        ],
      }
    ],
    quiz: {
      id: 'q-basic-concepts',
      title: 'Basic Concepts Quiz',
      questions: [
        { id: 'q1bc1', text: 'What is the unit of Voltage?', options: ['Ampere (A)', 'Ohm (Ω)', 'Volt (V)', 'Watt (W)'], correctAnswer: 'Volt (V)', explanation: 'Voltage is measured in Volts (V). Ampere is for current, Ohm for resistance, and Watt for power.' },
        { id: 'q1bc2', text: 'Which quantity represents the flow of electric charge?', options: ['Voltage', 'Current', 'Resistance', 'Power'], correctAnswer: 'Current', explanation: 'Current (I) is the rate of flow of electric charge, measured in Amperes (A).' },
        { id: 'q1bc3', text: 'What does electrical resistance measure?', options: ['The force pushing electrons', 'The rate of energy transfer', 'The opposition to current flow', 'The amount of charge stored'], correctAnswer: 'The opposition to current flow', explanation: 'Resistance (R), measured in Ohms (Ω), opposes the flow of current.' },
        { id: 'q1bc4', text: 'If Voltage is like water pressure, Current is like:', options: ['Pipe diameter', 'Water flow rate', 'Water temperature', 'Water purity'], correctAnswer: 'Water flow rate', explanation: 'Current is analogous to the volume of water flowing per unit time.' },
      ]
    }
  },
  {
    id: 'ohms-law',
    title: 'Ohm\'s Law',
    description: 'Learn the fundamental relationship between voltage, current, and resistance.',
    icon: 'CalculatorIcon',
    lessons: [
      {
        id: 'l2-intro',
        title: 'Introduction to Ohm\'s Law',
        content: [
          { type: 'text', value: 'Ohm\'s Law is a cornerstone of electronics. It describes the relationship between voltage (V), current (I), and resistance (R) in an electrical circuit.' },
          { type: 'heading', level: 3, value: 'The Formula' },
          { type: 'formula', value: 'V = I * R' },
          { type: 'text', value: 'Where:' },
          { type: 'list', items: ['V = Voltage in Volts (V)', 'I = Current in Amperes (A)', 'R = Resistance in Ohms (Ω)'] },
          { type: 'text', value: 'This formula can be rearranged to solve for any of the three variables:'},
          { type: 'list', items: ['I = V / R', 'R = V / I'] },
          { type: 'image', src: 'https://picsum.photos/seed/ohms_law_triangle/400/400', alt: 'Ohm\'s Law Triangle', caption: 'The Ohm\'s Law triangle is a helpful mnemonic.'},
          { type: 'info', title: 'Key Takeaway', value: 'Ohm\'s Law helps us predict how a circuit behaves and is essential for circuit design and analysis.' },
          { type: 'heading', level: 3, value: 'Interactive Calculator'},
          { type: 'text', value: 'Use the calculator below to experiment with Ohm\'s Law. Enter any two values to find the third.'},
          { type: 'interactive', componentKey: 'OhmLawCalculator' }
        ],
      }
    ],
    quiz: {
      id: 'q-ohms-law',
      title: 'Ohm\'s Law Quiz',
      questions: [
        { id: 'q2ol1', text: 'According to Ohm\'s Law, if voltage increases and resistance stays the same, what happens to current?', options: ['Increases', 'Decreases', 'Stays the same', 'Becomes zero'], correctAnswer: 'Increases', explanation: 'Since I = V/R, if V increases and R is constant, I must increase.' },
        { id: 'q2ol2', text: 'A circuit has a 12V supply and a 4Ω resistor. What is the current?', options: ['3A', '48A', '0.33A', '16A'], correctAnswer: '3A', explanation: 'I = V/R = 12V / 4Ω = 3A.' },
        { id: 'q2ol3', text: 'If 2A of current flows through a 5Ω resistor, what is the voltage across it?', options: ['2.5V', '0.4V', '7V', '10V'], correctAnswer: '10V', explanation: 'V = I*R = 2A * 5Ω = 10V.' },
      ]
    }
  },
  {
    id: 'passive-components',
    title: 'Passive Components',
    description: 'Explore resistors, capacitors, and inductors - the building blocks of electronic circuits.',
    icon: 'ChipIcon',
    lessons: [
      {
        id: 'l3-intro-passive',
        title: 'What are Passive Components?',
        content: [
            { type: 'text', value: 'Passive components are electronic components that do not require an external power source to operate, and they cannot amplify or generate electrical signals. They primarily consume, store, or dissipate energy. The three main types are resistors, capacitors, and inductors.' }
        ]
      },
      {
        id: 'l3-resistors',
        title: 'Resistors',
        content: [
          { type: 'heading', level: 3, value: 'Function' },
          { type: 'text', value: 'Resistors limit the flow of electric current. They are used to control voltage and current levels within circuits, protect components, and provide specific resistances for timing or filtering applications.' },
          { type: 'image', src: 'https://picsum.photos/seed/resistors_array/600/200', alt: 'Various Resistors', caption: 'Common types of resistors.' },
          { type: 'heading', level: 3, value: 'Symbol' },
          { type: 'image', src: 'https://upload.wikimedia.org/wikipedia/commons/thumb/8/84/Electronic_component_resistor_symbol.svg/200px-Electronic_component_resistor_symbol.svg.png', alt: 'Resistor Schematic Symbol', caption: 'IEC Resistor Symbol (left) and ANSI Resistor Symbol (right, less common internationally).' },
          { type: 'info', title: 'Key Property', value: 'Resistance (R), measured in Ohms (Ω).' },
          { type: 'text', value: 'In biomedical devices, resistors are used in sensor interfaces (e.g., voltage dividers for thermistors), current limiting for LEDs or sensitive components, and in feedback networks for amplifiers.' },
        ],
      },
      {
        id: 'l3-capacitors',
        title: 'Capacitors',
        content: [
          { type: 'heading', level: 3, value: 'Function' },
          { type: 'text', value: 'Capacitors store electrical energy in an electric field. They consist of two conductive plates separated by an insulating material called a dielectric. Capacitors can block direct current (DC) while allowing alternating current (AC) to pass, and are used for filtering, smoothing power supplies, and in timing circuits.' },
          { type: 'image', src: 'https://picsum.photos/seed/capacitors_array/600/200', alt: 'Various Capacitors', caption: 'Different types of capacitors.' },
          { type: 'heading', level: 3, value: 'Symbol' },
          { type: 'image', src: 'https://upload.wikimedia.org/wikipedia/commons/thumb/9/95/Electronic_component_capacitor_symbol.svg/200px-Electronic_component_capacitor_symbol.svg.png', alt: 'Capacitor Schematic Symbol', caption: 'Non-polarized (left) and polarized (right) capacitor symbols.' },
          { type: 'info', title: 'Key Property', value: 'Capacitance (C), measured in Farads (F). Often in microfarads (μF), nanofarads (nF), or picofarads (pF).' },
          { type: 'text', value: 'Capacitors are vital in biomedical ECG/EEG amplifiers for filtering noise and coupling signals. Defibrillators use large capacitors to store and deliver a high-energy electrical charge.' },
        ],
      },
      {
        id: 'l3-inductors',
        title: 'Inductors',
        content: [
          { type: 'heading', level: 3, value: 'Function' },
          { type: 'text', value: 'Inductors store energy in a magnetic field when electric current flows through them. They typically consist of a coil of wire. Inductors resist changes in current and are used in filters, oscillators, and transformers.' },
          { type: 'image', src: 'https://picsum.photos/seed/inductors_array/600/200', alt: 'Various Inductors', caption: 'Different types of inductors.' },
          { type: 'heading', level: 3, value: 'Symbol' },
          { type: 'image', src: 'https://upload.wikimedia.org/wikipedia/commons/thumb/4/44/Electronic_component_inductor_symbol.svg/200px-Electronic_component_inductor_symbol.svg.png', alt: 'Inductor Schematic Symbol', caption: 'Inductor Symbol.' },
          { type: 'info', title: 'Key Property', value: 'Inductance (L), measured in Henrys (H). Often in millihenrys (mH) or microhenrys (μH).' },
          { type: 'text', value: 'Inductors are used in biomedical applications like MRI (Magnetic Resonance Imaging) systems (large superconducting magnets), power supplies for filtering, and in wireless power transfer for implantable devices.' },
        ],
      }
    ],
    quiz: {
      id: 'q-passive-components',
      title: 'Passive Components Quiz',
      questions: [
        { id: 'q3pc1', text: 'Which component is primarily used to limit current flow?', options: ['Capacitor', 'Inductor', 'Resistor', 'Transistor'], correctAnswer: 'Resistor', explanation: 'Resistors oppose the flow of current.' },
        { id: 'q3pc2', text: 'What is the primary function of a capacitor?', options: ['Store energy in a magnetic field', 'Store energy in an electric field', 'Dissipate energy as heat', 'Amplify signals'], correctAnswer: 'Store energy in an electric field', explanation: 'Capacitors store charge, creating an electric field between their plates.' },
        { id: 'q3pc3', text: 'The unit of inductance is:', options: ['Ohm (Ω)', 'Farad (F)', 'Henry (H)', 'Watt (W)'], correctAnswer: 'Henry (H)', explanation: 'Inductance (L) is measured in Henrys (H).' },
      ]
    }
  },
  {
    id: 'circuit-analysis',
    title: 'Basic Circuit Analysis',
    description: 'Introduction to series and parallel circuits, and Kirchhoff\'s Laws.',
    icon: 'CircuitBoardIcon',
    lessons: [
      {
        id: 'l4-series-circuits',
        title: 'Series Circuits',
        content: [
          { type: 'heading', level: 3, value: 'What is a Series Circuit?' },
          { type: 'text', value: 'In a series circuit, components are connected end-to-end, forming a single path for current to flow.' },
          { type: 'image', src: 'https://picsum.photos/seed/series_circuit_diagram/500/300', alt: 'Series Circuit Diagram', caption: 'Three resistors (R1, R2, R3) in series with a voltage source (V).' },
          { type: 'heading', level: 3, value: 'Key Characteristics:' },
          { type: 'list', items: [
              'Current: The same current flows through all components (I_total = I1 = I2 = I3).',
              'Resistance: The total resistance is the sum of individual resistances (R_total = R1 + R2 + R3).',
              'Voltage: The total voltage is divided among the components (V_total = V1 + V2 + V3). This is known as voltage division.'
            ]
          },
          { type: 'info', title: 'Analogy', value: 'Think of a single-lane road. All cars (current) must follow the same path, and any blockage (resistance) affects the entire flow.' },
        ],
      },
      {
        id: 'l4-parallel-circuits',
        title: 'Parallel Circuits',
        content: [
          { type: 'heading', level: 3, value: 'What is a Parallel Circuit?' },
          { type: 'text', value: 'In a parallel circuit, components are connected across common points (nodes), providing multiple paths for current to flow.' },
          { type: 'image', src: 'https://picsum.photos/seed/parallel_circuit_diagram/500/300', alt: 'Parallel Circuit Diagram', caption: 'Three resistors (R1, R2, R3) in parallel with a voltage source (V).' },
          { type: 'heading', level: 3, value: 'Key Characteristics:' },
          { type: 'list', items: [
              'Voltage: The same voltage is applied across all components (V_total = V1 = V2 = V3).',
              'Current: The total current is the sum of currents through each branch (I_total = I1 + I2 + I3). This is known as current division.',
              'Resistance: The reciprocal of the total resistance is the sum of the reciprocals of individual resistances (1/R_total = 1/R1 + 1/R2 + 1/R3). Total resistance in a parallel circuit is always less than the smallest individual resistance.'
            ]
          },
          { type: 'info', title: 'Analogy', value: 'Think of a multi-lane highway. Cars (current) can choose different lanes (paths), but the start and end points (voltage) are the same for all lanes.' },
        ],
      },
      {
        id: 'l4-kirchhoffs-laws',
        title: 'Kirchhoff\'s Laws (Introduction)',
        content: [
          { type: 'heading', level: 3, value: 'Kirchhoff\'s Current Law (KCL)' },
          { type: 'text', value: 'KCL states that the algebraic sum of currents entering a node (or junction) in an electrical circuit is equal to the sum of currents leaving the node. Essentially, charge is conserved – what goes in must come out.' },
          { type: 'formula', value: 'Σ I_entering = Σ I_leaving' },
          { type: 'image', src: 'https://picsum.photos/seed/kcl_diagram/400/250', alt: 'KCL Diagram', caption: 'Illustration of KCL at a node.' },
          { type: 'heading', level: 3, value: 'Kirchhoff\'s Voltage Law (KVL)' },
          { type: 'text', value: 'KVL states that the algebraic sum of all voltages around any closed loop or path in a circuit is equal to zero. Essentially, energy is conserved – the sum of voltage drops equals the sum of voltage rises in a loop.' },
          { type: 'formula', value: 'Σ V_loop = 0' },
          { type: 'image', src: 'https://picsum.photos/seed/kvl_diagram/400/250', alt: 'KVL Diagram', caption: 'Illustration of KVL in a loop.' },
          { type: 'info', title: 'Importance', value: 'Kirchhoff\'s Laws are fundamental for analyzing more complex circuits where simple series/parallel reductions are not enough.' }
        ],
      }
    ],
    quiz: {
      id: 'q-circuit-analysis',
      title: 'Circuit Analysis Quiz',
      questions: [
        { id: 'q4ca1', text: 'In a series circuit, which quantity is the same through all components?', options: ['Voltage', 'Resistance', 'Power', 'Current'], correctAnswer: 'Current', explanation: 'Current has only one path in a series circuit, so it\'s the same through all components.' },
        { id: 'q4ca2', text: 'In a parallel circuit, the total resistance is always:', options: ['Greater than the largest resistance', 'Equal to the sum of resistances', 'Less than the smallest resistance', 'Equal to the average resistance'], correctAnswer: 'Less than the smallest resistance', explanation: 'Adding parallel paths for current flow reduces overall resistance.' },
        { id: 'q4ca3', text: 'Kirchhoff\'s Current Law (KCL) is based on the conservation of:', options: ['Energy', 'Charge', 'Momentum', 'Power'], correctAnswer: 'Charge', explanation: 'KCL states that current (flow of charge) entering a node equals current leaving it.' },
      ]
    }
  },
  // INTERMEDIATE LEVEL MODULES START HERE
  {
    id: 'diodes-applications',
    title: 'Diodes & Applications',
    description: 'Intermediate: Explore diodes, their characteristics, and applications like rectification with interactive simulations.',
    icon: 'ChipIcon',
    lessons: [
      {
        id: 'l5-pn-junction',
        title: 'PN Junction Diodes',
        content: [
          { type: 'heading', level: 2, value: 'The PN Junction Diode' },
          { type: 'text', value: 'A PN junction diode is formed by joining P-type and N-type semiconductor materials. It allows current to flow easily in one direction (forward bias) while restricting current flow in the opposite direction (reverse bias).' },
          { type: 'image', src: 'https://picsum.photos/seed/diode_symbol/400/200', alt: 'Diode Symbol and PN Junction', caption: 'Diode symbol (left) and basic PN junction structure (right).' },
          { type: 'heading', level: 3, value: 'Forward Bias' },
          { type: 'text', value: 'When the P-type side (anode) is connected to a positive terminal and N-type side (cathode) to negative, the depletion region narrows, and current flows. A small forward voltage (typically ~0.7V for silicon, ~0.3V for germanium) is needed to overcome the barrier potential.' },
          { type: 'heading', level: 3, value: 'Reverse Bias' },
          { type: 'text', value: 'When the P-type side is connected to a negative terminal and N-type to positive, the depletion region widens, blocking significant current flow. Only a small reverse saturation current (leakage current) exists, until the breakdown voltage is reached.' },
          { type: 'image', src: 'https://picsum.photos/seed/diode_vi_curve/500/300', alt: 'Diode V-I Characteristic Curve', caption: 'Typical V-I curve for a silicon diode, showing forward bias, reverse bias, and breakdown regions.' },
          { type: 'info', title: 'Interactive Simulation Idea', value: 'An interactive simulation showing electron/hole movement and depletion region changes under forward and reverse bias conditions.' },
        ]
      },
      {
        id: 'l5-diode-apps',
        title: 'Common Diode Applications',
        content: [
          { type: 'heading', level: 2, value: 'Rectification' },
          { type: 'text', value: 'Rectification is the process of converting alternating current (AC) to direct current (DC). Diodes are key components in rectifier circuits used in power supplies.' },
          { type: 'heading', level: 3, value: 'Half-Wave Rectifier' },
          { type: 'text', value: 'Uses a single diode to pass only one half (either positive or negative) of the AC waveform, blocking the other half.' },
          { type: 'image', src: 'https://picsum.photos/seed/half_wave_rect/500/250', alt: 'Half-Wave Rectifier Circuit', caption: 'Half-wave rectifier circuit and its output waveform.' },
          { type: 'heading', level: 3, value: 'Full-Wave Rectifier' },
          { type: 'text', value: 'Utilizes multiple diodes (e.g., a center-tapped transformer with two diodes, or a bridge rectifier with four diodes) to convert both halves of the AC waveform to pulsating DC, resulting in a smoother output than half-wave rectification.' },
          { type: 'image', src: 'https://picsum.photos/seed/full_wave_rect/500/250', alt: 'Full-Wave Bridge Rectifier Circuit', caption: 'Full-wave bridge rectifier circuit and its output waveform.' },
          { type: 'heading', level: 2, value: 'Other Applications' },
          { type: 'list', items: [
              'Clipping Circuits: Used to limit or "clip" parts of a signal waveform to a certain voltage level.',
              'Clamping Circuits (DC Restorers): Used to shift the DC level of a waveform.',
              'Voltage Protection: Protect circuits from reverse voltage.',
              'LEDs: Light Emitting Diodes are special types of diodes that emit light when forward biased.'
            ]
          },
          { type: 'info', title: 'Biomedical Note', value: 'Rectifiers are crucial in power supplies for almost all medical devices. LEDs are widely used for indication and in optical sensors (e.g., pulse oximeters).' },
        ]
      }
    ],
    quiz: {
      id: 'q-diodes',
      title: 'Diodes Quiz',
      questions: [
        { id: 'q5d1', text: 'What is the typical forward voltage drop across a silicon PN junction diode when it conducts?', options: ['0.1V', '0.3V', '0.7V', '1.2V'], correctAnswer: '0.7V', explanation: 'Silicon diodes typically require about 0.7V to become significantly forward biased and conduct current.' },
        { id: 'q5d2', text: 'The process of converting AC to DC using diodes is known as:', options: ['Amplification', 'Oscillation', 'Rectification', 'Modulation'], correctAnswer: 'Rectification', explanation: 'Rectification uses the one-way current flow property of diodes to convert AC, which periodically reverses direction, into DC.' },
        { id: 'q5d3', text: 'In a reverse-biased diode, the current flow is typically:', options: ['Very large', 'Zero', 'A small leakage current', 'Equal to forward current'], correctAnswer: 'A small leakage current', explanation: 'A reverse-biased diode ideally blocks current, but a small leakage current (reverse saturation current) flows.'}
      ]
    }
  },
  {
    id: 'bjt-transistors',
    title: 'BJT Transistors',
    description: 'Intermediate: Understand Bipolar Junction Transistors (BJTs) - NPN/PNP types, operating regions, and their use as amplifiers and switches.',
    icon: 'ChipIcon',
    lessons: [
      {
        id: 'l6-intro-bjt',
        title: 'Introduction to BJTs',
        content: [
          { type: 'heading', level: 2, value: 'Bipolar Junction Transistors (BJTs)' },
          { type: 'text', value: 'Bipolar Junction Transistors (BJTs) are three-terminal semiconductor devices that can amplify electrical signals or act as electronic switches. They are called "bipolar" because their operation involves two types of charge carriers: electrons and holes.' },
          { type: 'heading', level: 3, value: 'Types: NPN and PNP' },
          { type: 'text', value: 'BJTs consist of three alternating layers of P-type and N-type semiconductor material. An NPN transistor has a P-type layer (base) sandwiched between two N-type layers (emitter and collector). A PNP transistor has an N-type layer (base) between two P-type layers (emitter and collector).' },
          { type: 'image', src: 'https://picsum.photos/seed/bjt_symbols_struct/500/250', alt: 'NPN and PNP BJT Symbols & Structures', caption: 'Circuit symbols and basic structures for NPN (arrow out) and PNP (arrow in) BJTs.' },
          { type: 'heading', level: 3, value: 'Terminals: Emitter (E), Base (B), Collector (C)' },
          { type: 'list', items: [
              'Emitter (E): Heavily doped region that emits (injects) charge carriers into the base.',
              'Base (B): Thin, lightly doped region that controls the flow of charge carriers between emitter and collector.',
              'Collector (C): Moderately doped region that collects charge carriers from the base.'
            ] 
          },
          { type: 'heading', level: 3, value: 'Operating Regions' },
          { type: 'list', items: [
              'Cut-off: Both Emitter-Base (EB) and Collector-Base (CB) junctions are reverse biased. The transistor acts like an open switch (no collector current).',
              'Active: The EB junction is forward biased, and the CB junction is reverse biased. The transistor acts as an amplifier; collector current is proportional to base current (IC = β * IB).',
              'Saturation: Both EB and CB junctions are forward biased. The transistor acts like a closed switch (maximum collector current, small VCE).'
            ] 
          },
           { type: 'info', title: 'Interactive Simulation Idea', value: 'A simulation allowing users to vary base current (IB) and observe the effect on collector current (IC) and collector-emitter voltage (VCE) across different operating regions.' },
        ]
      },
      {
        id: 'l6-bjt-apps',
        title: 'BJT as an Amplifier and Switch',
        content: [
          { type: 'heading', level: 2, value: 'BJT as an Amplifier' },
          { type: 'text', value: 'When operating in the active region, a small change in the base current (IB) results in a much larger change in the collector current (IC). This current gain is denoted by β (beta) or hFE. The voltage gain can also be achieved depending on the circuit configuration (e.g., common-emitter).' },
          { type: 'formula', value: 'I_C = β * I_B' },
          { type: 'image', src: 'https://picsum.photos/seed/bjt_amplifier_ce/500/300', alt: 'Common-Emitter Amplifier Circuit', caption: 'A basic common-emitter BJT amplifier configuration, often used for voltage amplification.' },
          { type: 'heading', level: 2, value: 'BJT as a Switch' },
          { type: 'text', value: 'By driving the BJT between its cut-off (OFF state) and saturation (ON state) regions, it can function as an electronic switch. A small base current can control a larger current through a load connected to the collector.' },
          { type: 'image', src: 'https://picsum.photos/seed/bjt_switch_led/500/300', alt: 'BJT Switch Circuit with LED', caption: 'Using an NPN BJT to switch an LED on/off. When base current flows, the BJT saturates, turning the LED on.' },
          { type: 'info', title: 'Biomedical Relevance', value: 'BJTs are fundamental in discrete component biosignal amplifiers (e.g., for ECG pre-amplifiers before widespread IC use), and as switches to control actuators, relays, or other components in various medical devices.' },
        ]
      }
    ],
    quiz: {
      id: 'q-bjt',
      title: 'BJT Quiz',
      questions: [
        { id: 'q6bjt1', text: 'What are the three terminals of a Bipolar Junction Transistor?', options: ['Source, Drain, Gate', 'Anode, Cathode, Gate', 'Emitter, Base, Collector', 'Input, Output, Ground'], correctAnswer: 'Emitter, Base, Collector', explanation: 'A BJT has an Emitter (E), a Base (B), and a Collector (C) terminal.' },
        { id: 'q6bjt2', text: 'In which operating region is a BJT primarily used for signal amplification?', options: ['Cut-off Region', 'Active Region', 'Saturation Region', 'Breakdown Region'], correctAnswer: 'Active Region', explanation: 'The active region provides a linear relationship between base current and collector current (current gain), suitable for amplification.' },
        { id: 'q6bjt3', text: 'An NPN BJT acts like a closed switch when it is in the:', options: ['Cut-off region', 'Active region', 'Saturation region', 'Reverse-active region'], correctAnswer: 'Saturation region', explanation: 'In saturation, both junctions are forward biased, allowing maximum current flow with minimal voltage drop across collector-emitter, similar to a closed switch.'}
      ]
    }
  },
  {
    id: 'fet-transistors',
    title: 'FET Transistors',
    description: 'Intermediate: Explore Field-Effect Transistors (JFETs, MOSFETs), their structure, operation, and advantages in various applications.',
    icon: 'ChipIcon',
    lessons: [
      {
        id: 'l7-intro-fet',
        title: 'Introduction to FETs',
        content: [
          { type: 'heading', level: 2, value: 'Field-Effect Transistors (FETs)' },
          { type: 'text', value: 'Field-Effect Transistors (FETs) are unipolar devices, meaning their operation involves only one type of charge carrier (either electrons for N-channel or holes for P-channel). Current flow between two terminals (Source and Drain) is controlled by an electric field established by a voltage applied to a third terminal (Gate).' },
          { type: 'heading', level: 3, value: 'Junction FET (JFET)' },
          { type: 'text', value: 'JFETs have a channel made of N-type or P-type semiconductor material. The gate is made of the opposite type material, forming a PN junction with the channel. Applying a reverse bias to this gate-channel junction creates a depletion region that narrows the channel, thus controlling current flow.' },
          { type: 'image', src: 'https://picsum.photos/seed/jfet_symbols_struct/500/250', alt: 'N-Channel and P-Channel JFET Symbols & Structures', caption: 'Symbols and basic structures for N-Channel and P-Channel JFETs.' },
          { type: 'heading', level: 3, value: 'Metal-Oxide-Semiconductor FET (MOSFET)' },
          { type: 'text', value: 'MOSFETs are the most widely used type of FET. The gate is electrically insulated from the channel by a very thin layer of oxide material (typically Silicon Dioxide, SiO2). This insulation results in extremely high input impedance.' },
          { type: 'list', items: [
              'Enhancement Mode (E-MOSFET): No conductive channel exists with zero gate voltage. Applying an appropriate gate voltage "enhances" conductivity by forming a channel between source and drain.',
              'Depletion Mode (D-MOSFET): A conductive channel exists even with zero gate voltage. Applying gate voltage can either enhance channel conductivity further or "deplete" (reduce) it by reducing charge carriers.'
            ]
          },
          { type: 'image', src: 'https://picsum.photos/seed/mosfet_symbols_types/500/300', alt: 'MOSFET Symbols (Enhancement and Depletion)', caption: 'Common symbols for N-Channel and P-Channel Enhancement-mode and Depletion-mode MOSFETs.' },
           { type: 'info', title: 'Interactive Simulation Idea', value: 'A simulation demonstrating how gate voltage (V_GS) modulates channel conductivity and drain current (I_D) in JFETs and different types of MOSFETs.' },
        ]
      },
      {
        id: 'l7-fet-chars',
        title: 'FET Characteristics and Applications',
        content: [
          { type: 'heading', level: 2, value: 'Key Characteristics of FETs' },
          { type: 'list', items: [
              'Very High Input Impedance: Especially MOSFETs, due to the insulated gate. This means they draw virtually no current from the input signal source, making them ideal for interfacing with high-impedance sensors or previous stages.',
              'Voltage Controlled Device: The drain current (I_D) is controlled by the gate-source voltage (V_GS).',
              'Typically Lower Noise Figure: Compared to BJTs in some high-frequency applications.',
              'Thermal Stability: Less prone to thermal runaway than BJTs.',
              'Faster Switching Speeds (MOSFETs): Due to being majority carrier devices.'
            ]
          },
          { type: 'heading', level: 2, value: 'Comparison: FETs vs. BJTs' },
          { type: 'text', value: 'BJTs are current-controlled devices with lower input impedance, while FETs are voltage-controlled devices with high input impedance. The choice between them depends on specific application requirements like input signal characteristics, power handling, switching speed, and cost.' },
          { type: 'heading', level: 2, value: 'Applications' },
          { type: 'text', value: 'FETs, particularly MOSFETs, are the backbone of modern digital electronics (e.g., microprocessors, memory using CMOS technology). They are also extensively used in analog circuits (amplifiers, active filters), power electronics (power MOSFETs for switching power supplies, motor control), and as analog switches.'},
          { type: 'image', src: 'https://picsum.photos/seed/mosfet_id_vds_curves/500/300', alt: 'MOSFET Drain Characteristic Curves (ID vs VDS)', caption: 'Typical drain characteristic curves (I_D vs V_DS for various V_GS values) for an N-Channel E-MOSFET.' },
          { type: 'info', title: 'Biomedical Context', value: 'The high input impedance of FETs (especially MOSFETs) makes them exceptionally well-suited for amplifying very weak biopotentials (e.g., from ECG, EEG, EMG electrodes) with minimal signal loading. CMOS technology using MOSFETs is crucial for low-power, high-density integrated circuits in portable and implantable medical devices.' },
        ]
      }
    ],
    quiz: {
      id: 'q-fet',
      title: 'FET Quiz',
      questions: [
        { id: 'q7fet1', text: 'What does MOSFET stand for?', options: ['Metal-Oxide Semiconductor Field-Effect Transistor', 'Multiple-Output Silicon Field-Effect Transistor', 'Main-Output System FET', 'Meta-Organic Semiconductor FET'], correctAnswer: 'Metal-Oxide Semiconductor Field-Effect Transistor', explanation: 'MOSFET is an acronym for Metal-Oxide Semiconductor Field-Effect Transistor, highlighting its gate structure.' },
        { id: 'q7fet2', text: 'A key advantage of MOSFETs, due to their insulated gate, is their:', options: ['Lower manufacturing cost', 'Higher current gain (β)', 'Very high input impedance', 'Ability to operate at higher temperatures'], correctAnswer: 'Very high input impedance', explanation: 'The insulated gate in MOSFETs prevents gate current flow, resulting in extremely high input impedance, which is beneficial for many applications.' },
        { id: 'q7fet3', text: 'FETs are considered to be:', options: ['Current-controlled devices', 'Voltage-controlled devices', 'Power-controlled devices', 'Resistance-controlled devices'], correctAnswer: 'Voltage-controlled devices', explanation: 'In FETs, the voltage applied to the gate terminal controls the current flowing between the source and drain terminals.'}
      ]
    }
  },
  {
    id: 'op-amps',
    title: 'Operational Amplifiers (Op-Amps)',
    description: 'Intermediate: Discover the versatile operational amplifier, its ideal characteristics, and common circuit configurations like inverting and non-inverting amplifiers.',
    icon: 'CircuitBoardIcon',
    lessons: [
      {
        id: 'l8-intro-opamp',
        title: 'Introduction to Operational Amplifiers',
        content: [
          { type: 'heading', level: 2, value: 'What is an Op-Amp?' },
          { type: 'text', value: 'An operational amplifier (op-amp) is a versatile, high-gain, direct-coupled (DC) differential voltage amplifier, typically available as an integrated circuit (IC). It has two inputs: an inverting input (-) and a non-inverting input (+), and usually one output. Op-amps are fundamental building blocks in analog electronics, used for amplification, filtering, signal conditioning, and mathematical operations.' },
          { type: 'image', src: 'https://picsum.photos/seed/opamp_symbol_ic_pkg/500/250', alt: 'Op-Amp Symbol and IC Package', caption: 'Standard op-amp circuit symbol (left) and a common dual in-line package (DIP) IC (right).' },
          { type: 'heading', level: 3, value: 'Ideal Op-Amp Characteristics' },
          { type: 'text', value: 'While real op-amps have limitations, the concept of an "ideal" op-amp simplifies analysis and design. Its characteristics are:' },
          { type: 'list', items: [
              'Infinite Open-Loop Voltage Gain (A_OL = ∞): The gain without any feedback is extremely large.',
              'Infinite Input Impedance (Z_in = ∞): It draws no current from the input sources (i.e., input currents to both terminals are zero).',
              'Zero Output Impedance (Z_out = 0): The output voltage is unaffected by the load connected to it; it can supply any amount of current.',
              'Infinite Bandwidth (BW = ∞): It can amplify signals of any frequency equally, from DC to very high frequencies.',
              'Zero Offset Voltage (V_offset = 0): If the voltage difference between the inverting and non-inverting inputs is zero, the output voltage is zero.',
              'Infinite Common-Mode Rejection Ratio (CMRR = ∞): It perfectly rejects signals common to both inputs.'
            ]
          },
          { type: 'text', value: 'Real op-amps approximate these ideals, making them highly effective in practical circuits, especially when used with negative feedback.' },
        ]
      },
      {
        id: 'l8-opamp-circuits',
        title: 'Basic Op-Amp Configurations with Negative Feedback',
        content: [
          { type: 'text', value: 'Op-amps are almost always used with external components connected in a negative feedback configuration. Negative feedback stabilizes the gain, improves linearity, and makes the circuit performance predictable and dependent on the external components rather than the op-amp\'s open-loop characteristics.' },
          { type: 'heading', level: 3, value: 'Inverting Amplifier' },
          { type: 'text', value: 'The input signal is applied to the inverting (-) input through an input resistor (R_in), and the non-inverting (+) input is typically connected to ground. A feedback resistor (R_f) connects the output to the inverting input. The output is an amplified and inverted (180° phase shift) version of the input.' },
          { type: 'formula', value: 'V_out = -(R_f / R_in) * V_in' },
          { type: 'image', src: 'https://picsum.photos/seed/inverting_opamp_circuit/500/300', alt: 'Inverting Op-Amp Circuit Diagram', caption: 'Inverting amplifier configuration. The voltage gain A_v = -R_f / R_in.' },
          { type: 'heading', level: 3, value: 'Non-Inverting Amplifier' },
          { type: 'text', value: 'The input signal is applied directly to the non-inverting (+) input. The feedback network (R_f and R_in) is connected from the output to the inverting (-) input, with R_in going to ground. The output is an amplified version of the input, in phase with the input.' },
          { type: 'formula', value: 'V_out = (1 + R_f / R_in) * V_in' },
          { type: 'image', src: 'https://picsum.photos/seed/noninverting_opamp_circuit/500/300', alt: 'Non-Inverting Op-Amp Circuit Diagram', caption: 'Non-inverting amplifier configuration. The voltage gain A_v = 1 + R_f / R_in.' },
          { type: 'heading', level: 3, value: 'Voltage Follower (Buffer)' },
          { type: 'text', value: 'A special case of the non-inverting amplifier where R_f = 0 (direct connection from output to inverting input) and R_in approaches infinity (is removed). It has a voltage gain of exactly 1. Its primary use is as a buffer to provide impedance matching: it presents a high input impedance to the source and a low output impedance to the load.' },
          { type: 'formula', value: 'V_out = V_in' },
          { type: 'image', src: 'https://picsum.photos/seed/voltage_follower_opamp/500/250', alt: 'Voltage Follower (Buffer) Circuit', caption: 'Voltage follower configuration. Gain A_v = 1. Provides impedance isolation.' },
          { type: 'info', title: 'Biomedical Applications', value: 'Op-amps are ubiquitous in medical electronics: amplifying weak biopotentials (ECG, EEG, EMG), active filters for noise removal, signal conditioning for sensors, and in precision instrumentation amplifiers.' },
          { type: 'info', title: 'Interactive Simulation Idea', value: 'An interactive op-amp circuit builder where users can select configurations (inverting, non-inverting, follower, summer, differentiator, integrator), change resistor/capacitor values, apply input signals (DC/AC), and observe the output voltage/waveform and gain.' },
        ]
      }
    ],
    quiz: {
      id: 'q-opamp',
      title: 'Op-Amp Quiz',
      questions: [
        { id: 'q8op1', text: 'Which of these is a characteristic of an IDEAL operational amplifier?', options: ['Zero input impedance', 'Finite open-loop gain', 'Zero output impedance', 'Finite bandwidth'], correctAnswer: 'Zero output impedance', explanation: 'An ideal op-amp has infinite open-loop gain, infinite input impedance, zero output impedance, and infinite bandwidth.' },
        { id: 'q8op2', text: 'What is the voltage gain of an ideal voltage follower (buffer) op-amp configuration?', options: ['0', '1', '-1', 'Infinity'], correctAnswer: '1', explanation: 'A voltage follower (buffer) has a voltage gain of 1 (V_out = V_in) and provides no phase inversion. It is used for impedance matching.' },
        { id: 'q8op3', text: 'In an inverting op-amp configuration, if the feedback resistor Rf = 20kΩ and the input resistor Rin = 5kΩ, what is the closed-loop voltage gain?', options: ['4', '-4', '5', '-5'], correctAnswer: '-4', explanation: 'The voltage gain for an inverting amplifier is given by Av = -Rf/Rin. So, Av = -20kΩ/5kΩ = -4.' },
      ]
    }
  }
];
