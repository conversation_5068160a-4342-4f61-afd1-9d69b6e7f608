<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phototransistor Learning Module - Biomedical Electronics</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a2a6c, #3a7bd5, #00d2ff);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        header {
            background: linear-gradient(to right, #0d47a1, #1976d2);
            color: white;
            padding: 30px;
            text-align: center;
            border-bottom: 5px solid #ffc107;
        }

        .university {
            opacity: 0.9;
            margin-top: 10px;
            font-size: 1.1rem;
        }

        .author {
            margin-top: 5px;
            font-style: italic;
            font-size: 1.2rem;
        }

        .content-wrapper {
            display: flex;
            min-height: 70vh;
        }

        .sidebar {
            width: 280px;
            background: #f5f9ff;
            padding: 30px 15px;
            border-right: 2px solid #e0e0e0;
        }

        .module-title {
            background: #1565c0;
            color: white;
            padding: 10px;
            border-radius: 8px;
            margin: 15px 0 25px;
            text-align: center;
            font-weight: 600;
            font-size: 1.3rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            padding: 12px 20px;
            margin: 8px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar li:hover {
            background: #bbdefb;
            transform: translateX(5px);
        }

        .sidebar li.active {
            background: #0d47a1;
            color: white;
            box-shadow: 0 4px 8px rgba(13, 71, 161, 0.3);
        }

        .sidebar li i {
            margin-right: 10px;
            width: 24px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            padding: 30px 40px;
            background: #fff;
            display: flex;
            flex-direction: column;
        }

        .theory-section {
            background: #e8f4ff;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.08);
        }

        .theory-section h2 {
            color: #0d47a1;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #90caf9;
        }

        .theory-section p {
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .theory-section img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            margin: 15px 0;
        }

        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .equipment-card {
            background: white;
            border-radius: 12px;
            padding: 20px 10px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .equipment-card:hover {
            transform: translateY(-5px);
            background: #e1f5fe;
        }

        .equipment-card i {
            font-size: 2.5rem;
            color: #0d47a1;
            margin-bottom: 15px;
        }

        .simulator-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .simulator-container {
            background: linear-gradient(to bottom right, #e8f4ff, #f1f8ff);
            border-radius: 15px;
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            box-shadow: inset 0 0 15px rgba(0,0,0,0.1);
        }

        #simulator {
            width: 100%;
            height: 280px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .light-source {
            position: absolute;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, #ffeb3b, #ff9800);
            border-radius: 50%;
            top: 30px;
            left: 100px;
            cursor: move;
            box-shadow: 0 0 25px #ff9800;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: all 0.4s ease;
        }

        .device-box {
            position: absolute;
            width: 180px;
            height: 120px;
            background: #f5f5f5;
            border: 2px solid #0d47a1;
            border-radius: 10px;
            bottom: 50px;
            right: 100px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .phototransistor {
            font-size: 4rem;
            color: #3f51b5;
        }

        .output-value {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            min-width: 200px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: center;
            width: 100%;
        }

        .control-group {
            background: white;
            padding: 15px;
            border-radius: 10px;
            min-width: 200px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.08);
        }

        .control-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: #0d47a1;
        }

        .slider {
            width: 100%;
            margin: 10px 0;
        }

        .value-display {
            font-weight: bold;
            color: #e91e63;
            font-size: 1.1rem;
            text-align: center;
            margin-top: 5px;
        }

        .comparison {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .comparison-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            flex: 1;
            text-align: center;
            box-shadow: 0 4px 10px rgba(0,0,0,0.08);
        }

        .comparison-box h3 {
            color: #0d47a1;
            margin-bottom: 15px;
        }

        .pros-cons {
            text-align: left;
            margin-top: 15px;
        }

        .pros {
            color: #388e3c;
        }

        .cons {
            color: #d32f2f;
        }

        footer {
            text-align: center;
            padding: 25px;
            background: #0d47a1;
            color: white;
            border-top: 2px solid #ffc107;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 2px solid #e0e0e0;
            }
            
            .comparison {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Biomedical Device Electronics: Phototransistors</h1>
            <div class="university">Sudan University of Science and Technology, College of Engineering</div>
            <div class="author">Dr. Mohammed Yagoub Esmail - Biomedical Engineering Department</div>
        </header>
        
        <div class="content-wrapper">
            <div class="sidebar">
                <div class="module-title">Learning Modules</div>
                <ul>
                    <li class="active"><i class="fas fa-book-open"></i> Overview</li>
                    <li><i class="fas fa-microscope"></i> Structure</li>
                    <li><i class="fas fa-lightbulb"></i> Working Modes</li>
                    <li><i class="fas fa-shapes"></i> Types</li>
                    <li><i class="fas fa-plus-circle"></i> Advantages</li>
                    <li><i class="fas fa-bolt"></i> Circuit Setup</li>
                    <li><i class="fas fa-tools"></i> Circuit Tips</li>
                    <li><i class="fas fa-heartbeat"></i> Medical Applications</li>
                    <li><i class="fas fa-balance-scale"></i> Pros & Cons</li>
                    <li><i class="fas fa-exchange-alt"></i> Vs Photodiode</li>
                    <li><i class="fas fa-vial"></i> Interactive Simulator</li>
                </ul>
            </div>
            
            <div class="main-content">
                <div class="theory-section">
                    <h2>Overview of Phototransistors</h2>
                    <p>A phototransistor is a light-sensitive transistor that converts light energy into electrical current. Unlike regular transistors, they are specifically designed to detect light and amplify the resulting current. Phototransistors are widely used in medical devices for applications such as pulse oximeters, blood analyzers, and optical sensors.</p>
                    
                    <div class="equipment-grid">
                        <div class="equipment-card">
                            <i class="fas fa-heartbeat"></i>
                            <h3>Pulse Oximeter</h3>
                        </div>
                        <div class="equipment-card">
                            <i class="fas fa-syringe"></i>
                            <h3>Blood Analyzer</h3>
                        </div>
                        <div class="equipment-card">
                            <i class="fas fa-procedures"></i>
                            <h3>Patient Monitor</h3>
                        </div>
                        <div class="equipment-card">
                            <i class="fas fa-laser"></i>
                            <h3>Laser Therapy</h3>
                        </div>
                    </div>
                    
                    <p>Phototransistors offer significant advantages in medical device design, providing high sensitivity to light variations while being cost-effective and simple to integrate into electronic circuits. Their ability to detect low light levels makes them ideal for non-invasive medical diagnostics.</p>
                </div>
                
                <div class="simulator-section">
                    <h2>Phototransistor Simulation</h2>
                    
                    <div class="simulator-container">
                        <div id="simulator">
                            <div class="light-source">LIGHT SOURCE</div>
                            <div class="device-box">
                                <div class="phototransistor"><i class="fas fa-sun"></i></div>
                                <h3>PHOTOTRANSISTOR</h3>
                            </div>
                            <div class="output-value">
                                <strong>Current Output:</strong> <span id="currentOutput">0 μA</span><br>
                                <strong>Light Intensity:</strong> <span id="lightIntensity">0 lx</span>
                            </div>
                        </div>
                        
                        <div class="controls">
                            <div class="control-group">
                                <label>Light Intensity</label>
                                <input type="range" min="0" max="100" value="0" class="slider" id="intensityControl">
                                <div class="value-display">0%</div>
                            </div>
                            <div class="control-group">
                                <label>Light Wavelength</label>
                                <input type="range" min="400" max="1000" value="600" class="slider" id="wavelengthControl">
                                <div class="value-display">600 nm</div>
                            </div>
                            <div class="control-group">
                                <label>Color Filter</label>
                                <select id="colorFilter">
                                    <option value="none">No filter</option>
                                    <option value="red">Red Filter</option>
                                    <option value="blue">Blue Filter</option>
                                    <option value="infrared">Infrared Filter</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="comparison">
                    <div class="comparison-box">
                        <h3><i class="fas fa-thumbs-up"></i> Advantages</h3>
                        <div class="pros-cons">
                            <p class="pros"><i class="fas fa-check-circle"></i> Higher sensitivity than photodiodes</p>
                            <p class="pros"><i class="fas fa-check-circle"></i> Built-in signal amplification</p>
                            <p class="pros"><i class="fas fa-check-circle"></i> Cost-effective solution</p>
                            <p class="pros"><i class="fas fa-check-circle"></i> Simple circuit integration</p>
                        </div>
                    </div>
                    
                    <div class="comparison-box">
                        <h3><i class="fas fa-balance-scale-right"></i> Vs Photodiodes</h3>
                        <div class="pros-cons">
                            <p><strong>Speed:</strong> Photodiodes = Fast, Phototransistors = Slower</p>
                            <p><strong>Current Output:</strong> Phototransistors have higher output</p>
                            <p><strong>Circuit Complexity:</strong> Phototransistors require fewer components</p>
                            <p><strong>Medical Applications:</strong> Phototransistors preferred for low light detection</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>Biomedical Engineering Department - © 2023 | Sudan University of Science and Technology</p>
            <p>Design Principles: Safety, Reliability, Sensitivity, Biocompatibility, and Ease of Sterilization</p>
        </footer>
    </div>

    <script>
        // Simulation logic
        const intensityControl = document.getElementById('intensityControl');
        const wavelengthControl = document.getElementById('wavelengthControl');
        const colorFilter = document.getElementById('colorFilter');
        const currentOutput = document.getElementById('currentOutput');
        const lightIntensity = document.getElementById('lightIntensity');
        const lightSource = document.querySelector('.light-source');
        const valueDisplays = document.querySelectorAll('.value-display');
        
        // Update values when sliders change
        intensityControl.addEventListener('input', updateSimulation);
        wavelengthControl.addEventListener('input', updateSimulation);
        colorFilter.addEventListener('change', updateSimulation);
        
        function updateSimulation() {
            // Update value displays
            valueDisplays[0].textContent = intensityControl.value + '%';
            valueDisplays[1].textContent = wavelengthControl.value + ' nm';
            
            // Calculate current output based on simulation parameters
            const intensity = intensityControl.value / 100;
            const wavelength = wavelengthControl.value;
            let effectiveness = 1;
            
            // Adjust effectiveness based on wavelength (optimum around 850-950nm)
            if (wavelength >= 800 && wavelength <= 950) {
                effectiveness = 1.0;
            } else if (wavelength < 700 || wavelength > 1000) {
                effectiveness = 0.2;
            } else {
                effectiveness = 0.6;
            }
            
            // Apply filter effect
            const filter = colorFilter.value;
            if (filter === 'red' && wavelength > 630) effectiveness *= 0.8;
            if (filter === 'blue' && wavelength < 490) effectiveness *= 0.8;
            if (filter === 'infrared' && wavelength < 700) effectiveness *= 0.3;
            
            // Calculate output values
            const outputCurrent = Math.round(150 * intensity * effectiveness);
            const luxValue = Math.round(5000 * intensity);
            
            // Update display
            currentOutput.textContent = outputCurrent + ' μA';
            lightIntensity.textContent = luxValue + ' lx';
            
            // Visual feedback
            lightSource.style.transform = `scale(${1 + intensity * 0.3})`;
            
            // Device response
            const deviceBox = document.querySelector('.device-box');
            if (outputCurrent > 100) {
                deviceBox.style.borderColor = '#4caf50';
                deviceBox.style.boxShadow = '0 0 15px #4caf50';
            } else if (outputCurrent > 50) {
                deviceBox.style.borderColor = '#ff9800';
                deviceBox.style.boxShadow = '0 0 10px #ff9800';
            } else {
                deviceBox.style.borderColor = '#0d47a1';
                deviceBox.style.boxShadow = 'none';
            }
        }
        
        // Section navigation logic
        const menuItems = document.querySelectorAll('.sidebar li');
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                menuItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });
        
        // Make light source draggable (simple implementation)
        let isDragging = false;
        lightSource.addEventListener('mousedown', () => {
            isDragging = true;
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const rect = document.getElementById('simulator').getBoundingClientRect();
                const x = e.clientX - rect.left - 60;
                const y = e.clientY - rect.top - 60;
                
                // Keep within bounds
                if (x > 20 && x < rect.width - 140 && y > 20 && y < rect.height - 140) {
                    lightSource.style.left = x + 'px';
                    lightSource.style.top = y + 'px';
                    
                    // Update simulation based on distance to device
                    const deviceRect = document.querySelector('.device-box').getBoundingClientRect();
                    const lightRect = lightSource.getBoundingClientRect();
                    
                    const distance = Math.sqrt(
                        Math.pow(deviceRect.left - lightRect.left, 2) +
                        Math.pow(deviceRect.top - lightRect.top, 2)
                    );
                    
                    // Max distance is about 300px
                    const maxDistance = 300;
                    const distanceEffect = Math.max(0, 1 - (distance / maxDistance));
                    
                    // Adjust intensity based on distance
                    const currentIntensity = parseInt(intensityControl.value);
                    intensityControl.value = Math.min(100, Math.max(0, currentIntensity * distanceEffect));
                    updateSimulation();
                }
            }
        });
        
        document.addEventListener('mouseup', () => {
            isDragging = false;
        });
        
        // Initialize simulation
        updateSimulation();
    </script>
</body>
</html>
