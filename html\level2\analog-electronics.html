<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analog Electronics - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/analog-electronics.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="module-title">Analog Electronics</div>
            <div class="progress-indicator">
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
                <span class="progress-text">Progress: 0%</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="analog-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Analog Electronics Mastery</h1>
                <p>Explore the continuous world of analog circuits, from basic amplifiers to complex signal processing systems used in modern biomedical devices.</p>
                <div class="hero-objectives">
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Master operational amplifiers and their applications</li>
                        <li><i class="fas fa-check"></i> Understand diodes, transistors, and semiconductor devices</li>
                        <li><i class="fas fa-check"></i> Design filters and signal conditioning circuits</li>
                        <li><i class="fas fa-check"></i> Apply analog concepts to biomedical instrumentation</li>
                        <li><i class="fas fa-check"></i> Analyze frequency response and stability</li>
                    </ul>
                </div>
            </div>
            <div class="hero-visual">
                <div class="analog-showcase">
                    <div class="waveform-display">
                        <canvas id="waveformCanvas" width="400" height="200"></canvas>
                        <div class="waveform-controls">
                            <div class="control-group">
                                <label>Frequency:</label>
                                <input type="range" id="frequencySlider" min="1" max="10" value="2" step="0.5">
                                <span id="frequencyValue">2 Hz</span>
                            </div>
                            <div class="control-group">
                                <label>Amplitude:</label>
                                <input type="range" id="amplitudeSlider" min="0.1" max="2" value="1" step="0.1">
                                <span id="amplitudeValue">1.0 V</span>
                            </div>
                            <div class="control-group">
                                <label>Wave Type:</label>
                                <select id="waveTypeSelect">
                                    <option value="sine">Sine</option>
                                    <option value="square">Square</option>
                                    <option value="triangle">Triangle</option>
                                    <option value="sawtooth">Sawtooth</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="circuit-preview">
                        <div class="op-amp-circuit">
                            <div class="op-amp-symbol">
                                <div class="op-amp-body">
                                    <div class="input-pins">
                                        <div class="pin positive">+</div>
                                        <div class="pin negative">-</div>
                                    </div>
                                    <div class="output-pin">Out</div>
                                </div>
                            </div>
                            <div class="feedback-network">
                                <div class="resistor-feedback">Rf</div>
                                <div class="resistor-input">Rin</div>
                            </div>
                            <div class="gain-display">
                                <span>Gain = -Rf/Rin</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Learning Modules -->
    <section class="learning-modules">
        <div class="container">
            <h2><i class="fas fa-wave-square"></i> Interactive Learning Modules</h2>
            <div class="modules-grid">
                <!-- Semiconductor Devices -->
                <div class="module-card" onclick="openModule('semiconductor-devices')">
                    <div class="module-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="module-content">
                        <h3>Semiconductor Devices</h3>
                        <p>Learn about diodes, transistors, and their characteristics with interactive I-V curves</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-chart-line"></i> I-V Characteristics</span>
                            <span class="feature"><i class="fas fa-thermometer-half"></i> Temperature Effects</span>
                        </div>
                    </div>
                </div>

                <!-- Operational Amplifiers -->
                <div class="module-card" onclick="openModule('operational-amplifiers')">
                    <div class="module-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div class="module-content">
                        <h3>Operational Amplifiers</h3>
                        <p>Master op-amp configurations: inverting, non-inverting, differential, and integrator circuits</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-cogs"></i> Circuit Configurations</span>
                            <span class="feature"><i class="fas fa-calculator"></i> Gain Calculations</span>
                        </div>
                    </div>
                </div>

                <!-- Filters and Frequency Response -->
                <div class="module-card" onclick="openModule('filters-frequency')">
                    <div class="module-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="module-content">
                        <h3>Filters & Frequency Response</h3>
                        <p>Design and analyze low-pass, high-pass, band-pass, and band-stop filters</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-wave-square"></i> Bode Plots</span>
                            <span class="feature"><i class="fas fa-sliders-h"></i> Interactive Design</span>
                        </div>
                    </div>
                </div>

                <!-- Signal Conditioning -->
                <div class="module-card" onclick="openModule('signal-conditioning')">
                    <div class="module-icon">
                        <i class="fas fa-signal"></i>
                    </div>
                    <div class="module-content">
                        <h3>Signal Conditioning</h3>
                        <p>Learn amplification, filtering, and conversion techniques for sensor signals</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-compress-alt"></i> Amplification</span>
                            <span class="feature"><i class="fas fa-exchange-alt"></i> A/D Conversion</span>
                        </div>
                    </div>
                </div>

                <!-- Power Electronics -->
                <div class="module-card" onclick="openModule('power-electronics')">
                    <div class="module-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="module-content">
                        <h3>Power Electronics</h3>
                        <p>Understand power supplies, voltage regulators, and switching circuits</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-battery-full"></i> Power Supplies</span>
                            <span class="feature"><i class="fas fa-tachometer-alt"></i> Regulation</span>
                        </div>
                    </div>
                </div>

                <!-- Biomedical Applications -->
                <div class="module-card" onclick="openModule('biomedical-analog')">
                    <div class="module-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="module-content">
                        <h3>Biomedical Applications</h3>
                        <p>Explore analog circuits in ECG, EEG, pulse oximetry, and other medical devices</p>
                        <div class="module-features">
                            <span class="feature"><i class="fas fa-stethoscope"></i> Medical Devices</span>
                            <span class="feature"><i class="fas fa-chart-pulse"></i> Bio-signals</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Op-Amp Configuration Simulator -->
    <section class="opamp-simulator">
        <div class="container">
            <h2><i class="fas fa-expand-arrows-alt"></i> Op-Amp Configuration Simulator</h2>
            <div class="simulator-content">
                <div class="configuration-selector">
                    <h3>Select Configuration</h3>
                    <div class="config-buttons">
                        <button type="button" class="config-btn active" data-config="inverting">Inverting</button>
                        <button type="button" class="config-btn" data-config="non-inverting">Non-Inverting</button>
                        <button type="button" class="config-btn" data-config="differential">Differential</button>
                        <button type="button" class="config-btn" data-config="integrator">Integrator</button>
                        <button type="button" class="config-btn" data-config="differentiator">Differentiator</button>
                    </div>
                </div>
                <div class="simulator-display">
                    <div class="circuit-diagram">
                        <canvas id="opampCanvas" width="600" height="400"></canvas>
                    </div>
                    <div class="parameters-panel">
                        <h4>Circuit Parameters</h4>
                        <div class="parameter-controls">
                            <div class="control-group">
                                <label for="inputVoltage">Input Voltage (V):</label>
                                <input type="number" id="inputVoltage" value="1" step="0.1" min="-10" max="10">
                            </div>
                            <div class="control-group">
                                <label for="resistorR1">R1 (kΩ):</label>
                                <input type="number" id="resistorR1" value="10" step="1" min="1" max="100">
                            </div>
                            <div class="control-group">
                                <label for="resistorR2">R2 (kΩ):</label>
                                <input type="number" id="resistorR2" value="20" step="1" min="1" max="100">
                            </div>
                        </div>
                        <div class="results-display">
                            <h5>Results</h5>
                            <div class="result-item">
                                <span class="label">Gain:</span>
                                <span class="value" id="gainResult">-2.0</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Output Voltage:</span>
                                <span class="value" id="outputVoltage">-2.0 V</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Input Impedance:</span>
                                <span class="value" id="inputImpedance">10 kΩ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Design Tool -->
    <section class="filter-designer">
        <div class="container">
            <h2><i class="fas fa-filter"></i> Interactive Filter Designer</h2>
            <div class="designer-content">
                <div class="filter-controls">
                    <h3>Filter Parameters</h3>
                    <div class="filter-type-selector">
                        <label>Filter Type:</label>
                        <select id="filterType">
                            <option value="lowpass">Low-Pass</option>
                            <option value="highpass">High-Pass</option>
                            <option value="bandpass">Band-Pass</option>
                            <option value="bandstop">Band-Stop</option>
                        </select>
                    </div>
                    <div class="filter-parameters">
                        <div class="control-group">
                            <label for="cutoffFreq">Cutoff Frequency (Hz):</label>
                            <input type="number" id="cutoffFreq" value="1000" step="100" min="10" max="10000">
                        </div>
                        <div class="control-group">
                            <label for="filterOrder">Order:</label>
                            <select id="filterOrder">
                                <option value="1">1st Order</option>
                                <option value="2" selected>2nd Order</option>
                                <option value="3">3rd Order</option>
                                <option value="4">4th Order</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label for="dampingFactor">Damping Factor:</label>
                            <input type="number" id="dampingFactor" value="0.707" step="0.1" min="0.1" max="2">
                        </div>
                    </div>
                </div>
                <div class="frequency-response">
                    <h3>Frequency Response</h3>
                    <canvas id="bodeCanvas" width="600" height="300"></canvas>
                    <div class="response-info">
                        <div class="info-item">
                            <span class="label">-3dB Frequency:</span>
                            <span class="value" id="cutoffDisplay">1000 Hz</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Roll-off Rate:</span>
                            <span class="value" id="rolloffRate">-40 dB/decade</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Solved Examples -->
    <section class="solved-examples">
        <div class="container">
            <h2><i class="fas fa-lightbulb"></i> Solved Examples</h2>
            <div class="examples-grid">
                <!-- Example 1: ECG Amplifier Design -->
                <div class="example-card">
                    <div class="example-header">
                        <h3><i class="fas fa-heartbeat"></i> Example 1: ECG Amplifier Design</h3>
                    </div>
                    <div class="example-content">
                        <div class="problem-statement">
                            <h4>Problem:</h4>
                            <p>Design a differential amplifier for ECG signal acquisition with the following specifications:</p>
                            <ul>
                                <li>Input signal: 1mV differential ECG signal</li>
                                <li>Common-mode noise: 50mV at 60Hz</li>
                                <li>Required gain: 1000 V/V</li>
                                <li>CMRR requirement: > 80dB</li>
                                <li>Input impedance: > 10MΩ</li>
                            </ul>
                        </div>
                        <div class="solution">
                            <h4>Solution:</h4>
                            <div class="solution-steps">
                                <div class="step">
                                    <h5>Step 1: Choose Op-Amp</h5>
                                    <p>Select a precision op-amp with high input impedance (e.g., AD8221)</p>
                                    <ul>
                                        <li>Input bias current: < 1nA</li>
                                        <li>CMRR: > 100dB</li>
                                        <li>Gain bandwidth product: > 1MHz</li>
                                    </ul>
                                </div>
                                <div class="step">
                                    <h5>Step 2: Calculate Resistor Values</h5>
                                    <p>For instrumentation amplifier configuration:</p>
                                    <div class="calculation">
                                        <p>Gain = 1 + (2R1/RG)</p>
                                        <p>1000 = 1 + (2 × 10kΩ/RG)</p>
                                        <p>RG = 20kΩ/999 ≈ 20Ω</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <h5>Step 3: Add High-Pass Filter</h5>
                                    <p>Remove DC offset and low-frequency noise:</p>
                                    <div class="calculation">
                                        <p>fc = 1/(2πRC) = 0.5Hz</p>
                                        <p>C = 1/(2π × 1MΩ × 0.5Hz) = 0.32μF</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Example 2: Active Filter Design -->
                <div class="example-card">
                    <div class="example-header">
                        <h3><i class="fas fa-filter"></i> Example 2: Active Low-Pass Filter</h3>
                    </div>
                    <div class="example-content">
                        <div class="problem-statement">
                            <h4>Problem:</h4>
                            <p>Design a 2nd-order Sallen-Key low-pass filter with:</p>
                            <ul>
                                <li>Cutoff frequency: 1kHz</li>
                                <li>Butterworth response (Q = 0.707)</li>
                                <li>Unity gain</li>
                                <li>Standard resistor values</li>
                            </ul>
                        </div>
                        <div class="solution">
                            <h4>Solution:</h4>
                            <div class="solution-steps">
                                <div class="step">
                                    <h5>Step 1: Choose Component Values</h5>
                                    <p>For equal component Sallen-Key topology:</p>
                                    <div class="calculation">
                                        <p>R1 = R2 = R</p>
                                        <p>C1 = C2 = C</p>
                                        <p>fc = 1/(2πRC)</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <h5>Step 2: Calculate Values</h5>
                                    <div class="calculation">
                                        <p>Choose C = 100nF (standard value)</p>
                                        <p>R = 1/(2π × 1000Hz × 100nF)</p>
                                        <p>R = 1.59kΩ ≈ 1.6kΩ (standard value)</p>
                                    </div>
                                </div>
                                <div class="step">
                                    <h5>Step 3: Verify Response</h5>
                                    <div class="calculation">
                                        <p>Actual fc = 1/(2π × 1.6kΩ × 100nF) = 995Hz</p>
                                        <p>Error = (1000-995)/1000 × 100% = 0.5% ✓</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../js/analog-electronics.js"></script>
</body>
</html>
