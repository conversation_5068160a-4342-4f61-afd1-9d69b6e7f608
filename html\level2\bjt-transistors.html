<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدورة التعليمية: الترانزستور ثنائي القطب (BJT)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #2c3e50;
            --secondary: #3498db;
            --accent: #e74c3c;
            --light: #ecf0f1;
            --dark: #2c3e50;
            --success: #2ecc71;
            --warning: #f39c12;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a3a, #2c3e50);
            color: var(--light);
            line-height: 1.6;
            padding-bottom: 50px;
            position: relative;
            overflow-x: hidden;
        }
        
        body::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: 
                radial-gradient(circle at 10% 20%, rgba(52, 152, 219, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(231, 76, 60, 0.1) 0%, transparent 20%);
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: rgba(44, 62, 80, 0.9);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            border-bottom: 2px solid var(--secondary);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logo i {
            font-size: 2.5rem;
            color: var(--secondary);
        }
        
        .logo h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            gap: 25px;
        }
        
        .nav-links a {
            text-decoration: none;
            color: var(--light);
            font-weight: 600;
            font-size: 1.1rem;
            padding: 8px 12px;
            border-radius: 5px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .nav-links a:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 3px;
            background: var(--secondary);
            transition: width 0.3s ease;
        }
        
        .nav-links a:hover {
            color: var(--secondary);
        }
        
        .nav-links a:hover:after {
            width: 100%;
        }
        
        .burger {
            display: none;
            cursor: pointer;
        }
        
        .hero {
            padding: 100px 0 60px;
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .hero h2 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: var(--light);
            text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
        }
        
        .hero p {
            font-size: 1.4rem;
            max-width: 800px;
            margin: 0 auto 30px;
            color: #bdc3c7;
        }
        
        .author {
            background: rgba(44, 62, 80, 0.8);
            display: inline-block;
            padding: 12px 25px;
            border-radius: 50px;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .author strong {
            color: var(--secondary);
        }
        
        .transistor-animation {
            margin: 50px auto;
            width: 200px;
            height: 200px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .transistor-body {
            width: 100px;
            height: 130px;
            background: linear-gradient(45deg, #34495e, #2c3e50);
            border-radius: 10px;
            position: relative;
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.4);
            overflow: hidden;
        }
        
        .transistor-terminals {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 25px;
        }
        
        .terminal {
            width: 8px;
            height: 40px;
            background: #95a5a6;
            position: relative;
            border-radius: 4px 4px 0 0;
            box-shadow: 0 -5px 10px rgba(149, 165, 166, 0.6);
            margin-top: -1px;
        }
        
        .terminal.base {
            height: 20px;
            top: -20px;
        }
        
        .section {
            background: rgba(44, 62, 80, 0.8);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(52, 152, 219, 0.4);
            transition: transform 0.3s ease;
        }
        
        .section:hover {
            transform: translateY(-5px);
        }
        
        .section-title {
            font-size: 2.2rem;
            margin-bottom: 25px;
            color: var(--secondary);
            display: flex;
            align-items: center;
            gap: 15px;
            border-bottom: 2px solid var(--secondary);
            padding-bottom: 12px;
        }
        
        .section-title i {
            font-size: 2rem;
        }
        
        .columns {
            display: flex;
            gap: 30px;
            margin: 40px 0;
        }
        
        .column {
            flex: 1;
            background: rgba(30, 40, 50, 0.6);
            padding: 25px;
            border-radius: 12px;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
        
        .column h3 {
            color: var(--success);
            margin-bottom: 20px;
            font-size: 1.6rem;
        }
        
        .systems-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .system-card {
            background: rgba(30, 40, 50, 0.6);
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
        
        .system-card:hover {
            transform: scale(1.03);
            box-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
            border-color: var(--secondary);
        }
        
        .system-card i {
            font-size: 3rem;
            color: var(--secondary);
            margin-bottom: 20px;
        }
        
        .system-card h4 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--light);
        }
        
        .diagram-container {
            position: relative;
            margin: 40px auto;
            max-width: 800px;
            background: rgba(30, 40, 50, 0.8);
            padding: 30px;
            border-radius: 15px;
            min-height: 400px;
            border: 1px solid rgba(52, 152, 219, 0.4);
        }
        
        .bjt-diagram {
            position: relative;
            height: 300px;
        }
        
        .layer {
            position: absolute;
            width: 100px;
            height: 300px;
            background: rgba(231, 76, 60, 0.6);
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            text-align: center;
        }
        
        .base {
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: rgba(46, 204, 113, 0.6);
            width: 60px;
            height: 200px;
            z-index: 2;
        }
        
        .emitter {
            left: 30%;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(52, 152, 219, 0.6);
            height: 250px;
        }
        
        .collector {
            right: 30%;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(155, 89, 182, 0.6);
            height: 250px;
        }
        
        .terminal-label {
            position: absolute;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .terminal-dot {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #f1c40f;
            border-radius: 50%;
            box-shadow: 0 0 10px #f1c40f;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        
        .comparison-table th {
            background: var(--secondary);
            padding: 15px;
            text-align: center;
            font-size: 1.2rem;
        }
        
        .comparison-table td {
            padding: 15px;
            text-align: center;
            background: rgba(30, 40, 50, 0.6);
            border: 1px solid rgba(52, 152, 219, 0.4);
        }
        
        .comparison-table tr:nth-child(even) td {
            background: rgba(40, 50, 65, 0.6);
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: var(--secondary);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            margin: 20px 10px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-size: 1.1rem;
        }
        
        .btn:hover {
            background: transparent;
            border-color: var(--secondary);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--secondary);
        }
        
        .btn-outline:hover {
            background: var(--secondary);
        }
        
        footer {
            background: rgba(0, 0, 0, 0.3);
            padding: 30px 0;
            margin-top: 50px;
            border-top: 2px solid var(--secondary);
            text-align: center;
        }
        
        .university-info {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-top: 25px;
        }
        
        .footer-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-link:hover {
            color: var(--secondary);
        }
        
        @media (max-width: 900px) {
            .columns {
                flex-direction: column;
            }
            
            .hero h2 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.1rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .burger {
                display: block;
                color: white;
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav>
                <div class="logo">
                    <i class="fas fa-microchip"></i>
                    <h1>الترانزستور ثنائي القطب (BJT)</h1>
                </div>
                <div class="nav-links">
                    <a href="#intro">المقدمة</a>
                    <a href="#structure">البنية</a>
                    <a href="#types">الأنواع</a>
                    <a href="#working">طريقة العمل</a>
                    <a href="#uses">الاستخدامات</a>
                </div>
                <div class="burger">
                    <i class="fas fa-bars"></i>
                </div>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h2>الدورة التعليمية: الترانزستور ثنائي القطب (BJT)</h2>
            <p>فهم الأساسيات، أنواع، واستخدامات الترانزستور المكون الأساسي في الإلكترونيات الحديثة</p>
            
            <div class="author">
                إعداد: <strong>د. محمد يعقوب إسماعيل</strong> | قسم الهندسة الطبية الحيوية
            </div>
            
            <div class="transistor-animation">
                <div class="transistor-body">
                    <div class="transistor-terminals">
                        <div class="terminal collector"></div>
                        <div class="terminal base"></div>
                        <div class="terminal emitter"></div>
                    </div>
                </div>
            </div>
            
            <a href="#intro" class="btn">إبدأ التعلم <i class="fas fa-arrow-down"></i></a>
        </div>
    </section>

    <div class="container">
        <section id="intro" class="section">
            <h2 class="section-title"><i class="fas fa-info-circle"></i> المقدمة</h2>
            <p>الترانزستور ثنائي القطب (Bipolar Junction Transistor - BJT) هو أحد أهم المكونات الإلكترونية في العصر الحديث، حيث يعمل كمفتاح إلكتروني أو مضخم للإشارات. اخترعه علماء مختبرات بيل عام 1947 وحدث ثورة في عالم الإلكترونيات.</p>
            
            <div class="columns">
                <div class="column">
                    <h3>دور الترانزستور في الهندسة الطبية</h3>
                    <p>يدخل الـ BJT في تصنيع العديد من الأجهزة الطبية الحيوية مثل:</p>
                    <ul>
                        <li>أجهزة مراقبة القلب</li>
                        <li>منظمات ضربات القلب</li>
                        <li>أجهزة السمع</li>
                        <li>أجهزة التصوير الطبي</li>
                        <li>أنظمة العلاج بالصوت والموجات</li>
                    </ul>
                </div>
                <div class="column">
                    <h3>المزايا الرئيسية</h3>
                    <ul>
                        <li>تضخيم الإشارات الكهربائية الضعيفة</li>
                        <li>سرعة استجابة عالية</li>
                        <li>كسب تيار عالي (β)</li>
                        <li>متانة وأداء مستقر</li>
                        <li>تكلفة تصنيع منخفضة</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="structure" class="section">
            <h2 class="section-title"><i class="fas fa-sitemap"></i> البنية الأساسية</h2>
            <p>يتكون الترانزستور ثنائي القطب من ثلاث طبقات شبه موصلة مرتبة بطرق مختلفة وفقاً للنوع:</p>
            
            <div class="diagram-container">
                <div class="bjt-diagram">
                    <div class="layer emitter" style="left: 25%;">الباعث (Emitter)<br>يُشحن حاملات الشحنة</div>
                    <div class="layer base" style="z-index: 10;">القاعدة (Base)<br>طبقة رقيقة (nm) تتحكم في التدفق</div>
                    <div class="layer collector" style="right: 25%;">المجمع (Collector)<br>يجمع الحاملات بعد عبور القاعدة</div>
                    <div style="position:absolute; top: 20px; left: 45%;" class="terminal-label">سماكة القاعدة &lt;&lt; الباعث والمجمع</div>
                </div>
            </div>
            
            <div class="columns">
                <div class="column">
                    <h3>خصائص المواد شبه الموصلة</h3>
                    <p>تُصنع الترانزستورات ثنائية القطب من مواد شبه موصلة مثل السيليكون (الأكثر شيوعاً) أو الجرمانيوم. يتم "تشويب" هذه المواد بنوعين من الذرات:</p>
                    <ul>
                        <li>نوع-P: به فجوات موجبة الشحنة</li>
                        <li>نوع-N: به إلكترونات حرة سالبة الشحنة</li>
                    </ul>
                </div>
                <div class="column">
                    <h3>مبدأ عمل الطبقات</h3>
                    <p>تعمل الطبقات الثلاث معاً على:</p>
                    <ul>
                        <li>الباعث: يبعث حاملات الشحنة عند تأينه</li>
                        <li>القاعدة: تتحكم في تدفق الحاملات من الباعث إلى المجمع</li>
                        <li>المجمع: يجمع غالبية حاملات الشحنة</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="types" class="section">
            <h2 class="section-title"><i class="fas fa-project-diagram"></i> نوعا الـ BJT الرئيسيان</h2>
            
            <div class="columns">
                <div class="column">
                    <h3><i class="fas fa-arrow-right" style="color: #e74c3c;"></i> ترانزستور NPN</h3>
                    <ul>
                        <li>تركيبه: طبقة N ثم P ثم N</li>
                        <li>اتجاه التيار: من المجمع إلى الباعث</li>
                        <li>حركة الإلكترونات: من الباعث إلى المجمع عبر القاعدة</li>
                        <li>الاستقطاب: موجب على المجمع بالنسبة للباعث</li>
                        <li>رمز كهربائي: سهم يشير للخارج</li>
                    </ul>
                </div>
                <div class="column">
                    <h3><i class="fas fa-arrow-left" style="color: #2ecc71;"></i> ترانزستور PNP</h3>
                    <ul>
                        <li>تركيبه: طبقة P ثم N ثم P</li>
                        <li>اتجاه التيار: من الباعث إلى المجمع</li>
                        <li>حركة الفجوات: من الباعث إلى المجمع عبر القاعدة</li>
                        <li>الاستقطاب: سالب على المجمع بالنسبة للباعث</li>
                        <li>رمز كهربائي: سهم يشير للداخل</li>
                    </ul>
                </div>
            </div>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>المعيار</th>
                        <th>NPN</th>
                        <th>PNP</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>نقل الشحنة</td>
                        <td>إلكترونات</td>
                        <td>فجوات</td>
                    </tr>
                    <tr>
                        <td>السرعة النسبية</td>
                        <td>أسرع</td>
                        <td>أبطأ قليلاً</td>
                    </tr>
                    <tr>
                        <td>مقاومة الدخل</td>
                        <td>منخفضة</td>
                        <td>أعلى</td>
                    </tr>
                    <tr>
                        <td>التكلفة النسبية</td>
                        <td>أقل</td>
                        <td>أعلى</td>
                    </tr>
                    <tr>
                        <td>الاستخدام الأكثر شيوعاً</td>
                        <td>تطبيقات التضخيم والتبديل</td>
                        <td>دوائر الدفع والسحب (Push-Pull)</td>
                    </tr>
                </tbody>
            </table>
            
            <p style="text-align: center; margin-top: 20px;">
                <a href="#" class="btn">تجربة محاكاة المصنع بــ Tinkercad <i class="fas fa-external-link-alt"></i></a>
            </p>
        </section>

        <section id="working" class="section">
            <h2 class="section-title"><i class="fas fa-cogs"></i> مبدأ العمل</h2>
            
            <div class="columns">
                <div class="column">
                    <h3>أوضاع التشغيل</h3>
                    <p>يعمل الترانزستور في أربعة أوضاع رئيسية حسب جهد الاستقطاب:</p>
                    <ol>
                        <li><strong>وضع القطع (Cutoff):</strong> عازل، لا يمرر تيار</li>
                        <li><strong>وضع التشبع (Saturation):</strong> موصل بالكامل</li>
                        <li><strong>وضع التضخيم النشط (Active):</strong> يستخدم لتضخيم الإشارات</li>
                        <li><strong>المنطقة العكسية (Reverse)</strong></li>
                    </ol>
                    
                    <h3>معامل التضخيم β</h3>
                    <p>يُعبر عنه بالمعادلة: β = I<sub>C</sub> / I<sub>B</sub></p>
                    <p>حيث I<sub>C</sub>: تيار المجمع، I<sub>B</sub>: تيار القاعدة</p>
                    <p>القيم النموذجية: 50 إلى 200 للترانزستورات الشائعة</p>
                </div>
                <div class="column">
                    <h3>إشارة التضخيم</h3>
                    <p>عند دخول إشارة كهربائية صغيرة إلى القاعدة:</p>
                    <ul>
                        <li>التغير الصغير في تيار القاعدة (I<sub>B</sub>)</li>
                        <li>يسبب تغير كبير في تيار المجمع (I<sub>C</sub>)</li>
                        <li>وفق المعادلة: I<sub>C</sub> = β × I<sub>B</sub></li>
                        <li>تحصل على تضخيم للإشارة الأصلية بمعامل β</li>
                    </ul>
                    
                    <div style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 10px; margin-top: 20px;">
                        <h4 style="color: #f1c40f;"><i class="fas fa-lightbulb"></i> مثال تطبيقي</h4>
                        <p>إشارة قادمة من حساس طبي ضعيفة (0.1mA)</p>
                        <p>باستخدام ترانزستور بمعامل تضخيم β=150</p>
                        <p>نحصل على إشارة مكبرة: 0.1 × 150 = 15mA</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="uses" class="section">
            <h2 class="section-title"><i class="fas fa-microchip"></i> التطبيقات والأستخدامات</h2>
            
            <div class="systems-container">
                <div class="system-card">
                    <i class="fas fa-heartbeat"></i>
                    <h4>الأجهزة الطبية</h4>
                    <p>منظمات ضربات القلب، أجهزة السمع، مراقبة العلامات الحيوية</p>
                </div>
                
                <div class="system-card">
                    <i class="fas fa-volume-up"></i>
                    <h4>أنظمة التضخيم الصوتي</h4>
                    <p>مكبرات الصوت، مكبرات الإشارة، معالجات الإشارات الصوتية</p>
                </div>
                
                <div class="system-card">
                    <i class="fas fa-microchip"></i>
                    <h4>الدوائر الإلكترونية</h4>
                    <p>المنطق الرقمي، الذواكر السريعة، الأنظمة المتكاملة</p>
                </div>
                
                <div class="system-card">
                    <i class="fas fa-car"></i>
                    <h4>أنظمة التحكم</h4>
                    <p>أنظمة السيارات، أنظمة الطيران، أتمتة المصانع</p>
                </div>
                
                <div class="system-card">
                    <i class="fas fa-plug"></i>
                    <h4>مصادر الطاقة</h4>
                    <p>منظمات الجهد، الدوائر المستقرة، محولات الطاقة</p>
                </div>
                
                <div class="system-card">
                    <i class="fas fa-bolt"></i>
                    <h4>المفاتيح الإلكترونية</h4>
                    <p>التحكم في المحركات، الأنظمة الإلكترونية للطاقة</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="#" class="btn"><i class="fas fa-download"></i> تحميل كتيب البرنامج</a>
                <a href="#" class="btn btn-outline"><i class="fas fa-video"></i> مشاهدة فيديوهات تعليمية</a>
            </div>
        </section>
    </div>

    <footer>
        <div class="container">
            <div class="university-info">
                <p>جامعة السودان للعلوم والتكنولوجيا - كلية الهندسة - قسم الهندسة الطبية الحيوية</p>
            </div>
            
            <p>© 2023 | جميع الحقوق محفوظة</p>
            
            <div class="footer-links">
                <a href="#" class="footer-link"><i class="fab fa-edrawmax"></i> Edraw Software</a>
                <a href="#" class="footer-link"><i class="fab fa-tinkercad"></i> Tinkercad Circuits</a>
                <a href="#" class="footer-link"><i class="fas fa-book"></i> المرجع العلمي</a>
            </div>
        </div>
    </footer>
</body>
</html>
