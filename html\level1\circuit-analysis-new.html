<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Circuit Analysis - Virtual Electronics Lab</title>
    <link rel="stylesheet" href="../../css/circuit-analysis.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="module-nav">
        <div class="nav-container">
            <a href="../../index.html" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Home
            </a>
            <div class="module-title">Circuit Analysis</div>
            <div class="progress-indicator">
                <div class="progress-bar">
                    <div class="progress-fill" id="moduleProgress"></div>
                </div>
                <span class="progress-text">Progress: 0%</span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="circuit-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Circuit Analysis Fundamentals</h1>
                <p>Master the essential techniques for analyzing electrical circuits with step-by-step solved examples and interactive demonstrations.</p>
                <div class="hero-objectives">
                    <h3>Learning Objectives</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Apply Kirchhoff's Laws to complex circuits</li>
                        <li><i class="fas fa-check"></i> Analyze series and parallel combinations</li>
                        <li><i class="fas fa-check"></i> Use voltage and current divider rules</li>
                        <li><i class="fas fa-check"></i> Solve practical biomedical circuit problems</li>
                        <li><i class="fas fa-check"></i> Apply nodal and mesh analysis techniques</li>
                    </ul>
                </div>
            </div>
            <div class="hero-visual">
                <div class="circuit-demo">
                    <div class="demo-circuit">
                        <div class="voltage-source">12V</div>
                        <div class="resistor r1">R1<br>2kΩ</div>
                        <div class="resistor r2">R2<br>3kΩ</div>
                        <div class="resistor r3">R3<br>6kΩ</div>
                        <div class="current-flow" id="currentFlow"></div>
                        <div class="voltage-labels">
                            <span class="v1">V1 = ?</span>
                            <span class="v2">V2 = ?</span>
                            <span class="v3">V3 = ?</span>
                        </div>
                    </div>
                    <div class="analysis-steps">
                        <h4>Analysis Steps:</h4>
                        <ol>
                            <li>Identify circuit topology</li>
                            <li>Apply Kirchhoff's Laws</li>
                            <li>Calculate equivalent resistance</li>
                            <li>Find currents and voltages</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Slide Presentation -->
    <section class="slide-presentation">
        <div class="container">
            <div class="presentation-header">
                <h2><i class="fas fa-chalkboard-teacher"></i> Interactive Learning Modules</h2>
                <div class="slide-controls">
                    <button type="button" class="slide-btn" id="prevSlide" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <div class="slide-counter">1 / 10</div>
                    <button type="button" class="slide-btn" id="nextSlide">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <div class="slides-container">
                <!-- Slide 1: Kirchhoff's Current Law (KCL) -->
                <div class="slide active" data-slide="1">
                    <div class="slide-content">
                        <h3><i class="fas fa-project-diagram"></i> Kirchhoff's Current Law (KCL)</h3>
                        <div class="slide-layout">
                            <div class="slide-text">
                                <p>Kirchhoff's Current Law states that the algebraic sum of currents entering and leaving any node in a circuit equals zero.</p>
                                <div class="law-statement">
                                    <h4>Mathematical Expression:</h4>
                                    <div class="formula">∑ I_in = ∑ I_out</div>
                                    <p>Or equivalently: ∑ I = 0 (considering direction)</p>
                                </div>
                                <div class="medical-application">
                                    <h4>Biomedical Application:</h4>
                                    <p>In ECG amplifier circuits, KCL ensures proper current distribution through differential amplifier stages, maintaining signal integrity and common-mode rejection.</p>
                                </div>
                            </div>
                            <div class="slide-visual">
                                <div class="kcl-demo">
                                    <h4>Interactive KCL Example</h4>
                                    <div class="node-circuit">
                                        <div class="node-point">Node A</div>
                                        <div class="current-arrow i1">I₁ = 5A →</div>
                                        <div class="current-arrow i2">I₂ = 3A →</div>
                                        <div class="current-arrow i3">← I₃ = ?</div>
                                        <div class="current-arrow i4">← I₄ = 2A</div>
                                    </div>
                                    <div class="calculation">
                                        <h5>Solution:</h5>
                                        <p>I₁ + I₂ = I₃ + I₄</p>
                                        <p>5A + 3A = I₃ + 2A</p>
                                        <p><strong>I₃ = 6A</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: Practical Example -->
                <div class="slide" data-slide="2">
                    <div class="slide-content">
                        <h3><i class="fas fa-heartbeat"></i> Practical Example: Pacemaker Circuit</h3>
                        <div class="slide-layout">
                            <div class="slide-text">
                                <p>Let's analyze a simplified pacemaker output circuit to understand how circuit analysis applies to medical devices.</p>
                                <div class="problem-statement">
                                    <h4>Problem:</h4>
                                    <p>A pacemaker needs to deliver 2mA through a 500Ω tissue resistance. The battery provides 3V. Calculate the required series resistance for current limiting.</p>
                                </div>
                                <div class="solution-steps">
                                    <h4>Solution Steps:</h4>
                                    <ol>
                                        <li><strong>Given:</strong> I = 2mA, R_tissue = 500Ω, V_battery = 3V</li>
                                        <li><strong>Find:</strong> R_series = ?</li>
                                        <li><strong>Total Resistance:</strong> R_total = V/I = 3V/0.002A = 1500Ω</li>
                                        <li><strong>Series Resistance:</strong> R_series = R_total - R_tissue = 1500Ω - 500Ω = 1000Ω</li>
                                        <li><strong>Verification:</strong> I = 3V/(1000Ω + 500Ω) = 2mA ✓</li>
                                    </ol>
                                </div>
                            </div>
                            <div class="slide-visual">
                                <div class="pacemaker-circuit">
                                    <h4>Pacemaker Output Circuit</h4>
                                    <div class="circuit-diagram">
                                        <div class="battery">3V Battery</div>
                                        <div class="series-resistor">R_series = 1kΩ</div>
                                        <div class="tissue-resistance">Tissue = 500Ω</div>
                                        <div class="current-indicator">I = 2mA</div>
                                    </div>
                                    <div class="safety-note">
                                        <h5>Safety Consideration:</h5>
                                        <p>The series resistor ensures safe current levels for cardiac stimulation while maintaining precise control.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Calculator Section -->
    <section class="circuit-calculator">
        <div class="container">
            <h2><i class="fas fa-calculator"></i> Circuit Analysis Calculator</h2>
            <div class="calculator-tabs">
                <button type="button" class="tab-btn active" data-tab="series">Series Circuits</button>
                <button type="button" class="tab-btn" data-tab="parallel">Parallel Circuits</button>
                <button type="button" class="tab-btn" data-tab="voltage-divider">Voltage Divider</button>
                <button type="button" class="tab-btn" data-tab="current-divider">Current Divider</button>
            </div>
            
            <div class="calculator-content">
                <!-- Series Calculator -->
                <div class="calc-panel active" id="series-calc">
                    <div class="calc-inputs">
                        <h3>Series Circuit Calculator</h3>
                        <div class="input-group">
                            <label for="seriesVoltage">Supply Voltage (V):</label>
                            <input type="number" id="seriesVoltage" placeholder="Enter voltage">
                        </div>
                        <div class="resistor-inputs">
                            <h4>Resistor Values (Ω):</h4>
                            <div class="resistor-grid">
                                <input type="number" id="seriesR1" placeholder="R1">
                                <input type="number" id="seriesR2" placeholder="R2">
                                <input type="number" id="seriesR3" placeholder="R3">
                                <input type="number" id="seriesR4" placeholder="R4 (optional)">
                            </div>
                        </div>
                        <button type="button" class="calc-btn" onclick="calculateSeries()">Calculate</button>
                    </div>
                    <div class="calc-results">
                        <h3>Results</h3>
                        <div class="result-grid">
                            <div class="result-item">
                                <span class="label">Total Resistance:</span>
                                <span class="value" id="seriesRtotal">-</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Total Current:</span>
                                <span class="value" id="seriesItotal">-</span>
                            </div>
                            <div class="result-item">
                                <span class="label">Total Power:</span>
                                <span class="value" id="seriesPtotal">-</span>
                            </div>
                        </div>
                        <div class="voltage-breakdown" id="seriesVoltages">
                            <!-- Voltage breakdown will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="../../js/circuit-analysis.js"></script>
</body>
</html>
