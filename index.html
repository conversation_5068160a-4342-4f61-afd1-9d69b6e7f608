
<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Virtual Electronics Lab - Biomedical Engineering</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Enhanced Navigation Header with Mega Menu -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-microchip"></i>
                    <span>Virtual Electronics Lab</span>
                </div>
                <div class="nav-menu">
                    <div class="nav-item">
                        <a href="#home" class="nav-link">
                            <i class="fas fa-home"></i>
                            Home
                        </a>
                    </div>
                    <div class="nav-item mega-menu-item">
                        <a href="#courses" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            Courses
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-play-circle"></i> Fundamentals</h3>
                                    <ul>
                                        <li><a href="html/courses/fundamentals.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openPresentation()">Interactive Presentation</a></li>
                                        <li><a href="#" onclick="openModule('basic-concepts')">Basic Concepts</a></li>
                                        <li><a href="#" onclick="openModule('ohms-law')">Ohm's Law</a></li>
                                        <li><a href="#" onclick="openModule('passive-components')">Passive Components</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-microchip"></i> Digital Electronics</h3>
                                    <ul>
                                        <li><a href="html/courses/digital-electronics.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('logic-gates')">Logic Gates</a></li>
                                        <li><a href="#" onclick="openModule('boolean-algebra')">Boolean Algebra</a></li>
                                        <li><a href="#" onclick="openModule('combinational-circuits')">Combinational Circuits</a></li>
                                        <li><a href="#" onclick="openModule('sequential-circuits')">Sequential Circuits</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-wave-square"></i> Analog Electronics</h3>
                                    <ul>
                                        <li><a href="html/courses/analog-electronics.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('semiconductor-devices')">Semiconductor Devices</a></li>
                                        <li><a href="#" onclick="openModule('operational-amplifiers')">Op-Amps</a></li>
                                        <li><a href="#" onclick="openModule('filters-frequency')">Filters & Frequency</a></li>
                                        <li><a href="#" onclick="openModule('signal-conditioning')">Signal Conditioning</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-heartbeat"></i> Biomedical Applications</h3>
                                    <ul>
                                        <li><a href="html/courses/biomedical-applications.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('biosignals')">Bio-signal Processing</a></li>
                                        <li><a href="#" onclick="openModule('medical-devices')">Medical Devices</a></li>
                                        <li><a href="#" onclick="openModule('patient-monitoring')">Patient Monitoring</a></li>
                                        <li><a href="#" onclick="openModule('imaging-systems')">Imaging Systems</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section featured">
                                    <h3><i class="fas fa-star"></i> Featured</h3>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-presentation"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Interactive Presentation</h4>
                                            <p>Comprehensive animated slides</p>
                                            <a href="#" onclick="openPresentation()" class="featured-btn">Start Learning</a>
                                        </div>
                                    </div>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Circuit Simulator</h4>
                                            <p>Design and test circuits</p>
                                            <a href="#" onclick="openSimulator()" class="featured-btn">Open Simulator</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-item mega-menu-item">
                        <a href="#tools" class="nav-link">
                            <i class="fas fa-tools"></i>
                            Tools
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu tools-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-calculator"></i> Calculators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('ohms-calculator')">Ohm's Law Calculator</a></li>
                                        <li><a href="#" onclick="openTool('power-calculator')">Power Calculator</a></li>
                                        <li><a href="#" onclick="openTool('resistor-calculator')">Resistor Color Code</a></li>
                                        <li><a href="#" onclick="openTool('capacitor-calculator')">Capacitor Calculator</a></li>
                                        <li><a href="#" onclick="openTool('frequency-calculator')">Frequency Calculator</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-microchip"></i> Simulators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('circuit-simulator')">Circuit Simulator</a></li>
                                        <li><a href="#" onclick="openTool('logic-simulator')">Logic Gate Simulator</a></li>
                                        <li><a href="#" onclick="openTool('filter-designer')">Filter Designer</a></li>
                                        <li><a href="#" onclick="openTool('waveform-generator')">Waveform Generator</a></li>
                                        <li><a href="#" onclick="openTool('pcb-designer')">PCB Designer</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-chart-line"></i> Analysis Tools</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('bode-plotter')">Bode Plot Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('fft-analyzer')">FFT Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('noise-analyzer')">Noise Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('stability-analyzer')">Stability Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('distortion-analyzer')">Distortion Analyzer</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nav-item">
                        <a href="#simulator" class="nav-link">
                            <i class="fas fa-flask"></i>
                            Virtual Labs
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#about" class="nav-link">
                            <i class="fas fa-info-circle"></i>
                            About
                        </a>
                    </div>
                </div>
                <div class="nav-actions">
                    <button class="search-btn" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" onclick="toggleTheme()">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="hamburger" onclick="toggleMobileMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </nav>
        <!-- Search Overlay -->
        <div class="search-overlay" id="searchOverlay">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search courses, tools, or topics..." id="searchInput">
                <button class="search-close" onclick="toggleSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="searchResults">
                <!-- Search results will be populated here -->
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Interactive Virtual Electronics Lab</h1>
                <h2>For Biomedical Engineering Students</h2>
                <p>Master electronics fundamentals through interactive simulations, animated presentations, and hands-on virtual experiments. Experience cutting-edge learning with comprehensive slide presentations featuring animated tools, circuit diagrams, and interactive elements designed specifically for biomedical engineering applications.</p>
                <div class="hero-buttons">
                    <a href="#courses" class="btn btn-primary">Start Learning</a>
                    <a href="#simulator" class="btn btn-secondary">Try Simulator</a>
                </div>
            </div>
            <div class="hero-animation">
                <div class="circuit-board">
                    <div class="component resistor"></div>
                    <div class="component capacitor"></div>
                    <div class="component ic"></div>
                    <div class="wire wire-1"></div>
                    <div class="wire wire-2"></div>
                    <div class="wire wire-3"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Learning Path Section -->
    <section id="courses" class="learning-path">
        <div class="container">
            <h2 class="section-title">
                <i class="fas fa-graduation-cap"></i>
                Progressive Learning Path
            </h2>
            <p class="section-subtitle">Master electronics from fundamentals to advanced biomedical applications</p>

            <div class="learning-levels">
                <!-- Level 1: Fundamentals -->
                <div class="level-card" data-level="1">
                    <div class="level-header">
                        <div class="level-number">01</div>
                        <h3>Electronics Fundamentals</h3>
                        <span class="level-badge beginner">Beginner</span>
                    </div>
                    <div class="level-content">
                        <p>Build a solid foundation in basic electronics concepts and circuit analysis.</p>
                        <div class="topics-grid">
                            <div class="topic-item" onclick="openFundamentals()">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Complete Course</span>
                            </div>
                            <div class="topic-item presentation-item" onclick="openPresentation()">
                                <i class="fas fa-presentation"></i>
                                <span>Interactive Presentation</span>
                            </div>
                            <div class="topic-item interactive-topic" onclick="openModule('basic-concepts')">
                                <i class="fas fa-bolt"></i>
                                <span>Basic Concepts</span>
                                <div class="topic-badge">Interactive</div>
                            </div>
                            <div class="topic-item interactive-topic" onclick="openModule('ohms-law')">
                                <i class="fas fa-calculator"></i>
                                <span>Ohm's Law</span>
                                <div class="topic-badge">Interactive</div>
                            </div>
                            <div class="topic-item interactive-topic" onclick="openModule('passive-components')">
                                <i class="fas fa-cog"></i>
                                <span>Passive Components</span>
                                <div class="topic-badge">Interactive</div>
                            </div>
                            <div class="topic-item" onclick="openModule('solved-examples')">
                                <i class="fas fa-book-open"></i>
                                <span>Solved Examples</span>
                            </div>
                        </div>
                        <div class="level-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                </div>

                <!-- Level 2: Semiconductor Devices -->
                <div class="level-card" data-level="2">
                    <div class="level-header">
                        <div class="level-number">02</div>
                        <h3>Semiconductor Devices</h3>
                        <span class="level-badge intermediate">Intermediate</span>
                    </div>
                    <div class="level-content">
                        <p>Explore diodes, transistors, and operational amplifiers with interactive simulations.</p>
                        <div class="topics-grid">
                            <div class="topic-item" onclick="openModule('diodes')">
                                <i class="fas fa-arrow-right"></i>
                                <span>Diodes & Applications</span>
                            </div>
                            <div class="topic-item" onclick="openModule('bjt')">
                                <i class="fas fa-microchip"></i>
                                <span>BJT Transistors</span>
                            </div>
                            <div class="topic-item" onclick="openModule('fet')">
                                <i class="fas fa-memory"></i>
                                <span>FET Transistors</span>
                            </div>
                            <div class="topic-item" onclick="openModule('opamps')">
                                <i class="fas fa-wave-square"></i>
                                <span>Op-Amps</span>
                            </div>
                        </div>
                        <div class="level-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                </div>

                <!-- Level 3: Medical Electronics -->
                <div class="level-card" data-level="3">
                    <div class="level-header">
                        <div class="level-number">03</div>
                        <h3>Medical Electronics</h3>
                        <span class="level-badge advanced">Advanced</span>
                    </div>
                    <div class="level-content">
                        <p>Apply electronics knowledge to biomedical devices and signal processing.</p>
                        <div class="topics-grid">
                            <div class="topic-item" onclick="openModule('biosignals')">
                                <i class="fas fa-heartbeat"></i>
                                <span>Biosignal Processing</span>
                            </div>
                            <div class="topic-item" onclick="openModule('medical-devices')">
                                <i class="fas fa-stethoscope"></i>
                                <span>Medical Device Circuits</span>
                            </div>
                            <div class="topic-item" onclick="openModule('safety')">
                                <i class="fas fa-shield-alt"></i>
                                <span>Safety & Standards</span>
                            </div>
                            <div class="topic-item" onclick="openModule('sensors')">
                                <i class="fas fa-thermometer-half"></i>
                                <span>Medical Sensors</span>
                            </div>
                        </div>
                        <div class="level-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                </div>

                <!-- Level 4: Advanced Applications -->
                <div class="level-card" data-level="4">
                    <div class="level-header">
                        <div class="level-number">04</div>
                        <h3>Advanced Applications</h3>
                        <span class="level-badge expert">Expert</span>
                    </div>
                    <div class="level-content">
                        <p>Master digital electronics and microcontroller applications in healthcare.</p>
                        <div class="topics-grid">
                            <div class="topic-item" onclick="openModule('digital-electronics')">
                                <i class="fas fa-binary"></i>
                                <span>Digital Electronics</span>
                            </div>
                            <div class="topic-item" onclick="openModule('microcontrollers')">
                                <i class="fas fa-cpu"></i>
                                <span>Microcontrollers</span>
                            </div>
                            <div class="topic-item" onclick="openModule('advanced-circuits')">
                                <i class="fas fa-sitemap"></i>
                                <span>Advanced Circuits</span>
                            </div>
                            <div class="topic-item" onclick="openModule('project-design')">
                                <i class="fas fa-drafting-compass"></i>
                                <span>Project Design</span>
                            </div>
                        </div>
                        <div class="level-progress">
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <span class="progress-text">0% Complete</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Virtual Simulator Section -->
    <section id="simulator" class="simulator-section">
        <div class="container">
            <h2 class="section-title">
                <i class="fas fa-flask"></i>
                Virtual Circuit Simulator
            </h2>
            <p class="section-subtitle">Practice with interactive circuit simulations</p>

            <div class="simulator-preview">
                <div class="simulator-screen">
                    <div class="simulator-toolbar">
                        <button class="tool-btn active"><i class="fas fa-mouse-pointer"></i></button>
                        <button class="tool-btn"><i class="fas fa-plus"></i></button>
                        <button class="tool-btn"><i class="fas fa-trash"></i></button>
                        <button class="tool-btn"><i class="fas fa-play"></i></button>
                        <button class="tool-btn"><i class="fas fa-stop"></i></button>
                    </div>
                    <div class="simulator-canvas">
                        <div class="demo-circuit">
                            <div class="demo-component battery"></div>
                            <div class="demo-component resistor"></div>
                            <div class="demo-component led"></div>
                            <div class="demo-wire"></div>
                        </div>
                    </div>
                </div>
                <div class="simulator-info">
                    <h3>Interactive Learning Features</h3>
                    <ul>
                        <li><i class="fas fa-check"></i> Drag & drop components</li>
                        <li><i class="fas fa-check"></i> Real-time simulation</li>
                        <li><i class="fas fa-check"></i> Measurement tools</li>
                        <li><i class="fas fa-check"></i> Step-by-step tutorials</li>
                        <li><i class="fas fa-check"></i> Save & share circuits</li>
                    </ul>
                    <a href="simulator.html" class="btn btn-primary">Launch Simulator</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Presentations Section -->
    <section id="presentations" class="presentations-showcase">
        <div class="container">
            <h2 class="section-title"><i class="fas fa-presentation"></i> Interactive Slide Presentations</h2>
            <p class="section-subtitle">Experience learning like never before with our animated, interactive presentations</p>
            <div class="presentations-grid">
                <div class="presentation-card">
                    <div class="presentation-preview">
                        <div class="preview-screen">
                            <div class="animated-circuit">
                                <div class="circuit-element battery">
                                    <span>V</span>
                                    <div class="voltage-indicator"></div>
                                </div>
                                <div class="circuit-wire">
                                    <div class="current-flow"></div>
                                </div>
                                <div class="circuit-element resistor">
                                    <span>R</span>
                                    <div class="heat-indicator"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="presentation-info">
                        <h3>Animated Circuit Diagrams</h3>
                        <p>Watch electrons flow, see voltage changes, and observe component behavior in real-time animations.</p>
                        <div class="presentation-features">
                            <span class="feature-tag"><i class="fas fa-bolt"></i> Live Animations</span>
                            <span class="feature-tag"><i class="fas fa-mouse-pointer"></i> Interactive Elements</span>
                        </div>
                    </div>
                </div>

                <div class="presentation-card">
                    <div class="presentation-preview">
                        <div class="preview-screen">
                            <div class="interactive-calculator">
                                <div class="calc-display">V = I × R</div>
                                <div class="calc-inputs">
                                    <div class="input-field">
                                        <label>V:</label>
                                        <input type="number" value="12" readonly>
                                    </div>
                                    <div class="input-field">
                                        <label>I:</label>
                                        <input type="number" value="2" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="presentation-info">
                        <h3>Interactive Calculators</h3>
                        <p>Built-in calculators and tools that respond to your inputs with instant visual feedback and results.</p>
                        <div class="presentation-features">
                            <span class="feature-tag"><i class="fas fa-calculator"></i> Real-time Calc</span>
                            <span class="feature-tag"><i class="fas fa-chart-line"></i> Visual Results</span>
                        </div>
                    </div>
                </div>

                <div class="presentation-card">
                    <div class="presentation-preview">
                        <div class="preview-screen">
                            <div class="medical-device">
                                <div class="device-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="signal-wave">
                                    <svg width="100" height="40" viewBox="0 0 100 40">
                                        <path d="M0,20 L20,20 L25,10 L30,30 L35,5 L40,20 L100,20"
                                              stroke="#4facfe" stroke-width="2" fill="none">
                                            <animate attributeName="stroke-dasharray"
                                                     values="0,200;100,100;200,0"
                                                     dur="2s"
                                                     repeatCount="indefinite"/>
                                        </path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="presentation-info">
                        <h3>Medical Device Simulations</h3>
                        <p>Explore real biomedical applications with animated ECG signals, pacemaker circuits, and medical sensors.</p>
                        <div class="presentation-features">
                            <span class="feature-tag"><i class="fas fa-heartbeat"></i> Medical Focus</span>
                            <span class="feature-tag"><i class="fas fa-play"></i> Live Demos</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="presentation-stats">
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Interactive Slides</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">25+</div>
                    <div class="stat-label">Animated Tools</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">Circuit Diagrams</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">Medical Examples</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2 class="section-title">Why Choose Our Virtual Lab?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <i class="fas fa-laptop-code"></i>
                    <h3>Interactive Learning</h3>
                    <p>Hands-on experience with virtual components and real-time feedback</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>Progressive Curriculum</h3>
                    <p>Structured learning path from basics to advanced biomedical applications</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-users"></i>
                    <h3>Collaborative</h3>
                    <p>Share projects and learn together with fellow students</p>
                </div>
                <div class="feature-card">
                    <i class="fas fa-mobile-alt"></i>
                    <h3>Accessible</h3>
                    <p>Learn anywhere, anytime on any device with internet connection</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Virtual Electronics Lab</h3>
                    <p>Empowering biomedical engineering students with interactive electronics education.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#courses">Courses</a></li>
                        <li><a href="#simulator">Simulator</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Virtual Electronics Lab. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
