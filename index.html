<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElectroLearn - Electronic Engineering Courses</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <script type="importmap">
    {
      "imports": {
        "react": "https://esm.sh/react@^19.1.0",
        "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
        "react/": "https://esm.sh/react@^19.1.0/"
      }
    }
    </script>
    <style>
      body {
        margin: 0; /* Basic reset */
        /* Tailwind's bg-slate-100 will be applied by App.tsx, but a fallback can be nice */
        background-color: #f1f5f9; 
        font-family: 'Roboto', sans-serif; /* Default font */
      }
      /* Additional global styles can be added here if necessary */
    </style>
</head>
<body>
    <div id="root"></div>
    <script type="module" src="./index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
