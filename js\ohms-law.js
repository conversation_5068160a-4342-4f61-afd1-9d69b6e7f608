// Ohm's Law Module JavaScript

// Global variables
let currentSlide = 1;
const totalSlides = 8;
let moduleProgress = 0;

// Initialize the module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeOhmsLawModule();
});

function initializeOhmsLawModule() {
    console.log('Initializing Ohm\'s Law Module...');
    
    // Initialize slide navigation
    initializeSlideNavigation();
    
    // Initialize interactive elements
    initializeTriangleDemo();
    initializeVoltageSimulator();
    initializeCalculator();
    initializeQuiz();
    
    // Update progress
    updateProgress();
    
    console.log('Ohm\'s Law Module initialized successfully');
}

// Slide Navigation
function initializeSlideNavigation() {
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => navigateSlide(-1));
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => navigateSlide(1));
    }
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') navigateSlide(-1);
        if (e.key === 'ArrowRight') navigateSlide(1);
    });
    
    updateSlideDisplay();
}

function navigateSlide(direction) {
    const newSlide = currentSlide + direction;
    
    if (newSlide >= 1 && newSlide <= totalSlides) {
        // Hide current slide
        const currentSlideEl = document.querySelector(`[data-slide="${currentSlide}"]`);
        if (currentSlideEl) {
            currentSlideEl.classList.remove('active');
        }
        
        // Update slide number
        currentSlide = newSlide;
        
        // Show new slide
        const newSlideEl = document.querySelector(`[data-slide="${currentSlide}"]`);
        if (newSlideEl) {
            newSlideEl.classList.add('active');
        }
        
        updateSlideDisplay();
        updateProgress();
        
        // Trigger slide-specific animations
        triggerSlideAnimations(currentSlide);
    }
}

function updateSlideDisplay() {
    const counter = document.querySelector('.slide-counter');
    const prevBtn = document.getElementById('prevSlide');
    const nextBtn = document.getElementById('nextSlide');
    
    if (counter) {
        counter.textContent = `${currentSlide} / ${totalSlides}`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentSlide === 1;
    }
    
    if (nextBtn) {
        nextBtn.disabled = currentSlide === totalSlides;
    }
}

function triggerSlideAnimations(slideNumber) {
    // Add specific animations for each slide
    switch(slideNumber) {
        case 1:
            animateScientistCard();
            break;
        case 2:
            animateFormulaCards();
            break;
        case 3:
            animateVoltageDemo();
            break;
        default:
            break;
    }
}

// Triangle Demo Interactions
function initializeTriangleDemo() {
    // Make triangle sections clickable
    const triangleSections = document.querySelectorAll('.triangle-section');
    triangleSections.forEach(section => {
        section.addEventListener('click', function() {
            const variable = this.querySelector('span').textContent;
            updateFormulaDisplay(variable);
        });
    });
}

function selectVariable(variable) {
    const formulaDisplay = document.getElementById('selectedFormula');
    if (!formulaDisplay) return;
    
    let formula = '';
    switch(variable) {
        case 'V':
            formula = 'V = I × R';
            break;
        case 'I':
            formula = 'I = V / R';
            break;
        case 'R':
            formula = 'R = V / I';
            break;
        default:
            formula = 'V = I × R';
    }
    
    formulaDisplay.textContent = formula;
    
    // Add visual feedback
    formulaDisplay.style.transform = 'scale(1.1)';
    setTimeout(() => {
        formulaDisplay.style.transform = 'scale(1)';
    }, 200);
}

function updateFormulaDisplay(variable) {
    const formulaDisplay = document.getElementById('formulaDisplay');
    if (!formulaDisplay) return;
    
    let formula = '';
    switch(variable) {
        case 'V':
            formula = 'V = I × R';
            break;
        case 'I':
            formula = 'I = V / R';
            break;
        case 'R':
            formula = 'R = V / I';
            break;
        default:
            formula = 'V = I × R';
    }
    
    formulaDisplay.textContent = formula;
}

// Voltage Simulator
function initializeVoltageSimulator() {
    const voltageSlider = document.getElementById('voltageSlider');
    if (!voltageSlider) return;
    
    voltageSlider.addEventListener('input', function() {
        const voltage = parseFloat(this.value);
        const resistance = 1000; // 1kΩ
        const current = voltage / resistance * 1000; // Convert to mA
        
        updateVoltageDisplay(voltage, current);
    });
}

function updateVoltageDisplay(voltage, current) {
    const voltageLabel = document.getElementById('voltageLabel');
    const currentDisplay = document.getElementById('currentDisplay');
    const calcDisplay = document.getElementById('calcDisplay');
    
    if (voltageLabel) voltageLabel.textContent = `${voltage}V`;
    if (currentDisplay) currentDisplay.textContent = `${current.toFixed(1)} mA`;
    if (calcDisplay) calcDisplay.textContent = `${voltage}V / 1kΩ = ${current.toFixed(1)}mA`;
    
    // Animate current flow
    animateCurrentFlow(current);
}

function animateCurrentFlow(current) {
    const currentFlow = document.getElementById('currentFlow');
    if (!currentFlow) return;
    
    // Adjust animation speed based on current
    const speed = Math.max(0.5, current / 20);
    currentFlow.style.animationDuration = `${2 / speed}s`;
}

// Calculator Functions
function initializeCalculator() {
    const inputs = ['calcVoltage', 'calcCurrent', 'calcResistance'];
    
    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', calculateOhmsLaw);
        }
    });
}

function calculateOhmsLaw() {
    const voltage = parseFloat(document.getElementById('calcVoltage')?.value) || 0;
    const current = parseFloat(document.getElementById('calcCurrent')?.value) || 0;
    const resistance = parseFloat(document.getElementById('calcResistance')?.value) || 0;
    
    // Calculate missing values
    let calculatedV = voltage;
    let calculatedI = current;
    let calculatedR = resistance;
    let calculatedP = 0;
    
    // Determine which values to calculate based on what's provided
    const hasV = voltage > 0;
    const hasI = current > 0;
    const hasR = resistance > 0;
    
    if (hasI && hasR && !hasV) {
        calculatedV = current * resistance;
    } else if (hasV && hasR && !hasI) {
        calculatedI = voltage / resistance;
    } else if (hasV && hasI && !hasR) {
        calculatedR = voltage / current;
    }
    
    // Calculate power
    if (calculatedV > 0 && calculatedI > 0) {
        calculatedP = calculatedV * calculatedI;
    }
    
    // Update display
    updateCalculatorResults(calculatedV, calculatedI, calculatedR, calculatedP);
}

function updateCalculatorResults(voltage, current, resistance, power) {
    const voltageResult = document.getElementById('voltageResult');
    const currentResult = document.getElementById('currentResult');
    const resistanceResult = document.getElementById('resistanceResult');
    const powerResult = document.getElementById('powerResult');
    
    if (voltageResult) {
        voltageResult.textContent = voltage > 0 ? `${voltage.toFixed(2)} V` : '-';
    }
    if (currentResult) {
        currentResult.textContent = current > 0 ? `${current.toFixed(3)} A` : '-';
    }
    if (resistanceResult) {
        resistanceResult.textContent = resistance > 0 ? `${resistance.toFixed(2)} Ω` : '-';
    }
    if (powerResult) {
        powerResult.textContent = power > 0 ? `${power.toFixed(3)} W` : '-';
    }
}

// Quiz System
function initializeQuiz() {
    const quizContainer = document.getElementById('quizContainer');
    if (!quizContainer) return;
    
    const quizQuestions = [
        {
            question: "What is the formula for Ohm's Law?",
            options: ["V = I × R", "V = I / R", "V = R / I", "I = V × R"],
            correct: 0,
            explanation: "Ohm's Law states that voltage equals current times resistance: V = I × R"
        },
        {
            question: "If a circuit has 12V and 2A of current, what is the resistance?",
            options: ["6Ω", "24Ω", "10Ω", "14Ω"],
            correct: 0,
            explanation: "Using R = V / I: R = 12V / 2A = 6Ω"
        },
        {
            question: "What happens to current if voltage increases and resistance stays the same?",
            options: ["Current increases", "Current decreases", "Current stays the same", "Current becomes zero"],
            correct: 0,
            explanation: "According to Ohm's Law (I = V / R), if voltage increases and resistance stays constant, current will increase proportionally."
        },
        {
            question: "A medical device operates at 5V and draws 100mA. What is its power consumption?",
            options: ["0.5W", "50W", "500mW", "5W"],
            correct: 2,
            explanation: "Using P = V × I: P = 5V × 0.1A = 0.5W = 500mW"
        },
        {
            question: "Which material has the lowest electrical resistance?",
            options: ["Rubber", "Copper", "Carbon", "Glass"],
            correct: 1,
            explanation: "Copper is an excellent conductor with very low electrical resistance, commonly used in electrical wiring."
        },
        {
            question: "In an ECG machine, why is high input impedance important?",
            options: ["To increase power consumption", "To minimize signal loading", "To reduce cost", "To increase noise"],
            correct: 1,
            explanation: "High input impedance minimizes current draw from the patient, preventing signal loading and ensuring accurate measurements."
        }
    ];
    
    displayQuiz(quizQuestions);
}

function displayQuiz(questions) {
    const quizContainer = document.getElementById('quizContainer');
    if (!quizContainer) return;
    
    let quizHTML = '<div class="quiz-questions">';
    
    questions.forEach((q, index) => {
        quizHTML += `
            <div class="quiz-question" data-question="${index}">
                <h4>Question ${index + 1}: ${q.question}</h4>
                <div class="quiz-options">
                    ${q.options.map((option, optIndex) => `
                        <label class="quiz-option">
                            <input type="radio" name="q${index}" value="${optIndex}">
                            <span>${option}</span>
                        </label>
                    `).join('')}
                </div>
                <div class="quiz-explanation" style="display: none;">
                    <p><strong>Explanation:</strong> ${q.explanation}</p>
                </div>
            </div>
        `;
    });
    
    quizHTML += `
        </div>
        <button class="quiz-submit-btn" onclick="submitQuiz()">Submit Quiz</button>
        <div class="quiz-results" style="display: none;"></div>
    `;
    
    quizContainer.innerHTML = quizHTML;
}

function submitQuiz() {
    const quizQuestions = [
        { correct: 0 }, { correct: 0 }, { correct: 0 },
        { correct: 2 }, { correct: 1 }, { correct: 1 }
    ];

    let score = 0;
    let totalQuestions = quizQuestions.length;
    let results = [];

    // Check each answer
    quizQuestions.forEach((q, index) => {
        const selectedAnswer = document.querySelector(`input[name="q${index}"]:checked`);
        const isCorrect = selectedAnswer && parseInt(selectedAnswer.value) === q.correct;

        if (isCorrect) {
            score++;
        }

        results.push({
            question: index + 1,
            correct: isCorrect,
            selectedAnswer: selectedAnswer ? parseInt(selectedAnswer.value) : null
        });

        // Show explanations
        const explanation = document.querySelector(`[data-question="${index}"] .quiz-explanation`);
        if (explanation) {
            explanation.style.display = 'block';
            explanation.style.background = isCorrect ? '#d1fae5' : '#fee2e2';
            explanation.style.borderLeft = `4px solid ${isCorrect ? '#10b981' : '#ef4444'}`;
        }
    });

    // Calculate percentage
    const percentage = Math.round((score / totalQuestions) * 100);

    // Show results
    displayQuizResults(score, totalQuestions, percentage);

    // Update progress if passed
    if (percentage >= 80) {
        updateProgress();
        showNotification('Congratulations! You passed the quiz!', 'success');
    } else {
        showNotification('You need at least 80% to pass. Try again!', 'warning');
    }

    // Disable submit button
    const submitBtn = document.querySelector('.quiz-submit-btn');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = 'Quiz Completed';
    }
}

function displayQuizResults(score, total, percentage) {
    const resultsContainer = document.getElementById('quizResults');
    if (!resultsContainer) return;

    const passed = percentage >= 80;
    const resultHTML = `
        <div class="quiz-score ${passed ? 'passed' : 'failed'}">
            <h3>Quiz Results</h3>
            <div class="score-display">
                <span class="score">${score}/${total}</span>
                <span class="percentage">${percentage}%</span>
            </div>
            <div class="result-message">
                ${passed ?
                    '<i class="fas fa-check-circle"></i> Excellent! You have mastered Ohm\'s Law!' :
                    '<i class="fas fa-times-circle"></i> Keep studying and try again!'
                }
            </div>
            ${!passed ? '<button type="button" class="retry-btn" onclick="retryQuiz()">Retry Quiz</button>' : ''}
        </div>
    `;

    resultsContainer.innerHTML = resultHTML;
    resultsContainer.style.display = 'block';
}

function retryQuiz() {
    // Reset all radio buttons
    const radioButtons = document.querySelectorAll('input[type="radio"]');
    radioButtons.forEach(radio => radio.checked = false);

    // Hide all explanations
    const explanations = document.querySelectorAll('.quiz-explanation');
    explanations.forEach(exp => exp.style.display = 'none');

    // Hide results
    const resultsContainer = document.getElementById('quizResults');
    if (resultsContainer) {
        resultsContainer.style.display = 'none';
    }

    // Re-enable submit button
    const submitBtn = document.querySelector('.quiz-submit-btn');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Quiz';
    }
}

// Progress Tracking
function updateProgress() {
    const progressSteps = {
        1: 10,  // Introduction
        2: 20,  // Basic Formula
        3: 40,  // Understanding Voltage
        4: 60,  // Understanding Current
        5: 80,  // Understanding Resistance
        6: 90,  // Power and Ohm's Law
        7: 95,  // Applications
        8: 100  // Quiz completed
    };
    
    moduleProgress = progressSteps[currentSlide] || 0;
    
    const progressFill = document.getElementById('moduleProgress');
    const progressText = document.querySelector('.progress-text');
    
    if (progressFill) {
        progressFill.style.width = `${moduleProgress}%`;
    }
    
    if (progressText) {
        progressText.textContent = `Progress: ${moduleProgress}%`;
    }
}

// Animation Functions
function animateScientistCard() {
    const card = document.querySelector('.scientist-card');
    if (card) {
        card.style.transform = 'scale(0.8)';
        card.style.opacity = '0';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.transform = 'scale(1)';
            card.style.opacity = '1';
        }, 100);
    }
}

function animateFormulaCards() {
    const cards = document.querySelectorAll('.formula-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                card.style.transform = 'translateY(0)';
            }, 200);
        }, index * 200);
    });
}

function animateVoltageDemo() {
    const demo = document.querySelector('.voltage-simulator');
    if (demo) {
        demo.style.opacity = '0';
        setTimeout(() => {
            demo.style.transition = 'opacity 0.5s ease';
            demo.style.opacity = '1';
        }, 100);
    }
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: 'bold',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });
    
    // Set background color based on type
    const colors = {
        info: '#3b82f6',
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Toggle answer visibility for practice problems
function toggleAnswer(problemNumber) {
    const answer = document.getElementById(`answer${problemNumber}`);
    const button = answer.previousElementSibling;

    if (answer.classList.contains('hidden')) {
        answer.classList.remove('hidden');
        button.textContent = 'Hide Answer';
        answer.style.animation = 'slideIn 0.3s ease-out';
    } else {
        answer.classList.add('hidden');
        button.textContent = 'Show Answer';
    }
}

// Export functions for global access
window.selectVariable = selectVariable;
window.submitQuiz = submitQuiz;
window.toggleAnswer = toggleAnswer;
window.retryQuiz = retryQuiz;
