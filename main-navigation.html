<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Virtual Electronics Lab - Biomedical Engineering</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Enhanced Navigation Header with Dynamic Features -->
    <header class="main-header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <i class="fas fa-microchip"></i>
                    <span>Virtual Electronics Lab</span>
                </div>
                
                <div class="nav-menu">
                    <div class="nav-item">
                        <a href="#home" class="nav-link">
                            <i class="fas fa-home"></i>
                            Home
                        </a>
                    </div>
                    
                    <div class="nav-item mega-menu-item">
                        <a href="#courses" class="nav-link">
                            <i class="fas fa-graduation-cap"></i>
                            Courses
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-play-circle"></i> Fundamentals</h3>
                                    <ul>
                                        <li><a href="html/courses/fundamentals.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openPresentation()">Interactive Presentation</a></li>
                                        <li><a href="#" onclick="openModule('basic-concepts')">Basic Concepts</a></li>
                                        <li><a href="#" onclick="openModule('ohms-law')">Ohm's Law</a></li>
                                        <li><a href="#" onclick="openModule('passive-components')">Passive Components</a></li>
                                        <li><a href="#" onclick="openModule('solved-examples')">Solved Examples</a></li>
                                    </ul>
                                </div>
                                
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-microchip"></i> Semiconductor Devices</h3>
                                    <ul>
                                        <li><a href="html/courses/analog-electronics.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('diodes')">Diodes & Applications</a></li>
                                        <li><a href="#" onclick="openModule('bjt')">BJT Transistors</a></li>
                                        <li><a href="#" onclick="openModule('fet')">FET Transistors</a></li>
                                        <li><a href="#" onclick="openModule('opamps')">Operational Amplifiers</a></li>
                                    </ul>
                                </div>
                                
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-heartbeat"></i> Medical Electronics</h3>
                                    <ul>
                                        <li><a href="html/courses/biomedical-applications.html">Complete Course</a></li>
                                        <li><a href="#" onclick="openModule('biosignals')">Bio-signal Processing</a></li>
                                        <li><a href="#" onclick="openModule('medical-devices')">Medical Device Circuits</a></li>
                                        <li><a href="#" onclick="openModule('safety')">Safety & Standards</a></li>
                                        <li><a href="#" onclick="openModule('sensors')">Medical Sensors</a></li>
                                    </ul>
                                </div>
                                
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-cpu"></i> Advanced Applications</h3>
                                    <ul>
                                        <li><a href="html/courses/digital-electronics.html">Digital Electronics Course</a></li>
                                        <li><a href="#" onclick="openModule('digital-electronics')">Digital Circuits</a></li>
                                        <li><a href="#" onclick="openModule('microcontrollers')">Microcontrollers</a></li>
                                        <li><a href="#" onclick="openModule('advanced-circuits')">Advanced Design</a></li>
                                        <li><a href="#" onclick="openModule('project-design')">Project Design</a></li>
                                    </ul>
                                </div>
                                
                                <div class="mega-menu-section featured">
                                    <h3><i class="fas fa-star"></i> Featured</h3>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-presentation"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Interactive Presentations</h4>
                                            <p>Animated slides with circuit diagrams</p>
                                            <a href="#" onclick="openPresentation()" class="featured-btn">Start Learning</a>
                                        </div>
                                    </div>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Virtual Lab Tools</h4>
                                            <p>Circuit simulator & design tools</p>
                                            <a href="#" onclick="openSimulator()" class="featured-btn">Open Lab</a>
                                        </div>
                                    </div>
                                    <div class="featured-item">
                                        <div class="featured-icon">
                                            <i class="fas fa-book-open"></i>
                                        </div>
                                        <div class="featured-content">
                                            <h4>Solved Examples</h4>
                                            <p>Step-by-step problem solutions</p>
                                            <a href="#" onclick="openModule('solved-examples')" class="featured-btn">View Examples</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="nav-item mega-menu-item">
                        <a href="#tools" class="nav-link">
                            <i class="fas fa-tools"></i>
                            Tools
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </a>
                        <div class="mega-menu tools-menu">
                            <div class="mega-menu-content">
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-calculator"></i> Calculators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('ohms-calculator')">Ohm's Law Calculator</a></li>
                                        <li><a href="#" onclick="openTool('power-calculator')">Power Calculator</a></li>
                                        <li><a href="#" onclick="openTool('resistor-calculator')">Resistor Color Code</a></li>
                                        <li><a href="#" onclick="openTool('capacitor-calculator')">Capacitor Calculator</a></li>
                                        <li><a href="#" onclick="openTool('frequency-calculator')">Frequency Calculator</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-microchip"></i> Simulators</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('circuit-simulator')">Circuit Simulator</a></li>
                                        <li><a href="#" onclick="openTool('logic-simulator')">Logic Gate Simulator</a></li>
                                        <li><a href="#" onclick="openTool('filter-designer')">Filter Designer</a></li>
                                        <li><a href="#" onclick="openTool('waveform-generator')">Waveform Generator</a></li>
                                        <li><a href="#" onclick="openTool('pcb-designer')">PCB Designer</a></li>
                                    </ul>
                                </div>
                                <div class="mega-menu-section">
                                    <h3><i class="fas fa-chart-line"></i> Analysis Tools</h3>
                                    <ul>
                                        <li><a href="#" onclick="openTool('bode-plotter')">Bode Plot Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('fft-analyzer')">FFT Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('noise-analyzer')">Noise Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('stability-analyzer')">Stability Analyzer</a></li>
                                        <li><a href="#" onclick="openTool('distortion-analyzer')">Distortion Analyzer</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="nav-item">
                        <a href="#simulator" class="nav-link">
                            <i class="fas fa-flask"></i>
                            Virtual Labs
                        </a>
                    </div>
                    
                    <div class="nav-item">
                        <a href="#about" class="nav-link">
                            <i class="fas fa-info-circle"></i>
                            About
                        </a>
                    </div>
                </div>
                
                <div class="nav-actions">
                    <button class="search-btn" onclick="toggleSearch()" data-tooltip="Search">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" onclick="toggleTheme()" data-tooltip="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="hamburger" onclick="toggleMobileMenu()">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Search Overlay -->
        <div class="search-overlay" id="searchOverlay">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search courses, tools, or topics..." id="searchInput">
                <button class="search-close" onclick="toggleSearch()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-results" id="searchResults">
                <!-- Search results will be populated here -->
            </div>
        </div>
    </header>

    <!-- Dynamic Content Area -->
    <main id="main-content">
        <!-- Content will be loaded dynamically here -->
    </main>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/navigation-template.js"></script>
    
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            // Load default content
            loadHomePage();
            
            // Initialize navigation
            if (window.navManager) {
                console.log('Navigation Manager initialized');
            }
        });
        
        function loadHomePage() {
            // Load the home page content dynamically
            const content = `
                <!-- Hero Section -->
                <section id="home" class="hero">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h1>Interactive Virtual Electronics Lab</h1>
                            <h2>For Biomedical Engineering Students</h2>
                            <p>Master electronics fundamentals through interactive simulations, animated presentations, and hands-on virtual experiments. Experience cutting-edge learning with comprehensive slide presentations featuring animated tools, circuit diagrams, and interactive elements designed specifically for biomedical engineering applications.</p>
                            <div class="hero-buttons">
                                <a href="#courses" class="btn btn-primary">Start Learning</a>
                                <a href="#simulator" class="btn btn-secondary">Try Simulator</a>
                            </div>
                        </div>
                        <div class="hero-animation">
                            <div class="circuit-board">
                                <div class="component resistor"></div>
                                <div class="component capacitor"></div>
                                <div class="component ic"></div>
                                <div class="wire wire-1"></div>
                                <div class="wire wire-2"></div>
                                <div class="wire wire-3"></div>
                            </div>
                        </div>
                    </div>
                </section>
            `;
            
            document.getElementById('main-content').innerHTML = content;
        }
    </script>
</body>
</html>
