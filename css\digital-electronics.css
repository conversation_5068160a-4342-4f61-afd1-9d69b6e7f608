/* Digital Electronics Module Styles */

/* Import modern color variables */
:root {
    /* Primary Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --dark-gradient: linear-gradient(135deg, #232526 0%, #414345 100%);
    
    /* Digital-specific colors */
    --digital-blue: #00d4ff;
    --digital-green: #00ff88;
    --digital-red: #ff4757;
    --digital-purple: #a55eea;
    --digital-orange: #ffa726;
    
    /* Logic states */
    --logic-high: #00ff88;
    --logic-low: #ff4757;
    --logic-unknown: #ffa726;
    
    /* Text Colors */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #a0aec0;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --bg-dark: #1a202c;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-dark);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.module-nav {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 32, 44, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    max-width: 100%;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-white);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-2px);
}

.module-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-white);
}

.progress-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    width: 200px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--accent-gradient);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-text {
    color: var(--text-white);
    font-weight: 600;
    font-size: 0.9rem;
}

/* Hero Section */
.digital-hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    position: relative;
    overflow: hidden;
}

.digital-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-text p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-objectives {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-objectives h3 {
    color: white;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.hero-objectives ul {
    list-style: none;
    padding: 0;
}

.hero-objectives li {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.hero-objectives li i {
    color: var(--digital-green);
    font-size: 1rem;
}

/* Digital Showcase */
.digital-showcase {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    align-items: center;
}

.binary-animation {
    background: rgba(0, 0, 0, 0.3);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.binary-stream {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.bit {
    width: 40px;
    height: 40px;
    background: var(--digital-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    font-weight: bold;
    font-size: 1.2rem;
    animation: bitPulse 2s ease-in-out infinite;
}

.bit:nth-child(even) {
    animation-delay: 0.5s;
}

.conversion-display {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.binary-value, .decimal-value, .hex-value {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
}

.binary-value {
    background: rgba(0, 212, 255, 0.2);
}

.decimal-value {
    background: rgba(0, 255, 136, 0.2);
}

.hex-value {
    background: rgba(165, 94, 234, 0.2);
}

/* Logic Gates Preview */
.logic-gates-preview {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.gate-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.gate-symbol {
    width: 80px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9rem;
}

.gate-inputs {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.input, .gate-output {
    width: 30px;
    height: 20px;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    color: white;
}

.input.high, .gate-output.high {
    background: var(--logic-high);
}

.input.low, .gate-output.low {
    background: var(--logic-low);
}

/* Learning Modules */
.learning-modules {
    padding: 80px 0;
    background: var(--bg-dark);
}

.learning-modules h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.learning-modules h2 i {
    color: var(--digital-blue);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.module-card:hover::before {
    left: 100%;
}

.module-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.module-icon {
    width: 80px;
    height: 80px;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.module-content h3 {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.module-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.module-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.feature {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature i {
    color: var(--digital-green);
}

/* Gate Simulator */
.gate-simulator {
    padding: 80px 0;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
}

.gate-simulator h2 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.simulator-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.gate-selection {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: fit-content;
}

.gate-selection h3 {
    color: white;
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.gate-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.gate-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.gate-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.gate-btn.active {
    background: var(--accent-gradient);
    box-shadow: var(--shadow-md);
}

.simulator-display {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
}

.gate-visual {
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.gate-inputs-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.input-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.input-control label {
    color: white;
    font-weight: 600;
}

.input-toggle {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.input-toggle[data-state="0"] {
    background: var(--logic-low);
}

.input-toggle[data-state="1"] {
    background: var(--logic-high);
}

.input-toggle:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.gate-symbol-display {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gate-shape {
    width: 120px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: var(--shadow-lg);
}

.gate-output-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.output-display label {
    color: white;
    font-weight: 600;
}

.output-value {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    transition: all 0.3s ease;
}

.output-value[data-state="0"] {
    background: var(--logic-low);
}

.output-value[data-state="1"] {
    background: var(--logic-high);
}

/* Truth Table */
.truth-table {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.truth-table h4 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.truth-table table {
    width: 100%;
    border-collapse: collapse;
}

.truth-table th,
.truth-table td {
    padding: 1rem;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
}

.truth-table th {
    background: rgba(255, 255, 255, 0.1);
    font-size: 1.1rem;
}

.truth-table tr.current-row {
    background: rgba(0, 212, 255, 0.2);
}

/* Animations */
@keyframes bitPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .simulator-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .simulator-display {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .gate-visual {
        flex-direction: column;
        gap: 2rem;
    }
    
    .nav-container {
        flex-direction: column;
        gap: 1rem;
    }
}
