// Landing Page Interactive Features
// ميزات الصفحة الرئيسية التفاعلية

class LandingPageManager {
    constructor() {
        this.isScrolled = false;
        this.isDarkMode = false;
        this.isMobileMenuOpen = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupScrollEffects();
        this.setupThemeToggle();
        this.setupMobileMenu();
        this.setupAnimations();
        this.loadUserPreferences();
    }

    setupEventListeners() {
        // Scroll events
        window.addEventListener('scroll', this.handleScroll.bind(this));
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', this.handleSmoothScroll.bind(this));
        });

        // Hero scroll indicator
        const scrollIndicator = document.querySelector('.hero-scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                document.querySelector('#features').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        }

        // Intersection Observer for animations
        this.setupIntersectionObserver();

        // Keyboard navigation
        document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this));

        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const newScrolled = scrollTop > 50;

        if (newScrolled !== this.isScrolled) {
            this.isScrolled = newScrolled;
            this.updateNavigation();
        }

        // Update scroll progress
        this.updateScrollProgress();

        // Parallax effect for hero section
        this.updateParallaxEffect(scrollTop);
    }

    updateNavigation() {
        const nav = document.querySelector('.main-nav');
        if (nav) {
            nav.classList.toggle('scrolled', this.isScrolled);
            
            if (this.isScrolled) {
                nav.style.background = this.isDarkMode 
                    ? 'rgba(17, 24, 39, 0.95)' 
                    : 'rgba(255, 255, 255, 0.95)';
                nav.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            } else {
                nav.style.background = this.isDarkMode 
                    ? 'rgba(17, 24, 39, 0.8)' 
                    : 'rgba(255, 255, 255, 0.8)';
                nav.style.boxShadow = 'none';
            }
        }
    }

    updateScrollProgress() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;

        // Update any progress indicators
        const progressBars = document.querySelectorAll('.scroll-progress');
        progressBars.forEach(bar => {
            bar.style.width = `${scrollPercent}%`;
        });
    }

    updateParallaxEffect(scrollTop) {
        const heroVisual = document.querySelector('.hero-visual');
        if (heroVisual) {
            const parallaxSpeed = 0.5;
            heroVisual.style.transform = `translateY(${scrollTop * parallaxSpeed}px)`;
        }
    }

    handleSmoothScroll(e) {
        e.preventDefault();
        const targetId = e.currentTarget.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        
        if (targetElement) {
            const offsetTop = targetElement.offsetTop - 70; // Account for fixed nav
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });

            // Close mobile menu if open
            if (this.isMobileMenuOpen) {
                this.toggleMobileMenu();
            }

            // Update active nav link
            this.updateActiveNavLink(targetId);
        }
    }

    updateActiveNavLink(targetId) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`a[href="${targetId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    setupThemeToggle() {
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', this.toggleTheme.bind(this));
        }
    }

    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        document.body.classList.toggle('dark-mode', this.isDarkMode);
        
        // Update theme toggle icon
        const themeIcon = document.querySelector('.theme-toggle i');
        if (themeIcon) {
            themeIcon.className = this.isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Update navigation background
        this.updateNavigation();

        // Save preference
        localStorage.setItem('darkMode', this.isDarkMode);

        // Dispatch theme change event
        document.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { isDarkMode: this.isDarkMode }
        }));
    }

    setupMobileMenu() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');

        if (mobileToggle && navMenu) {
            mobileToggle.addEventListener('click', this.toggleMobileMenu.bind(this));

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (this.isMobileMenuOpen && 
                    !mobileToggle.contains(e.target) && 
                    !navMenu.contains(e.target)) {
                    this.toggleMobileMenu();
                }
            });
        }
    }

    toggleMobileMenu() {
        this.isMobileMenuOpen = !this.isMobileMenuOpen;
        const navMenu = document.querySelector('.nav-menu');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (navMenu) {
            navMenu.classList.toggle('mobile-open', this.isMobileMenuOpen);
        }

        if (mobileToggle) {
            mobileToggle.classList.toggle('active', this.isMobileMenuOpen);
        }

        // Prevent body scroll when menu is open
        document.body.style.overflow = this.isMobileMenuOpen ? 'hidden' : '';
    }

    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Trigger counter animations for stats
                    if (entry.target.classList.contains('stat-item')) {
                        this.animateCounter(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animatedElements = document.querySelectorAll(
            '.feature-card, .course-card, .stat-item, .section-header'
        );
        animatedElements.forEach(el => observer.observe(el));
    }

    animateCounter(statItem) {
        const numberElement = statItem.querySelector('.stat-number');
        if (!numberElement || numberElement.dataset.animated) return;

        const finalNumber = parseInt(numberElement.textContent);
        const duration = 2000;
        const increment = finalNumber / (duration / 16);
        let currentNumber = 0;

        const updateCounter = () => {
            currentNumber += increment;
            if (currentNumber >= finalNumber) {
                numberElement.textContent = finalNumber;
                numberElement.dataset.animated = 'true';
            } else {
                numberElement.textContent = Math.floor(currentNumber);
                requestAnimationFrame(updateCounter);
            }
        };

        updateCounter();
    }

    setupAnimations() {
        // Add CSS for animations
        const style = document.createElement('style');
        style.textContent = `
            .animate-in {
                animation: slideInUp 0.6s ease forwards;
            }
            
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .feature-card,
            .course-card,
            .stat-item,
            .section-header {
                opacity: 0;
                transform: translateY(30px);
                transition: all 0.6s ease;
            }
            
            .mobile-open {
                display: flex !important;
                flex-direction: column;
                position: fixed;
                top: 70px;
                left: 0;
                right: 0;
                background: var(--bg-primary);
                padding: 20px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                z-index: 999;
            }
            
            .mobile-menu-toggle.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }
            
            .mobile-menu-toggle.active span:nth-child(2) {
                opacity: 0;
            }
            
            .mobile-menu-toggle.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -6px);
            }
        `;
        document.head.appendChild(style);
    }

    handleKeyboardNavigation(e) {
        // Escape key closes mobile menu
        if (e.key === 'Escape' && this.isMobileMenuOpen) {
            this.toggleMobileMenu();
        }

        // Alt + T toggles theme
        if (e.altKey && e.key === 't') {
            e.preventDefault();
            this.toggleTheme();
        }

        // Alt + M toggles mobile menu
        if (e.altKey && e.key === 'm') {
            e.preventDefault();
            this.toggleMobileMenu();
        }
    }

    handleResize() {
        // Close mobile menu on resize to desktop
        if (window.innerWidth > 768 && this.isMobileMenuOpen) {
            this.toggleMobileMenu();
        }

        // Update any size-dependent calculations
        this.updateScrollProgress();
    }

    loadUserPreferences() {
        // Load dark mode preference
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode === 'true') {
            this.toggleTheme();
        }

        // Load any other preferences
        const savedPreferences = localStorage.getItem('userPreferences');
        if (savedPreferences) {
            try {
                const preferences = JSON.parse(savedPreferences);
                this.applyUserPreferences(preferences);
            } catch (e) {
                console.warn('Failed to load user preferences:', e);
            }
        }
    }

    applyUserPreferences(preferences) {
        // Apply any saved user preferences
        if (preferences.reducedMotion) {
            document.body.classList.add('reduced-motion');
        }
        
        if (preferences.highContrast) {
            document.body.classList.add('high-contrast');
        }
    }

    // Public methods for external use
    scrollToSection(sectionId) {
        const section = document.querySelector(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth' });
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            zIndex: '10000',
            animation: 'slideInRight 0.3s ease',
            background: type === 'success' ? '#10b981' : 
                       type === 'error' ? '#ef4444' : 
                       type === 'warning' ? '#f59e0b' : '#3b82f6'
        });

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Get current state
    getState() {
        return {
            isScrolled: this.isScrolled,
            isDarkMode: this.isDarkMode,
            isMobileMenuOpen: this.isMobileMenuOpen,
            currentLanguage: window.getCurrentLanguage ? window.getCurrentLanguage() : 'en'
        };
    }
}

// Initialize landing page manager
const landingPageManager = new LandingPageManager();

// Export for global access
window.LandingPageManager = LandingPageManager;
window.landingPage = landingPageManager;

// Listen for language changes to update animations
document.addEventListener('languageChanged', (e) => {
    // Re-animate counters with new language formatting
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach(item => {
        const numberElement = item.querySelector('.stat-number');
        if (numberElement) {
            numberElement.dataset.animated = 'false';
        }
    });
});

// Add notification animations
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(notificationStyles);

console.log('🚀 Landing page manager initialized successfully!');
