<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - Virtual Electronics Lab</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #5a6fd8;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .working { background: #d4edda; color: #155724; }
        .missing { background: #f8d7da; color: #721c24; }
        h1 { color: #333; text-align: center; }
        h2 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>🔗 Virtual Electronics Lab - Navigation Test</h1>
    
    <div class="test-section">
        <h2>Main Pages</h2>
        <a href="index.html" class="test-link">Main Landing Page</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="simulator.html" class="test-link">Circuit Simulator</a>
        <span class="status working">✅ Working</span>
    </div>

    <div class="test-section">
        <h2>Level 1: Electronics Fundamentals</h2>
        <a href="html/level1/electronics-fundamentals.html" class="test-link">Fundamentals Hub</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="html/level1/basic-concepts-new.html" class="test-link">Basic Concepts (New)</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="html/level1/basic-concepts.html" class="test-link">Basic Concepts (Old)</a>
        <span class="status working">✅ Available</span>
        <br><br>
        <a href="html/level1/ohms-law.html" class="test-link">Ohm's Law</a>
        <span class="status working">✅ Working (8 slides)</span>
        <br><br>
        <a href="html/level1/passive-components.html" class="test-link">Passive Components</a>
        <span class="status working">✅ Available</span>
        <br><br>
        <a href="html/level1/circuit-analysis.html" class="test-link">Circuit Analysis</a>
        <span class="status working">✅ Available</span>
    </div>

    <div class="test-section">
        <h2>Level 2: Semiconductor Devices</h2>
        <a href="html/level2/bjt-transistors.html" class="test-link">BJT Transistors</a>
        <span class="status working">✅ Available</span>
        <br><br>
        <a href="html/level2/phototransistors.html" class="test-link">Phototransistors</a>
        <span class="status working">✅ Available</span>
        <br><br>
        <a href="html/level2/diodes.html" class="test-link">Diodes</a>
        <span class="status missing">⚠️ To be developed</span>
        <br><br>
        <a href="html/level2/fet-transistors.html" class="test-link">FET Transistors</a>
        <span class="status missing">⚠️ To be developed</span>
    </div>

    <div class="test-section">
        <h2>Level 3: Medical Electronics</h2>
        <a href="html/level3/medical-device-circuits.html" class="test-link">Medical Device Circuits</a>
        <span class="status working">✅ Available</span>
        <br><br>
        <a href="html/level3/biosignal-processing.html" class="test-link">Biosignal Processing</a>
        <span class="status missing">⚠️ To be developed</span>
        <br><br>
        <a href="html/level3/safety-standards.html" class="test-link">Safety Standards</a>
        <span class="status missing">⚠️ To be developed</span>
        <br><br>
        <a href="html/level3/medical-sensors.html" class="test-link">Medical Sensors</a>
        <span class="status missing">⚠️ To be developed</span>
    </div>

    <div class="test-section">
        <h2>CSS Files</h2>
        <a href="css/main.css" class="test-link">Main Styles</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="css/module.css" class="test-link">Module Styles</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="css/fundamentals.css" class="test-link">Fundamentals Styles</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="css/basic-concepts.css" class="test-link">Basic Concepts Styles</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="css/simulator.css" class="test-link">Simulator Styles</a>
        <span class="status working">✅ Working</span>
    </div>

    <div class="test-section">
        <h2>JavaScript Files</h2>
        <a href="js/main.js" class="test-link">Main Scripts</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="js/module-interactions.js" class="test-link">Module Interactions</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="js/fundamentals.js" class="test-link">Fundamentals Scripts</a>
        <span class="status working">✅ Working</span>
        <br><br>
        <a href="js/basic-concepts.js" class="test-link">Basic Concepts Scripts</a>
        <span class="status working">✅ Working</span>
    </div>

    <div class="test-section">
        <h2>Quick Navigation Test</h2>
        <p>Test the main navigation flow:</p>
        <ol>
            <li><a href="index.html">Start at Main Page</a></li>
            <li>Click "Electronics Fundamentals" in Level 1</li>
            <li>Click "Start Module" for Basic Concepts</li>
            <li>Navigate through the interactive slides</li>
            <li>Complete the quiz</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>File Structure Summary</h2>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
ELECTRONIC TRAINING/
├── index.html ✅
├── simulator.html ✅
├── test-navigation.html ✅
├── README.md ✅
├── css/
│   ├── main.css ✅
│   ├── module.css ✅
│   ├── fundamentals.css ✅
│   ├── basic-concepts.css ✅
│   └── simulator.css ✅
├── js/
│   ├── main.js ✅
│   ├── module-interactions.js ✅
│   ├── fundamentals.js ✅
│   └── basic-concepts.js ✅
└── html/
    ├── level1/
    │   ├── electronics-fundamentals.html ✅
    │   ├── basic-concepts-new.html ✅ (Main)
    │   ├── basic-concepts.html ✅ (Legacy)
    │   ├── passive-components.html ✅
    │   ├── ohms-law.html ⚠️
    │   └── circuit-analysis.html ✅
    ├── level2/
    │   ├── bjt-transistors.html ✅
    │   └── phototransistors.html ✅
    └── level3/
        └── medical-device-circuits.html ✅
        </pre>
    </div>

    <script>
        // Test JavaScript functionality
        console.log('Navigation test page loaded successfully');
        
        // Check if all CSS files are loading
        const cssFiles = ['css/main.css', 'css/module.css', 'css/fundamentals.css', 'css/basic-concepts.css', 'css/simulator.css'];
        cssFiles.forEach(file => {
            fetch(file, { method: 'HEAD' })
                .then(response => {
                    console.log(`${file}: ${response.ok ? 'OK' : 'Missing'}`);
                })
                .catch(err => {
                    console.log(`${file}: Error - ${err.message}`);
                });
        });
        
        // Check if all JS files are loading
        const jsFiles = ['js/main.js', 'js/module-interactions.js', 'js/fundamentals.js', 'js/basic-concepts.js'];
        jsFiles.forEach(file => {
            fetch(file, { method: 'HEAD' })
                .then(response => {
                    console.log(`${file}: ${response.ok ? 'OK' : 'Missing'}`);
                })
                .catch(err => {
                    console.log(`${file}: Error - ${err.message}`);
                });
        });
    </script>
</body>
</html>
